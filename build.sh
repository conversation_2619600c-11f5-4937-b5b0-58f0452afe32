#!/bin/bash

# shellcheck disable=SC2034
pwd_path=$(pwd)
## sh ./xx.sh dev
#命令行获取参数 1：dev>>模式
t_profile=$1

# 拷贝node_modules的方法
node_modules_path="/web/vue/yskt"
node_modules_copy() {
    project_name=$1
    if [ ! -d "${node_modules_path}/${project_name}" ]
        then
            mkdir -p "${node_modules_path}/${project_name}"
    fi

    if [ -d "./package-lock.json" ]
        then
            echo "删除package-lock.json"
            rm package-lock.json
    fi
    if [ -d "./yarn.lock" ]
        then
            echo "删除yarn.lock"
            rm yarn.lock
    fi
    # 判断服务器中是否存在对应项目的yarn.lock，如果存在就拷贝过来
    if [ -f "${node_modules_path}/${project_name}/yarn.lock" ]
        then
            cp "${node_modules_path}/${project_name}/yarn.lock" ./ > /dev/null
            # 判断服务器中是否存在对应项目的node_modules，如果存在就拷贝过来
            if [ -d "${node_modules_path}/${project_name}/node_modules" ]
                then
                    start1=$(date +%s)
                    rsync -avz ${node_modules_path}/"${project_name}"/node_modules/ ./node_modules/  > /dev/null
                    end1=$(date +%s)
                    runtime1=$((end1-start1))
                    echo "命令执行时间：$runtime1 秒"
            fi
    fi

    yarn install
    yarn build

    if [ -d "./build" ]; then
        # 如果输出不为空，表示目录包含内容
        if [ -n "$(find "./build" -maxdepth 1 -type f)" ]; then
            echo "打包成功！"
            cp yarn.lock ${node_modules_path}/${project_name}/
            mkdir -p ./dist
            cp -Rf ./build/* ./dist/

            start2=$(date +%s)
            rsync -avz ./node_modules/ ${node_modules_path}/"${project_name}"/node_modules/ > /dev/null
            end2=$(date +%s)
            runtime2=$((end2-start2))
            echo "命令执行时间：$runtime2 秒"
        else
            echo "--------------------------------------------------------------------------"
            echo "${project_name}打包失败"
            echo "--------------------------------------------------------------------------"
            exit 1
        fi
    else
        echo "--------------------------------------------------------------------------"
        echo "${project_name}打包失败"
        echo "--------------------------------------------------------------------------"
        exit 1
    fi
}

node_modules_copy "coursetour"



##拷贝文件
rm -rf ./docker/dist
cp -Rf ./dist ./docker/

##构建容器
cd ./docker/ || exit; bash ./startup.sh

git reset --hard

echo 'ok'
