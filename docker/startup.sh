#!/bin/bash

static_dist_dir="/root/apps/fnoneportfn/www/yskt"

# 版本号相关
file_path="coursetour_version.txt"

# # 使用 if 语句检查portalfn.txt文件是否存在
if [ -f "$static_dist_dir/$file_path" ]; then
    rm "$static_dist_dir/$file_path"
    cp -Rf ./$file_path "$static_dist_dir/"
else
    cp -Rf ./$file_path "$static_dist_dir/"
fi

## 添加cdn start
isCDN=$1
if [[ $isCDN = "cdn" ]]
then
    writeCDN=$2
    yskjCDN="https://esepage.yscdn.top"
    resultCDN=${writeCDN:-"$yskjCDN"}
    src1="$resultCDN/yskt/coursetour"

    # js
    sed -i "s#href=js/#href=${src1}/js/#g" ./dist/index.html

    # static
    sed -i "s#./static/#${src1}/static/#g" ./dist/index.html

    # css
    sed -i "s#href=css/#href=${src1}/css/#g" ./dist/index.html

    # ./assets/
    sed -i "s#./assets/#${src1}/assets/#g" ./dist/index.html
fi
## 添加cdn end

static_dist_dir_name="/root/apps/fnoneportfn/www/yskt/coursetour"

if [ -f "${static_dist_dir_name}" ]; then
    rm -rf "${static_dist_dir_name}"
fi

mkdir -p "${static_dist_dir_name}"

## 移动dist部署包到部署目录
cp -Rf ./dist/* "${static_dist_dir_name}/"