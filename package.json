{"name": "ys-coursetour", "version": "0.1.0", "private": true, "description": "元素科技_在线巡课", "homepage": "./", "dependencies": {"@ant-design/icons": "^4.7.0", "@craco/craco": "6.4.2", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "ahooks": "3.0.0", "antd": "4.17.3", "antd-mobile": "^5.0.0-rc.5", "array-move": "^4.0.0", "axios": "0.24.0", "caniuse-lite": "^1.0.30001632", "cra-template": "1.1.2", "dayjs": "^1.10.7", "echarts": "^5.6.0", "echarts-for-react": "^3.0.3", "events": "^3.3.0", "flv.js": "^1.6.2", "hls.js": "1.5.17", "lodash": "^4.17.21", "mqtt": "^4.2.6", "react": "17.0.2", "react-dom": "17.0.2", "react-echarts": "^0.1.1", "react-is": "^19.1.0", "react-router-dom": "6.0.2", "react-scripts": "4.0.3", "react-sortable-hoc": "2.0.0", "simple-progress-webpack-plugin": "^2.0.0", "styled-components": "5.3.3", "uuid": "^8.3.2", "web-vitals": "^1.0.1", "webrtc-adapter": "^9.0.3"}, "scripts": {"start": "craco start --host", "build": "craco build", "test": "craco test"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.26.0", "autoprefixer": "^9.8.8", "postcss": "^7.0.39", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17"}, "overrides": {"caniuse-lite": "1.0.30001632"}, "resolutions": {"caniuse-lite": "1.0.30001632"}}