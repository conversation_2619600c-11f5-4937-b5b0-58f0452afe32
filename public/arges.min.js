function ArgesWebControlPlug(e,i){var n,o,t=this;this.plugnContainer=e.plugnContainer,this.classId=e.classId,this.errorTryTime=e.errorTryTime,this.scrollStatus=!0,this.ip=e.ip,this.port=e.port,this.cache=e.cache,this.username=e.username,this.password=e.password,this.winNum=e.winNum,this.menuLimit=e.menuLimit;var a,r,d=100,s=0,l=e.connError,c=e.connClose,w=e.connSuccess,u=e.winSelected,g=e.ptzCommand,h=e.fullScreen,f=e.showVersion,m=e.getSplitNum,p=e.videoresult,v=e.playbackresult,x=e.setPlaybackSpeed,C=e.selectPlaybackTime,S=e.playbackDownload,b=e.switchStream,y=e.switchLayout,_=e.ptzPosition,E=e.voiceIntercom,k=e.queryARTag,P=e.addARTag,R=e.delARTag,H=e.openARTagVideo,M=e.winClicked,O=!!e.captionBar,T={right:0,bottom:0},J=[];if(e.offset){var N=e.offset.trim().split(/\s+/);2===N.length&&(T.right=NaN==parseFloat(N[0])?0:parseFloat(N[0]),T.bottom=NaN==parseFloat(N[1])?0:parseFloat(N[1]))}var W="tr",L={},A={suffix:"Google Chrome",open:!0,name:"Google"},F=0;if("WebSocket"in window){const e=async()=>{(r=new WebSocket("ws://127.0.0.1:8009/")).onopen=(()=>{console.log("8009 initialization success!");var e={action:"queryall"};console.log("发送数据"),console.log(e),r.send(JSON.stringify(e))}),r.onmessage=(e=>{var i=JSON.parse(e.data);if(console.log("初始化插件信息"),console.log(i),0!==i.code)return console.log("初始化插件流程发生错误,停止执行！"),void r.close();if("queryall"===i.action){const e=i.data.tag.filter(e=>e.tag===W);if(0===e.length)return console.log("未查询到进程，正在拉起进程，请稍等！"),void d();const{html_port:n}=e[0];s(n)}}),r.onerror=function(e){console.log("链接建立失败，正在拉起进程！"),d()}},d=()=>{++F>0?l(W):(r.close(),ArgesWebControlPlug.JS_WakeUp(`ArgesWebControl${W}://`),setTimeout(()=>{e()},3e3))},s=async e=>{(a=new WebSocket(`ws://127.0.0.1:${e}/`)).onopen=function(e){console.log("connect success.");a.sendMsg({action:"getBrowser"}),A.open||a.close()},a.onmessage=function(e){var a=JSON.parse(e.data);if(console.log("接收数据"),console.log(a),"open"==a.action)"success"==a.result&&(L[a.did]=a.vid,w&&(w(a.vid),a.NeedLocate&&t.JS_Open(t.plugnContainer)));else if("getBrowser"==a.action){n=a.titleSuffix,o=a.titleSuffixType,A.suffix=n,A.name=a.browserType;var r=i||document.getElementById(t.plugnContainer),d=Number(r.style.borderLeftWidth.split("px")[0]),s=Number(r.style.borderTopWidth.split("px")[0]);T.right+=d,T.bottom+=s,t.plugnContainer&&t.JS_Open(t.plugnContainer),console.log("浏览器信息",A)}else if("getVersion"==a.action){if(f){var l=a.version?a.version:"";f(l)}}else if("getSplitNum"==a.action){if(m){var c=a.splitNum?a.splitNum:"";""!=c&&m(c)}}else if("videoresult"==a.action){if(p){var O="null"==a.gacode?null:a.gacode,J="null"==a.winNo?null:a.winNo,N=void 0===a.tag_id?0:a.tag_id,W=a.resultcode;p(W,O,J,N)}}else if("fullScreen"==a.action){if(h){var F=a.mode,I=a.status;h(F,I)}}else if("playbackresult"==a.action){if(v){O="null"==a.gacode?null:a.gacode,J="null"==a.winNo?null:a.winNo,W=a.resultcode;v(W,O,J)}}else if("setPlaybackSpeed"==a.action){if(x){O=a.gacode;var B=a.streamControlId,V=a.controlType,z=a.speed;x(O,B,V,z)}}else if("winselected"==a.action){if(u){O="null"==a.gacode?null:a.gacode,J="null"==a.winNo?null:a.winNo;u(O,J)}}else if("ptzCommand"==a.action){if(g){var U=a.operType,$=a.operContent;O="null"==a.gacode?null:a.gacode;g(U,$,O)}}else if("startplayback"==a.action){if(C){O=a.gacode;var D=a.begintime;J=a.winNo;C(O,D,J,a.vid)}}else if("playbackDownload"==a.action){if(S){O=a.gacode;var G=a.rowId,q=(D=a.begintime,a.endtime);S(O,G,D,q)}}else if("videostart"==a.action){if(u){O="null"==a.gacode?null:a.gacode;u(O)}}else if("3dlocation"==a.action)_&&_(a);else if("getTalkUrl"==a.action)E&&E(a);else if("switchStream"==a.action){if(b){const{gacode:e,winNo:i,streamType:n}=a;b(e,i,n)}}else if("switchLayout"==a.action){if(y){const{splitNum:e}=a;y(e)}}else if("queryARTag"==a.action){if(k){const{puid:e,gacode:i,idx:n,vid:o}=a;k(e,i,n,o)}}else if("addARTag"==a.action){if(P){const{puid:e,gacode:i,idx:n,vid:o,tag:t,tag_count:r}=a;P(e,i,n,o,t,r)}}else if("delARTag"==a.action){const{puid:e,gacode:i,idx:n,vid:o,tag_id:t}=a;R&&R(e,i,n,o,t)}else if("openARTagVideo"==a.action){const{puid:e,gacode:i,link_video_gacode:n,idx:o,tag_id:t}=a;H&&H(e,i,n,o,t)}else if("winclicked"==a.action){const{gacode:e,x:i,y:n,winNo:o}=a;M&&M(e,i,n,o)}},a.onclose=function(e){c&&c(),console.log("连接关闭...")},a.onerror=function(e){l&&l(W)},a.sendMsg=function(e){console.log("发送数据"),console.log(e),1===a.readyState&&a.send(JSON.stringify(e))}};e()}else console.log("您的浏览器不支持 WebSocket!");this.JS_GetVersion=function(){var e={action:"getVersion",vid:L[t.plugnContainer]};a.sendMsg(e)},this.JS_GetSplitNum=function(){var e={action:"getSplitNum",vid:L[t.plugnContainer]};a.sendMsg(e)},this.JS_SetSplitNum=function(e,i){var n={action:"setSplitNum",vid:L[t.plugnContainer],splitNum:e,wideScreen:i};a.sendMsg(n)},this.JS_SelectWindow=function(e,i){var n={action:"selectWindow",vid:L[t.plugnContainer],moudle:e,winNo:i};a.sendMsg(n)},this.JS_CloseVideo=function(e,i){var n={action:"closeVideo",vid:L[t.plugnContainer],moudle:e,winNo:i};a.sendMsg(n)},this.JS_Video=function(e,i,n,o,r,d){var s={action:"video",vid:L[t.plugnContainer],url:e||null,gacode:i,name:n||null,secondName:o||"",videoType:r||"",winNo:d};a.sendMsg(s)},this.JS_PlaybackThumb=function(e,i,n){var o={action:"playbackThumb",vid:L[t.plugnContainer],nQueryBeginTime:e,nQueryEndTime:i,vRecordInfo:n};a.sendMsg(o)},this.JS_VideoAgain=function(e,i,n,o){var r={action:"video",vid:L[t.plugnContainer],url:e||null,gacode:i,name:n||null,winNo:o};a.sendMsg(r)},this.JS_PlaybackUrl=function(e,i,n,o,r,d){var s={action:"playbackUrl",vid:L[t.plugnContainer],url:e||null,gacode:i,name:n||null,winNo:o,streamControlId:r,storageType:d};a.sendMsg(s)},this.JS_PlaybackDownloadUrl=function(e,i,n,o){var r={action:"playbackDownloadUrl",vid:L[t.plugnContainer],url:e||null,gacode:i,streamControlId:n,rowId:o};a.sendMsg(r)},this.JS_VideoCache=function(e,i,n,o){var r={action:"video",vid:L[t.plugnContainer],puid:e,idx:i,type:n,name:o||null};a.sendMsg(r)},this.JS_Talk=function(e,i,n,o,r){var d={action:"talkUrl",returnCode:o,errorMsg:n,url:e,gacode:i,winNo:r,vid:L[t.plugnContainer]};a.sendMsg(d)},this.JS_Playback=function(e,i,n,o){if(Array.isArray(e)){var r={action:"playback",vid:L[t.plugnContainer],channelInfo:e,type:i,startTime:n,endTime:o};a.sendMsg(r)}else console.log("参数异常:channelInfo")},this.JS_Open=function(){var e=this.getElementPosition(t.plugnContainer),i={action:"open",did:t.plugnContainer,title:document.title+n,titleType:o,x:e.x,y:e.y,z:e.z,w:e.w,h:e.h,margin:e.margin,ip:t.ip||null,port:t.port||null,cache:t.cache||!1,username:t.username||null,password:t.password||null,winNum:t.winNum||1,captionBar:O,menuLimit:t.menuLimit||null};a.sendMsg(i)},this.JS_SetMask=function(e){J=e||[]},this.JS_SetPosition=function(){var e=this.getElementPosition(t.plugnContainer),i={action:"position",vid:L[t.plugnContainer],x:e.x,y:e.y,z:e.z,w:e.w,h:e.h,margin:e.margin,mask:e.mask};a.sendMsg(i)},this.JS_Close=function(){Object.keys(L).forEach(function(e){var i={action:"close",vid:L[e]};a.sendMsg(i)})},this.JS_SetShow=function(){var e=this.getElementPosition(t.plugnContainer),i={action:"show",vid:L[t.plugnContainer],x:e.x,y:e.y,z:e.z,w:e.w,h:e.h,margin:e.margin,mask:e.mask};a.sendMsg(i)},this.JS_SetHide=function(){var e={action:"hide",vid:L[t.plugnContainer]};a.sendMsg(e)},this.JS_Onfocus=function(){var e={action:"onfocus",vid:L[t.plugnContainer]};a.sendMsg(e)},this.JS_Onblur=function(){var e={action:"onblur",vid:L[t.plugnContainer]};a.sendMsg(e)},this.JS_SetModel=function(e){var i=this.getElementPosition(t.plugnContainer),n={action:"setModel",vid:L[t.plugnContainer],type:e,x:i.x,y:i.y,z:i.z,w:i.w,h:i.h,margin:i.margin,mask:i.mask};a.sendMsg(n)},this.JS_OpenCaptionBar=function(){this.getElementPosition(t.plugnContainer);var e={action:"setCaptionBar",vid:L[t.plugnContainer],type:!0};a.sendMsg(e)},this.JS_CloseCaptionBar=function(){this.getElementPosition(t.plugnContainer);var e={action:"setCaptionBar",vid:L[t.plugnContainer],type:!1};a.sendMsg(e)},this.JS_OpenMatrixControl=function(){this.getElementPosition(t.plugnContainer);var e={action:"setMatrixControl",vid:L[t.plugnContainer],type:!0};a.sendMsg(e)},this.JS_CloseMatrixControl=function(){this.getElementPosition(t.plugnContainer);var e={action:"setMatrixControl",vid:L[t.plugnContainer],type:!1};a.sendMsg(e)},this.JS_SearchLabel=function(e,i,n,o,r){var d={action:"onQueryARTag",idx:n,puid:e,gacode:i,tag:o,tag_count:o.length,resultCode:r,vid:L[t.plugnContainer]};a.sendMsg(d)},this.JS_AddLabel=function(e,i,n,o,r,d){var s={action:"onAddARTag",idx:n,puid:e,gacode:i,resultCode:d,tag:o,tag_count:r,vid:L[t.plugnContainer]};a.sendMsg(s)},this.JS_DeleteLabel=function(e,i,n,o,r){var d={action:"onDelARTag",idx:n,puid:e,gacode:i,resultCode:r,tag_id:o,vid:L[t.plugnContainer]};a.sendMsg(d)},this.JS_OpenVideoTag=function(e,i,n,o,r,d,s){var l={action:"onOpenARTagVideo",vid:L[t.plugnContainer],url:e||null,gacode:i,link_video_gacode:n,name:o||null,secondName:r||"",videoType:d||"",tag_id:s};a.sendMsg(l)},this.JS_AddWaterMark=function(e,i={}){var n={action:"addWatermark",waterName:e,...i,vid:L[t.plugnContainer]};a.sendMsg(n)},this.JS_ClearWaterMark=function(){var e={action:"clearWatermark",vid:L[t.plugnContainer]};a.sendMsg(e)},this.JS_ShowSplitControl=function(e){this.getElementPosition(t.plugnContainer);var i={action:"setSplitControl",vid:L[t.plugnContainer],type:e};a.sendMsg(i)},this.getOsInfo=function(){var e=navigator.userAgent.toLowerCase(),i="Unknown",n="Unknown";return e.indexOf("win")>-1?(i="Windows",n=e.indexOf("windows nt 5.0")>-1?"Windows 2000":e.indexOf("windows nt 5.1")>-1||e.indexOf("windows nt 5.2")>-1?"Windows XP":e.indexOf("windows nt 6.0")>-1?"Windows Vista":e.indexOf("windows nt 6.1")>-1||e.indexOf("windows 7")>-1?"Windows 7":e.indexOf("windows nt 6.2")>-1||e.indexOf("windows 8")>-1?"Windows 8":e.indexOf("windows nt 6.3")>-1?"Windows 8.1":e.indexOf("windows nt 6.2")>-1||e.indexOf("windows nt 10.0")>-1?"Windows 10":"Unknown"):i=e.indexOf("iphone")>-1?"iPhone":e.indexOf("mac")>-1?"Mac":e.indexOf("x11")>-1||e.indexOf("unix")>-1||e.indexOf("sunname")>-1||e.indexOf("bsd")>-1?"Unix":e.indexOf("linux")>-1?e.indexOf("android")>-1?"Android":"Linux":"Unknown",{name:i,version:n}},this.getBrowserToolbarHeight=function(){var e=window.outerHeight,i=window.outerWidth,n=window.screenTop,o=screen.availHeight-n-e,t=window.screenLeft,a=screen.availWidth-t-i,r=0,l=this.getOsInfo(),c=window.outerWidth/window.innerWidth,w=window.devicePixelRatio/c,u=Number(w.toFixed(2));if(u>0&&u<1.1?d=100:u>1.2&&u<1.3?d=125:u>1.4&&u<1.6?d=150:u>1.6&&u<1.8?d=175:u>1.8&&u<2.1?d=200:u>2.1&&u<2.3?d=225:u>2.3&&u<2.6?d=250:u>2.8&&u<3.2?d=300:u>3.3&&u<3.8&&(d=350),"chrome"==A.name&&(n==t&&n>=0&&n<=12&&Math.abs(n-a)<5&&o<-20&&o>=-40?r=0:window.outerHeight==screen.availHeight&&window.outerWidth==screen.availWidth||n<=0&&o<=0&&t<=0&&a<=0||n==t&&t==a&&a==o&&n>=1&&n<=5?(r=window.outerHeight*w-window.innerHeight*window.devicePixelRatio+8*w,"Windows 10"==l.version&&(r=window.outerHeight*w-window.innerHeight*window.devicePixelRatio+n*w)):r=window.outerHeight*w-window.innerHeight*window.devicePixelRatio),"360"==A.name){var g=1;g=100==d?window.devicePixelRatio:window.devicePixelRatio/d*100,w.toFixed(2)>0&&w.toFixed(2)<=1?w=1:w.toFixed(2)>1&&w.toFixed(2)<=1.25?w=1.25:w.toFixed(2)>1.4&&w.toFixed(2)<=1.5?w=1.5:w.toFixed(2)>1.6&&w.toFixed(2)<=1.75?w=1.75:w.toFixed(2)>1.9&&w.toFixed(2)<=2?w=2:w.toFixed(2)>2&&w.toFixed(2)<=2.5?w=2.25:w.toFixed(2)>2.25&&w.toFixed(2)<=3?w=2.5:w.toFixed(2)>2.8&&w.toFixed(2)<=3?w=3:w.toFixed(2)>3&&w.toFixed(2)<=3.5&&(w=3.5),(s=(window.outerWidth-window.innerWidth*g)*w)<0&&(s=0);var h=window.outerHeight*w-window.innerHeight*window.devicePixelRatio;n==t&&n>=0&&n<=20&&Math.abs(n-a)<5&&o<-20&&o>=-40?(r=0,0!=s&&h<0&&(s=s+10*w+t*w)):n<=0&&o<=0&&t<=0&&a<=0||0==n&&1==o&&t<=0&&1==a||n==t&&t==a&&a==o&&n>=1&&n<=5?(s=s+t*w+a*w,r=h/d>.9&&h/d<1.2?window.outerHeight*w-window.innerHeight*window.devicePixelRatio-2*w+n*w:window.outerHeight*w-window.innerHeight*window.devicePixelRatio-28*w+n*w):r=h/d>1&&h/d<1.25?window.outerHeight*w-window.innerHeight*window.devicePixelRatio:window.outerHeight*w-window.innerHeight*window.devicePixelRatio-28*w}if("360js"==A.name){h=window.outerHeight*w-window.innerHeight*window.devicePixelRatio;r=n==t&&n>=0&&n<=11&&Math.abs(n-a)<5&&o<-30&&o>=-40?0:n<=0&&o<=0&&t<=0&&a<=0||0==n&&1==o&&t<=0&&1==a||n==t&&t==a&&a==o&&n>=1&&n<=5?h/d>.9&&h/d<1.05?window.outerHeight*w-window.innerHeight*window.devicePixelRatio+n:window.outerHeight*w-window.innerHeight*window.devicePixelRatio-24*w+n:h/d>1&&h/d<1.25?window.outerHeight*w-window.innerHeight*window.devicePixelRatio:window.outerHeight*w-window.innerHeight*window.devicePixelRatio-24*w}if("edge"==A.name&&(r=n==t&&n>=0&&n<=11&&Math.abs(n-a)<5&&o<-20&&o>=-40?0:window.outerHeight*w-window.innerHeight*window.devicePixelRatio),"maxthon"==A.name){h=window.outerHeight*w-window.innerHeight*window.devicePixelRatio;r=n==t&&n>=0&&n<=15&&Math.abs(n-a)<5&&o<-30&&o>=-40?0:n<=0&&o<=0&&t<=0&&a<=0||0==n&&1==o&&t<=0&&1==a||n==t&&t==a&&a==o&&n>=1&&n<=5?h/d>.9&&h/d<1.1?window.outerHeight*w-window.innerHeight*window.devicePixelRatio+n*w:window.outerHeight*w-window.innerHeight*window.devicePixelRatio-24*w+n*w:h/d>.9&&h/d<1.2?window.outerHeight*w-window.innerHeight*window.devicePixelRatio+2*w:window.outerHeight*w-window.innerHeight*window.devicePixelRatio-20*w}return"uc"==A.name&&(r=window.outerHeight>screen.availHeight&&window.outerWidth>=screen.availWidth?0:window.outerHeight*w-window.innerHeight*window.devicePixelRatio-24*w),"firefox"==A.name&&(r=window.outerHeight*w-window.innerHeight*window.devicePixelRatio),r},this.getElementPosition=function(){var e={},n=i||document.getElementById(t.plugnContainer),o=n.getBoundingClientRect(),a=this.getBrowserToolbarHeight(),r=o.left,d=o.top;return e.w=n.clientWidth,e.h=n.clientHeight,e.x=r,e.y=d*window.devicePixelRatio+a,e.z=d+a,e.x<0&&(e.x=0),e.x+=T.right*window.devicePixelRatio,e.y+=T.bottom*window.devicePixelRatio,e.x=e.x*window.devicePixelRatio+s,e.w=e.w*window.devicePixelRatio,e.h=e.h*window.devicePixelRatio,d=e.y<a?e.y-a:o.top*window.devicePixelRatio,e.margin={top:d,left:"",bottom:"",right:""},e.mask=J,e},this.getEleClientRect=function(e){var i={},n=this.getBrowserToolbarHeight(),o=e.getBoundingClientRect();return i.w=e.clientWidth*window.devicePixelRatio,i.h=e.clientHeight*window.devicePixelRatio,i.x=o.left,i.y=o.top*window.devicePixelRatio+n,i.x<0&&(i.x=0),i.x=i.x*window.devicePixelRatio,i},this.throttle=function(e,i){var n=null;return function(){clearTimeout(n),n=setTimeout(function(){e()},i)}},this.getBrowserLabelSuffix=function(){return" - "+A.suffix},this.getExplore=function(){var e,i={},n=navigator.userAgent.toLowerCase();if((e=n.match(/rv:([\d.]+)\) like gecko/))?i.ie=e[1]:(e=n.match(/msie ([\d\.]+)/))?i.ie=e[1]:(e=n.match(/edge\/([\d\.]+)/))?i.edge=e[1]:(e=n.match(/firefox\/([\d\.]+)/))?i.firefox=e[1]:(e=n.match(/(?:opera|opr).([\d\.]+)/))?i.opera=e[1]:(e=n.match(/chrome\/([\d\.]+)/))?i.chrome=e[1]:(e=n.match(/version\/([\d\.]+).*safari/))&&(i.safari=e[1]),i.firefox)A.suffix="Mozilla Firefox",A.name="Firefox",T.right+=1,T.bottom+=1;else if(i.chrome){-1!=(whyun.browser||{}).string.indexOf("360EE")?(A.suffix="360安全浏览器 12.2",A.name="360"):(A.suffix="Google Chrome",A.name="Google"),T.right+=1,T.bottom+=1}else i.ie?(A.suffix="Internet Explorer",A.name="IE"):A.open=!1},window.addEventListener("resize",this.throttle(function(){t.JS_SetPosition()},0)),window.addEventListener("scroll",this.throttle(function(){t.JS_SetPosition()},0)),document.addEventListener("visibilitychange",function(){setTimeout(function(){document.hidden?t.JS_SetHide(t.plugnContainer):t.JS_SetShow(t.plugnContainer)},0)}),this.windowScreenLeftLast=0,this.windowScreenTopLast=0,setInterval(function(){window.screenLeft==t.windowScreenLeftLast&&window.screenTop==t.windowScreenTopLast||(t.JS_SetPosition(),t.windowScreenLeftLast=window.screenLeft,t.windowScreenTopLast=window.screenTop)},500)}ArgesWebControlPlug.prototype.JS_WakeUp=function(e){console.log(e),(e=document.createElement("iframe")).style.display="none",e.src=e,document.body.appendChild(e),setTimeout(function(){document.body.removeChild(e)},2e3)},ArgesWebControlPlug.JS_WakeUp=function(e){console.log(e);var i=document.createElement("iframe");i.style.display="none",i.src=e,document.body.appendChild(i),setTimeout(function(){document.body.removeChild(i)},2e3)},function(){var e="whyun";window[e]||(window[e]={});var i=78;function n(e){return/cpu (?:iphone )?os (\d+_\d+)/.test(e)?parseFloat(RegExp.$1.replace("_",".")):2}var o={result:"Chrome",details:{Chrome:5,Chromium:0,_360SE:0,_360EE:0},sorted:["Chrome","360SE","360EE","Chromium"],exec:function(e){var i={Chrome:5,Chromium:0,_360SE:0,_360EE:0},n=window.navigator.userAgent;if(/Chrome\/([\d.])+\sSafari\/([\d.])+$/.test(n)){if("Win32"==window.navigator.platform){if(window.clientInformation.languages||(i._360SE+=8),/zh/i.test(navigator.language)&&(i._360SE+=3,i._360EE+=3),window.clientInformation.languages){var o=window.clientInformation.languages.length;o>=3?(i.Chrome+=10,i.Chromium+=6):2==o?(i.Chrome+=3,i.Chromium+=6,i._360EE+=6):1==o&&(i.Chrome+=4,i.Chromium+=4)}for(var t in window.navigator.plugins)"np-mswmp.dll"==window.navigator.plugins[t].filename&&(i._360SE+=20,i._360EE+=20);window.chrome.webstore?Object.keys(window.chrome.webstore).length<=1?i._360SE+=7:2==Object.keys(window.chrome.webstore).length&&(i._360SE+=4,i.Chromium+=3):(i._360SE+=20,i._360EE+=20),window.navigator.plugins.length>=30?(i._360EE+=7,i._360SE+=7,i.Chrome+=7):window.navigator.plugins.length<30&&window.navigator.plugins.length>10?(i._360EE+=3,i._360SE+=3,i.Chrome+=3):window.navigator.plugins.length<=10&&(i.Chromium+=6)}else i._360SE-=50,i._360EE-=50,/Linux/i.test(window.navigator.userAgent)&&(i.Chromium+=5);var a=0,r=void 0;for(var t in window.navigator.plugins)if(r=/^(.+) PDF Viewer$/.exec(window.navigator.plugins[t].name)){if("Chrome"==r[1]){i.Chrome+=6,i._360SE+=6,a=1;break}if("Chromium"==r[1]){i.Chromium+=10,i._360EE+=6,a=1;break}}a||(i.Chromium+=9)}var d=new Object;d["Chrome"]=i.Chrome,d["Chromium"]=i.Chromium,d["360SE"]=i._360SE,d["360EE"]=i._360EE;var s=[];for(var l in d)s.push([l,d[l]]);return s.sort(function(e,i){return i[1]-e[1]}),this.sorted=s,this.details=i,this.result=s[0][0],"result"==e?s[0][0]:"details"==e?d:"sorted"==e?s:void 0}},t=function(){var e=window.navigator.userAgent;try{if(o.exec(),/Chrome\/([\d.])+\sSafari\/([\d.])+$/.test(e))return o.result}catch(e){return void console.warn(e)}};var a=function(){var e,o={},a=navigator.userAgent.toLowerCase();if(e=a.match(/rv:([\d.]+)\) like gecko/))o.name="ie",o["ie"]=e[1];else if(e=a.match(/msie ([\d.]+)/))o.name="ie",o["ie"]=e[1];else if(e=a.match(/edge\/([\d.]+)/))o.name="edge",o["edge"]=e[1];else if(e=a.match(/firefox\/([\d.]+)/))o.name="firefox",o["firefox"]=e[1];else if(e=a.match(/chrome\/([\d.]+)/)){o.name="chrome",o["chrome"]=e[1];var r=function(e){if(void 0!==window.scrollMaxX)return"";document.createElement("track");var n=window.navigator.appVersion,o=window.external;if(o&&"SEVersion"in o)return"sougou";if(o&&"LiebaoGetVersion"in o)return"liebao";if(/QQBrowser/.test(n))return"qq";if(/Maxthon/.test(n))return"maxthon";if(/TaoBrowser/.test(n))return"taobao";if(/BIDUBrowser/.test(n))return"baidu";if(/UBrowser/.test(n))return"uc";if(/\sOPR\//.test(n)||/Opera/.test(n)||window.navigator.vendor&&0===window.navigator.vendor.indexOf("Opera"))return"opera";var a=navigator.platform.toLowerCase();return 0==a.indexOf("mac")||0==a.indexOf("linux")?"chrome":parseInt(e)>i?"chrome":t()||"chrome"}(o["chrome"]);r&&(o["chrome"]+="("+r+")")}else(e=a.match(/opera.([\d.]+)/))?(o.name="opera",o["opera"]=e[1]):(e=a.match(/version\/([\d.]+).*safari/))?(o.name="safari",o["safari"]=e[1]):(o.name="unknown",o["unknow"]=0);var d={};if(a.indexOf("iphone")>-1)d.name="iphone",d.iphone=n(a);else if(a.indexOf("ipod")>-1)d.name="ipod",d.ipod=n(a);else if(a.indexOf("ipad")>-1)d.name="ipad",d.ipad=n(a);else if(a.indexOf("nokia")>-1)d.name="nokia",d.nokia=!0;else if(/android (\d+\.\d+)/.test(a))d.name="android",d.android=parseFloat(RegExp.$1);else if(a.indexOf("win")>-1){if(d.name="win",/win(?:dows )?([^do]{2})\s?(\d+\.\d+)?/.test(a))if("nt"==RegExp["$1"])switch(RegExp["$2"]){case"5.0":d.win="2000";break;case"5.1":d.win="XP";break;case"6.0":d.win="Vista";break;case"6.1":d.win="7";break;case"6.2":d.win="8";break;case"6.3":d.win="8.1";break;case"10.0":d.win="10";break;default:d.win="NT"}else"9x"==RegExp["$1"]?d.win="ME":d.win=RegExp["$1"]}else a.indexOf("mac")>-1?d.name="mac":a.indexOf("linux")>-1&&(d.name="linux");var s=d.name+(d[d.name]||"")+"|"+o.name+o[o.name];return{browser:o,system:d,isMobile:d.android||d.iphone||d.ios||d.ipad||d.ipod||d.nokia,string:s}}();window[e]["browser"]=a}();