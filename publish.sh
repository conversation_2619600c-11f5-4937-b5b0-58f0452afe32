#!/bin/bash

last_tags="0"

for i in $(git tag -l --sort=-creatordate);
do
    if [ `echo $i|grep ^V` ];then
      last_tags=$i
      break
    fi
done

echo $last_tags

if test $last_tags -eq "0"
then
  echo 'not found tags,publish ignore'
  exit
fi

git checkout $last_tags
last_tags_commitId=$(git rev-parse HEAD)
echo $last_tags_commitId

##文件名
fileNameDirTo="${last_tags}_sd-resource-patrol-web_${last_tags_commitId}"


## 编译
# 拷贝node_modules的方法
node_modules_path="/web/vue/yskt"
node_modules_copy() {
    project_name=$1
    if [ ! -d "${node_modules_path}/${project_name}" ]
        then
            mkdir -p "${node_modules_path}/${project_name}"
    fi

    if [ -d "./package-lock.json" ]
        then
            rm package-lock.json
    fi
    if [ -d "./yarn.lock" ]
        then
            rm yarn.lock
    fi
    # 判断服务器中是否存在对应项目的yarn.lock，如果存在就拷贝过来
    if [ -f "${node_modules_path}/${project_name}/yarn.lock" ]
        then
            cp "${node_modules_path}/${project_name}/yarn.lock" ./ > /dev/null
            # 判断服务器中是否存在对应项目的node_modules，如果存在就拷贝过来
            if [ -d "${node_modules_path}/${project_name}/node_modules" ]
                then
                    start1=$(date +%s)
                    rsync -avz ${node_modules_path}/"${project_name}"/node_modules/ ./node_modules/  > /dev/null
                    end1=$(date +%s)
                    runtime1=$((end1-start1))
                    echo "命令执行时间：$runtime1 秒"
            fi
    fi

    yarn install
    yarn build

    if [ -d "./build" ]; then
        # 如果输出不为空，表示目录包含内容
        if [ -n "$(find "./build" -maxdepth 1 -type f)" ]; then
            echo "打包成功！"
            cp yarn.lock ${node_modules_path}/${project_name}/
            mkdir -p ./dist
            cp -Rf ./build/* ./dist/

            start2=$(date +%s)
            rsync -avz ./node_modules/ ${node_modules_path}/"${project_name}"/node_modules/ > /dev/null
            end2=$(date +%s)
            runtime2=$((end2-start2))
            echo "命令执行时间：$runtime2 秒"
        else
            echo "--------------------------------------------------------------------------"
            echo "${project_name}打包失败"
            echo "--------------------------------------------------------------------------"
            exit 1
        fi
    else
        echo "--------------------------------------------------------------------------"
        echo "${project_name}打包失败"
        echo "--------------------------------------------------------------------------"
        exit 1
    fi
}
node_modules_copy "coursetour"

# 修改版本号
file_path="./docker/coursetour_version.txt"
line_number=1
sed -i "${line_number}s/.*/$last_tags/" "$file_path"

## 每次版本的文件夹
deployDir="/var/www/deploy/package/web/sd-resource-patrol-web/${last_tags}/"
mkdir -p $deployDir

##拷贝文件
rm -rf ./docker/dist
cp -Rf ./dist ./docker/
mv ./docker/ ./coursetour/


## 每次版本的文件夹E
zip -r "${fileNameDirTo}.zip" ./coursetour/
cp -rf "${fileNameDirTo}.zip" $deployDir

## 处理整体包
curl -s http://**************:8637/sc_publish_build.sh | bash -s sd-resource-patrol-web ${deployDir}${fileNameDirTo}.zip fe ${last_tags}

git reset --hard
git checkout master
exit