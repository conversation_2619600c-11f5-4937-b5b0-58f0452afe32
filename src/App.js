import { Routes, HashRouter, Route } from "react-router-dom";
import Layout from "./pages/layout";

/* 任务列表 */
import Tasks from "./pages/home";
import Task from "./pages/home/<USER>";
/* 预览 */
import PreviewTask from "./pages/home/<USER>";
import PreviewTeacher from "./pages/home/<USER>";
import PreviewFree from "./pages/home/<USER>";
/* 后台管理 */
import AdminLayout from "./pages/admin";
import AdminTask from "./pages/admin/task";
import AdminLog from "./pages/admin/log";
import AdminOperationLog from './pages/admin/operationLog'
import AdminLogStatic from "./pages/admin/log/static";

import Sys from "./pages/admin/setting";
import QuickReply from "./pages/admin/setting/quickreply";
import FreeTour from "./pages/admin/setting/freetour";

/* mobile */
import Mobile from "./pages/mobile";
import OffLine from "./pages/mobile/offline";
import Online from "./pages/mobile/online";
import FreeOnline from "./pages/mobile/freeOnline";
import FreeOffline from "./pages/mobile/freeOffline";
import ReplyList from "./pages/mobile/myReplyList";
import ReplyDetail from "./pages/mobile/myReplyList/detail";
import MobilePreviewTeacher from "./pages/mobile/PreviewTeacher";
import NoAuth from "./pages/NoAuth";
function App() {
  return (
    <HashRouter>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Tasks />}></Route>
          <Route path="task/:id" element={<Task />}></Route>

          <Route path="prev/:id" element={<PreviewTask />}></Route>

          <Route path="prevFree" element={<PreviewFree />}></Route>
          <Route path="prevTeacher" element={<PreviewTeacher />}></Route>

          <Route path="admin" element={<AdminLayout />}>
            <Route path="tasks" element={<AdminTask />}></Route>
            <Route path="operationLog" element={<AdminOperationLog />}></Route>
            {/* <Route path="log" element={<AdminLog />}></Route> */}
            <Route path="log/detail" element={<AdminLog />}></Route>
            <Route path="log/static" element={<AdminLogStatic />}></Route>
            <Route path="setting/quickreply" element={<QuickReply />}></Route>
            <Route path="setting/freetour" element={<FreeTour />}></Route>
            <Route path="setting/sys" element={<Sys />}></Route>
          </Route>
        </Route>
        <Route path="mobile" element={<Mobile />}></Route>
        {/* 任务-线上线下 */}
        <Route path="mobile/offLine/:id" element={<OffLine />}></Route>
        <Route path="mobile/onLine/:id" element={<Online />}></Route>
        {/* 自由-线上线下 */}
        <Route path="mobile/freeOnLine" element={<FreeOnline />}></Route>
        <Route path="mobile/freeOffLine" element={<FreeOffline />}></Route>
        <Route path="mobile/onLine" element={<Online />}></Route>
        <Route path="mobile/reply" element={<ReplyList />}></Route>
        <Route path="mobile/reply/:id" element={<ReplyDetail />}></Route>
        <Route
          path="mobile/previewTeacher"
          element={<MobilePreviewTeacher />}
        ></Route>
        <Route path="403" element={<NoAuth />}></Route>
      </Routes>
    </HashRouter>
  );
}

export default App;
