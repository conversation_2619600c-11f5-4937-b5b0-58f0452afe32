import instance from "./instance";

export const getPlatformConfig = (params) => {
  return instance({
    url: "/base-api/base/api/open/config/platform/config",
    params,
    hiddenToken: true,
  });
};
export const getPlatformConfigv2 = (params) => {
  return instance({
    url: "/base-api/base/api/open/config/platform/config/v2",
    params,
    hiddenToken: true,
  });
};
export const getTokenByonce = (params) => {
  return instance({
    url: "/base-api/base/api/auth/swap/token/byonce",
    params,
    hiddenToken: true,
  });
};
export const getAllOrgById = (params) => {
  return instance({
    url: "/sd-api/event/liveCenter/getAllOrgById.do",
    params,
  });
};
export const selectUserInfo = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/setting/selectUserInfo",
    params,
  });
};
export const getPermission = () => {
  return instance({
    url: "/sd-api/patrol/patrol/menu/permission?menu=patrol",
  });
};
/* admin */
export const getAdminTasks = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/task/selectByPage",
    params,
  });
};
export const getTaskById = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/task/selectTaskById",
    params,
  });
};
export const updateTaskById = (data) => {
  return instance({
    url: "/sd-api/patrol/patrol/task/updateTask",
    data,
    method: "post",
  });
};
export const updateTaskStatus = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/task/updateTaskStatus",
    params,
  });
};
export const deleteTask = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/task/deleteTask",
    params,
  });
};
export const addAdminTasks = (data) => {
  return instance({
    url: "/sd-api/patrol/patrol/task/addTask",
    data,
    method: "post",
  });
};
export const copyAdminTasks = (data) => {
  return instance({
    url: "/sd-api/patrol/patrol/task/copyTask",
    data,
    method: "post",
  });
};
export const selectMemberByPage = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/task/selectMemberByPage",
    params,
  });
};
export const selectLivingList = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/task/selectLivingList",
    params,
  });
};
export const deleteTaskMember = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/task/deleteTaskMember",
    params,
  });
};
export const searchByUserName = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/task/getUserInfo",
    params,
  });
};
export const addTaskMember = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/task/addTaskMember",
    params,
  });
};
export const updateTaskAllowControl = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/task/updateTaskAllowControl",
    params,
  });
};
export const addPatrolRecord = (data) =>
  instance({
    url: "/sd-api/patrol/patrol/record/addPatrolRecord",
    data,
    method: "post",
  });

export const updatePatrolRecord = (data) =>
  instance({
    url: "/sd-api/patrol/patrol/record/updatePatrolRecord",
    data,
    method: "post",
  });

export const importRecordMould = (data) =>
  instance({
    url: "/sd-api/patrol/patrol/record/importTaskRecordMould",
    data,
    method: "post",
  });
export const deleteRecord = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/record/deletePatrolRecord",
    params,
  });

export const getPatrolRecordList = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/record/selectByPage",
    params,
  });

export const getAdminRecordDetail = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/record/selectPatrolRecordDetail",
    params,
  });

export const getPatrolRecordEditDetail = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/myTask/selectPatrolRecordEditRecordList",
    params,
  });
export const getAdminRecordEditDetail = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/record/selectPatrolRecordEditRecordList",
    params,
  });
export const importMyRecord = (data) =>
  instance({
    url: "/sd-api/patrol/patrol/myTask/importRecord",
    data,
    method: "post",
  });
export const importTaskRecord = (data) =>
  instance({
    url: "/sd-api/patrol/patrol/task/importRecordMould",
    data,
    method: "post",
  });

export const getAllChannel = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/task/selectTaskMemberPassagewayForChoose",
    params,
  });
export const getAllGroup = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/task/groupPage",
    params,
  });
export const getMyGroup = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/task/selectTaskMemberPassagewayGroupForChoose",
    params,
  });
export const getMychannels = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/task/selectTaskMemberPassageway",
    params,
  });
export const editMychannels = (data) =>
  instance({
    url: "/sd-api/patrol/patrol/task/editTaskMemberPassageway",
    method: "post",
    data,
  });


// 日志列表
export const logList = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/log/list.do",
    params,
});



/* HOME */
export const getMyTasksDetail = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/myTask/selectTaskDetail",
    params,
  });
export const getTasksDetail = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/task/selectTaskDetail",
    params,
  });
export const getMyTasksList = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/myTask/selectByPage",
    params,
  });
export const selectPatrolRecordByTaskId = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/myTask/selectPatrolRecordByPage",
    params,
  });
export const selectTasksRecordByTaskId = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/task/selectPatrolRecordByPage",
    params,
  });
export const selectPatrolRecordDetail = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/myTask/selectPatrolRecordDetail",
    params,
  });
export const deletePatrolRecord = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/myTask/deletePatrolRecord",
    params,
  });

export const addMyPatrolRecord = (data) =>
  instance({
    url: "/sd-api/patrol/patrol/myTask/addPatrolRecord",
    data,
    method: "post",
  });

export const updateMyPatrolRecord = (data) =>
  instance({
    url: "/sd-api/patrol/patrol/myTask/updatePatrolRecord",
    data,
    method: "post",
  });
export const getPlayUrl = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/task/getPatrolLive",
    params,
  });
export const getPatrolLiveByFree = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/task/getPatrolLiveByFree",
    params,
  });
export const getMobilePlayUrl = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/task/getPatrolLiveByMobile",
    params,
  });

export const getptcmd = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/task/ptCmdStart",
    params,
    hideMsg: true,
  });
export const stopptcmd = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/task/ptCmdStop",
    params,
    hideMsg: true,
  });

export const zoomcmd = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/task/zoomcmd.do",
    params,
    hideMsg: true,
  });
export const stopzoomcmd = (params) =>
  instance({
    url: "/sd-api/patrol/patrol/task/zoomcmdstop.do",
    params,
    hideMsg: true,
  });

/* 系统设置 */
export const getSetting = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/setting/selectSetting",
    params,
  });
};
export const updateSetting = (data) => {
  return instance({
    url: "/sd-api/patrol/studio/setting/editSetting",
    data,
    method: "post",
  });
};

/* 表单模块 */
export const formGetAllData = (params) => {
  return instance({
    url: "/sd-api/patrol/formGetAllData",
    params,
  });
};

/* 快捷回复模块 */

export const updateNumTypeOne = (data) => {
  return instance({
    url: "/sd-api/patrol/patrol/quickReply/updateNumTypeOne",
    data,
    method: "post",
  });
};
export const updateNumTypeTwo = (data) => {
  return instance({
    url: "/sd-api/patrol/patrol/quickReply/updateNumTypeTwo",
    data,
    method: "post",
  });
};
export const addQuickReply = (data) => {
  return instance({
    url: "/sd-api/patrol/patrol/quickReply/addQuickReply",
    data,
    method: "post",
  });
};
export const editQuickReply = (data) => {
  return instance({
    url: "/sd-api/patrol/patrol/quickReply/editQuickReply",
    data,
    method: "post",
  });
};
export const getQuickReplyList = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/quickReply/selectByPage",
    params,
  });
};
export const delQuickReply = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/quickReply/deleteQuickReply",
    params,
  });
};
/* 自由巡课管理 */
export const addFreeUser = (data) => {
  return instance({
    url: "/sd-api/patrol/patrol/freeUser/addUser",
    data,
    method: "post",
  });
};
export const editFreeUser = (data) => {
  return instance({
    url: "/sd-api/patrol/patrol/freeUser/editUser",
    data,
    method: "post",
  });
};
export const delFreeUser = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/freeUser/deleteUser",
    params,
  });
};
export const getFreeUser = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/freeUser/selectUserDetail",
    params,
  });
};
export const freeUserList = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/freeUser/selectByPage",
    params,
  });
};
export const isFreeUser = () => {
  return instance({
    url: "/sd-api/patrol/patrol/freeUser/doUserFree",
  });
};
export const getFreeUserPassageway = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/freeUser/selectAllPassageway",
    params,
  });
};
/* 巡课记录 */
export const selectRecordedMeByPage = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/record/selectRecordedMeByPage",
    params,
  });
};
export const savePatrolRecordEditRecord = (data) => {
  return instance({
    url: "/sd-api/patrol/patrol/record/savePatrolRecordEditRecord",
    data,
    method: "post",
  });
};

/* 表单相关 */
export const formGetOneData = (params) => {
  return instance({
    url: "/sd-api/patrol/formGetOneData",
    params,
  });
};
export function forminfoSaveData(data) {
  return instance({
    url: "/sd-api/patrol/forminfoSaveData",
    data,
    method: "post",
  });
}
export const getAnswerById = (params) => {
  return instance({
    url: "/sd-api/patrol/getAnswerById",
    params,
  });
};
export function formsaveAnswer(id, data) {
  return instance({
    url: `/sd-api/patrol/saveAnswerForRepeat?templateUsedId=${id}`,
    data,
    method: "post",
  });
}
export const updateAnswerForRepeat = (id, answerId, data) => {
  return instance({
    url: `/sd-api/patrol/updateAnswerForRepeat?templateUsedId=${id}&answerId=${answerId}`,
    data,
    method: "post",
  });
};

/* 2.5 */
export const updateAllTaskMemberRule = (data) => {
  return instance({
    url: `/sd-api/patrol/patrol/task/updateAllTaskMemberRule`,
    data,
    method: "post",
  });
};
export const downloadRule = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/task/downloadRule",
    params,
  });
};
export const switchRule = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/task/switchRule",
    params,
  });
};
export const myTaskAndRecordNum = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/myTask/myTaskAndRecordNum",
    params,
  });
};

export const classTeacherIsTure = () => {
  return instance({
    url: "/sd-api/patrol/patrol/classTeacher/isTure",
  });
};
export const getTeacherClass = () => {
  return instance({
    url: "/sd-api/patrol/patrol/classTeacher/teacherClass",
  });
};
export const getClassRoomDevices = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/classTeacher/getDevices",
    params,
  });
};

export const fileCenterCallback = (data) =>
  instance({
    url: "/sd-api/event/oss/upload/file-center-callback",
    data,
    method: "post",
  });

/* 魔盒 */
export const getMagicClassRoom = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/magic/getMagicClassRoom",
    params,
  });
};
export const getPatrolClassRoom = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/free/getPatrolClassRoom",
    params,
  });
};
export const getMagicLive = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/magic/sendMagicMsg",
    params,
  });
};

/* 2.8 */
export const getGradeStageTreeByOrgId = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/getGradeStageTreeByOrgId.do",
    params,
  });
};
export const getStageGradeByOrgId = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/getStageGradeByOrgId.do",
    params,
  });
};

export const getpatrolliveByPri = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/monitor/getpatrolliveByPri.do",
    params,
  });
};
export const getPatrolLiveByFreePri = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/monitor/getPatrolLiveByFreePri.do",
    params,
  });
};
export const getVendorById = (params) => {
  return instance({
    url: "/sd-api/patrol/monitor/vendor.do",
    params,
  });
};

export const getpatrolliveByRtsp = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/monitor/getpatrolliveByRtsp.do",
    params,
  });
};

export const getVendorrList = (params) => {
  return instance({
    url: "/sd-api/monitor/monitor/docking/list.do",
    params,
  });
};
export const getTimeTableByRoomId = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/free/getTimeTableByRoomId",
    params,
  });
};
export const getRoomDeviceById = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/free/getRoomDeviceById",
    params,
  });
};
export const schoolCalendars = (data) => {
  return instance({
    url: "/sd-api/patrol/schoolCalendars.do",
    data,
    method: "post",
  });
};
export const getPatrolStatistics = (data) => {
  return instance({
    url: "/sd-api/patrol/statistics.do",
    data,
    method: "post",
  });
};

export const getSilent = () => {
  return instance({
    url: "/sd-api/monitor/monitor/iactive/silent/set.do",
  });
};
export const getSilentStatus = (params) => {
  return instance({
    url: "/sd-api/monitor/monitor/iactive/silent/status.do",
    params,
  });
};

export const getTimetable = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/monitor/timetable",
    params,
  });
};

export const statisticsbyrecorded = (params) => {
  return instance({
    url: "/sd-api/patrol/statisticsbyrecorded.do",
    params,
  });
};

export const statisticsbyrecord = (params) => {
  return instance({
    url: "/sd-api/patrol/statisticsbyrecord.do",
    params,
  });
};
export const statisticsbycomplete = (params) => {
  return instance({
    url: "/sd-api/patrol/statisticsbycomplete.do",
    params,
  });
};

/* v3.6 */
export const getBaseCourseUser = (params) => {
  return instance({
    url: "/sd-api/patrol/base/node/course",
    params,
  });
};
// 巡课3.7
// 获取班级信息
export const getPatrolClassInfo = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/free/getPatrolClassInfo",
    params,
  });
};
//被评价对象列表
export const evaluate = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/statistics/evaluate.do",
    params,
  });
};
// 巡课次数分布
export const patrolmove = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/statistics/move.do",
    params,
  });
};
// 获取巡查记录列表
// export const selectRecordByPage = (params) => {
//   return instance({
//     url: "/sd-api/patrol/patrol/statistics/selectRecordByPage",
//     params,
//   });
// };
// 数据统计
export const patrolTotal = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/statistics/total.do",
    params,
  });
};
// 巡课人员列表
export const patrolUser = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/statistics/user.do",
    params,
  });
};
// 用户pc端获取我的评价列表
export const selectMyRecordedNewByPage = (data) => {
  return instance({
    url: "/sd-api/patrol/patrol/record/selectMyRecordedNewByPage",
    data,
    method: "post",
  });
};
// 任务统计分析-统计-详情

export const selectRecordByPage = (params) => {
  return instance({
    url: "/sd-api/patrol/patrol/statistics/selectRecordByPage",
    params,
  });
};
// 查看移动端评价统计
export const appraiseStatistics = (data) => {
  return instance({
    url: "/sd-api/patrol/appraise/statistics",
    data,
    method: "post",
  });
};
// 查询移动端评价列表
export const getRecordList = (data) => {
  return instance({
    url: "/sd-api/patrol/appraise/getRecordList",
    data,
    method: "post",
  });
};
// 获取巡查任务记录详情
export const getPatrolRecordDetail = (params) => {
  return instance({
    url: "/sd-api/patrol/appraise/getPatrolRecordDetail",
    params,
  });
};

/** 3.7轮巡规则 */
// 删除规则
export const deleteRules = (params) => {
  return instance({
    url: "/sd-api/patrol/polling/rules/delete.do",
    params,
  });
}
// 编缉规则
export const editRules = (data) => {
  return instance({
    url: "/sd-api/patrol/polling/rules/edit.do",
    data,
    method: "post",
  });
};
//查询规则
export const getRules = (params) => {
  return instance({
    url: "/sd-api/patrol/polling/rules/get.do",
    params,
  });
}
//轮询-获取观看地址
export const rulesLive = (params) => {
  return instance({
    url: "/sd-api/patrol/polling/rules/live.do",
    params,
  });
}
export const rulesPage = (params) => {
  return instance({
    url: "/sd-api/patrol/polling/rules/page.do",
    params,
  });
}

//按照班级选择
export const rulesClassTree = (params) => {
  return instance({
    url: "/sd-api/patrol/polling/rules/tree/class.do",
    params,
  });
}
//按监控级选择
export const rulesMonitoringTree = (params) => {
  return instance({
    url: "/sd-api/patrol/polling/rules/tree/monitoring.do",
    params,
  });
}
//按教室级选择
export const rulesRoomTree = (params) => {
  return instance({
    url: "/sd-api/patrol/polling/rules/tree/room.do",
    params,
  });
}

// 在线喊话
export const magicOnlineTalk = (params) => {
  return instance({
    url: "/sd-api/patrol/magic/online/talk.do",
    params,
  });
}

//
export const getPatrolCourse = (params) => {
  return instance({
    url: "/sd-api/patrol/polling/rules/course.do",
    params,
  });
};
