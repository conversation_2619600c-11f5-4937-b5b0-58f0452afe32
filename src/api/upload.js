import instance from "./instance"
import axios from "axios"

export const isOpenOss = () =>
  instance({
    url: "/sd-api/uploadFile/oss/upload/isOpenOSS.do",
  })

export const getSign = params =>
  instance({
    url: "/sd-api/uploadFile/oss/upload/getSign",
    params,
  })

export const upload = (url, data, onUploadProgress, executor) => {
  const CancelToken = axios.CancelToken
  const config = {
    url,
    data,
    method: "post",
  }
  if (onUploadProgress) {
    config.onUploadProgress = onUploadProgress
  }
  if (executor) {
    config.cancelToken = new CancelToken(executor)
  }
  return instance(config)
}

export const attrCallBack = data =>
  instance({
    url: "/sd-api/uploadFile/oss/upload/attr-callback",
    data,
    method: "post",
  })
export const localUpload = data =>
  instance({
    url: "/sd-api/uploadFile/upload/upload_file.do",
    data,
    method: "post",
  })

/* ----------地址流截图----------- */
export const getScreenShot = params =>
  instance({
    url: "/sd-api/uploadFile/oss/upload/screenLive",
    params,
  })

// 录制
export const getRecordLive = params => {
  return instance({
    url: "/sd-api/uploadFile/oss/upload/liveRecord",
    params,
    hideMsg: true,
    returnWitioutCode: true,
  })
}
export const startRecordLive = params => {
  return instance({
    url: "/sd-api/uploadFile/oss/upload/startRecordLive",
    params,
    hideMsg: true,
    returnWitioutCode: true,
  })
}
export const stopRecordLive = params => {
  return instance({
    url: "/sd-api/uploadFile/oss/upload/stopRecordLive",
    params,
    hideMsg: true,
    returnWitioutCode: true,
  })
}
