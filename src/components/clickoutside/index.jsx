import React, {useEffect,useRef} from "react";


export function Clickoutside({children, cb}) {
  const cbFn = useRef();
  cbFn.current = cb;
  useEffect(() => {
    const clickOutside = document.querySelector('.click-outside');
    const checkIsIn = (e) => {
      if(!clickOutside.contains(e.target)) {
        cbFn && cbFn.current();
      }
    }
    document.body.addEventListener('click',checkIsIn);
    return () => {
      document.body.removeEventListener('click', checkIsIn);
    }
  }, [])
  return <div className="click-outside">
    {children}
  </div>
}