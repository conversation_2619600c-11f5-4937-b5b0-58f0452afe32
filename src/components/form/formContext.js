import React, { createContext, useReducer } from "react";

const formState = {
  editable: true,
  // 答题相关
  formInfoId: "", // 准备答题表单ID ----> 任务巡课中固定
  formAnswerId: "", // 已答题结果ID
  // 模板相关
  tempId: "",
  tempStr: "",
  name: "",
  data: {},
  total: 0,
  hasNumberType: false, //是否含有评分组件
};
/* 
  根据模板和data算出总分
*/
function getTotalScore(tempStr, data) {
  const calculateType = ["number"];
  const tempStrColumns = JSON.parse(tempStr).column;
  const keys = tempStrColumns.reduce((pre, cur) => {
    if (calculateType.includes(cur.type)) {
      return [...pre, cur.prop];
    }
    return [...pre];
  }, []);
  let total = 0;

  keys.forEach((key) => {
    total += Number(data[key]) ? Number(data[key]) : 0;
  });
  return total.toFixed(2);
}
export function formReducer(state, action) {
  switch (action.type) {
    /* 任务巡课 */
    case "INIT":
      return {
        ...state.data,
        ...action.payload,
        total: 0,
      };
    case "FILEDCHANGE":
      let data = {
        ...state.data,
        ...action.payload,
      };
      return {
        ...state,
        data,
      };

    case "CALCULATE":
      return {
        ...state,
        total: getTotalScore(state.tempStr, state.data),
      };

    case "SAVE":
      return {
        ...state,
        editable: false,
        ...action.payload,
      };
    case "RESET":
      return {
        ...state,
        data: {},
        editable: true,
        formAnswerId: "", // 答题ID
        total: 0,
        ...action.payload,
      };
    case "EDIT":
      return {
        ...state,
        editable: true,
        formAnswerId: "", // 答题ID
        ...action.payload,
      };
    case "CANCEL":
      return {
        ...formState,
      };
    default:
      break;
  }
}

export const FormContext = createContext();

export const FormContextProvider = ({ children }) => {
  const [state, dispatch] = useReducer(formReducer, formState);
  return (
    <FormContext.Provider value={[state, dispatch]}>
      {children}
    </FormContext.Provider>
  );
};
