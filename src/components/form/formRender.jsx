import React, { useContext, useEffect } from "react";
import styled from "styled-components";
import { FormContext } from "@/components";
import {
  Input,
  Radio,
  Space,
  Checkbox,
  Row,
  Col,
  Rate,
  Slider,
  InputNumber,
  Progress,
} from "antd";

const Root = styled.div``;
const FormGroup = styled.div`
  margin: 20px 10px;
  padding-bottom: 10px;
  word-break: break-all;
  border-bottom: 1px solid #ccc;
  font-size: 16px;
  font-weight: 500;
  color: #000000d9;
`;
const FormItem = styled.div`
  padding: 0 10px;
  .title {
    word-break: break-all;
  }
  & + & {
    margin-top: 30px;
  }
  .formItemTitle {
    word-break: break-all;
    font-size: 14px;
    font-weight: bold;
    color: #262626;
    margin-bottom: 12px;
  }
`;
function FormItemCreator(columns) {
  return columns.map((column, index) => {
    if (column.type === "title") {
      return <FormTitle key={index} column={column} />;
    }
    if (column.type === "input") {
      return <FormInput key={index} column={column} />;
    }
    if (column.type === "textarea") {
      return <FormTextarea key={index} column={column} />;
    }
    if (column.type === "radio") {
      return <FormRadio key={index} column={column} />;
    }
    if (column.type === "checkbox") {
      return <FormCheckbox key={index} column={column} />;
    }
    if (column.type === "rate") {
      return <FormRate key={index} column={column} />;
    }
    if (column.type === "number") {
      return <FormNumber key={index} column={column} />;
    }
    if (!column.type && column.column) {
      return (
        <>
          <FormGroup>{column.label}</FormGroup>
          {FormItemCreator(column.column)}
        </>
      );
    }
    return null;
  });
}
export function FormRender() {
  const [state, dispatch] = useContext(FormContext);

  const optionJson = JSON.parse(state.tempStr);
  const column = optionJson.column || [];
  const group = optionJson.group || [];
  const temp = [...column, ...group];

  return <Root>{FormItemCreator(temp)}</Root>;
}

function FormTitle({ column }) {
  return (
    <FormItem>
      {column.label && <div className="title">{column.label}</div>}
      {column.value && (
        <div className="title" style={column.styles}>
          {column.value}
        </div>
      )}
    </FormItem>
  );
}
function FormInput({ column }) {
  const [state, dispatch] = useContext(FormContext);
  return (
    <FormItem>
      <div className="formItemTitle">{column.label}</div>
      {state.editable ? (
        <Input
          maxLength={column.maxLength}
          onChange={(e) => {
            dispatch({
              type: "FILEDCHANGE",
              payload: {
                [column.prop]: e.target.value,
              },
            });
          }}
          value={state.data[column.prop]}
          placeholder={column.placeholder ? column.placeholder : "请输入"}
        />
      ) : (
        <div className="break-all">{state.data[column.prop]}</div>
      )}
    </FormItem>
  );
}
function FormTextarea({ column }) {
  const [state, dispatch] = useContext(FormContext);
  return (
    <FormItem>
      <div className="formItemTitle">{column.label}</div>
      {state.editable ? (
        <Input.TextArea
          rows={4}
          maxLength={column.maxLength}
          onChange={(e) => {
            dispatch({
              type: "FILEDCHANGE",
              payload: {
                [column.prop]: e.target.value,
              },
            });
          }}
          value={state.data[column.prop]}
          placeholder={column.placeholder ? column.placeholder : "请输入"}
        />
      ) : (
        <div className="break-all">{state.data[column.prop]}</div>
      )}
    </FormItem>
  );
}
function FormRadio({ column }) {
  const [state, dispatch] = useContext(FormContext);
  if (state.editable) {
    return (
      <FormItem>
        <div className="formItemTitle">{column.label}</div>
        <Radio.Group
          value={state.data[column.prop]}
          onChange={(e) => {
            dispatch({
              type: "FILEDCHANGE",
              payload: {
                [column.prop]: e.target.value,
                ["$" + column.prop]: e.target.label,
              },
            });
          }}
        >
          <Space wrap={true}>
            {column.dicData.map((item, index) => (
              <Radio label={item.label} key={index} value={item.value}>
                {item.label}
              </Radio>
            ))}
          </Space>
        </Radio.Group>
      </FormItem>
    );
  }
  return (
    <FormItem>
      <div className="formItemTitle">{column.label}</div>
      <div>
        {column.dicData.map((item, index) => (
          <div
            className="mt-2"
            key={index}
            style={{
              color:
                item.value === state.data[column.prop] ? "#007AFF" : "#262626",
            }}
          >
            {item.label}
          </div>
        ))}
      </div>
    </FormItem>
  );
}
function FormCheckbox({ column }) {
  const [state, dispatch] = useContext(FormContext);
  if (state.editable) {
    return (
      <FormItem>
        <div className="formItemTitle">{column.label}</div>
        <Checkbox.Group
          value={state.data[column.prop]}
          onChange={(values) => {
            const labels = values.map((value) => {
              const target = column.dicData.find(
                (item) => item.value === value
              );
              return target.label;
            });
            dispatch({
              type: "FILEDCHANGE",
              payload: {
                [column.prop]: values,
                ["$" + column.prop]: labels.join("|"),
              },
            });
          }}
        >
          <Space wrap={true}>
            {column.dicData.map((item, index) => {
              return (
                <Checkbox label={item.label} value={item.value} key={index}>
                  {item.label}
                </Checkbox>
              );
            })}
          </Space>
        </Checkbox.Group>
      </FormItem>
    );
  }
  return (
    <FormItem>
      <div className="formItemTitle">{column.label}</div>
      <div>
        {column.dicData.map((item, index) => (
          <div
            className="mt-2"
            key={index}
            style={{
              color: state.data[column.prop]?.includes(item.value)
                ? "#007AFF"
                : "#262626",
            }}
          >
            {item.label}
          </div>
        ))}
      </div>
    </FormItem>
  );
}
function FormRate({ column }) {
  const [state, dispatch] = useContext(FormContext);
  return (
    <FormItem>
      <div className="formItemTitle">{column.label}</div>
      <Rate
        disabled={!state.editable}
        value={state.data[column.prop] || 0}
        onChange={(value) => {
          dispatch({
            type: "FILEDCHANGE",
            payload: {
              [column.prop]: value,
            },
          });
        }}
        count={column.max}
      />
    </FormItem>
  );
}
function FormNumber({ column }) {
  const [state, dispatch] = useContext(FormContext);
  if (state.editable) {
    return (
      <FormItem>
        <div className="formItemTitle">{column.label}</div>
        <Slider
          step={0.1}
          value={state.data[column.prop] || 0}
          onChange={(value) => {
            dispatch({
              type: "FILEDCHANGE",
              payload: {
                [column.prop]: value,
              },
            });
            dispatch({
              type: "CALCULATE",
            });
          }}
          max={column.maxRows}
        />
        <div>
          <InputNumber
            value={state.data[column.prop] || 0}
            onChange={(value) => {
              dispatch({
                type: "FILEDCHANGE",
                payload: {
                  [column.prop]: Number(value).toFixed(1),
                },
              });
              dispatch({
                type: "CALCULATE",
              });
            }}
            step="0.1"
            size="large"
            min={0}
            max={column.maxRows}
          />
          <span className="ml-2">满分 {column.maxRows}</span>
        </div>
      </FormItem>
    );
  }
  const percent = (state.data[column.prop] / column.maxRows) * 100;
  return (
    <FormItem>
      <div className="formItemTitle">{column.label}</div>
      <div className="flex items-center">
        <Progress percent={percent} showInfo={false} strokeWidth={12} />
        <div className="ml-2">
          {state.data[column.prop] || 0}/{column.maxRows}
        </div>
      </div>
    </FormItem>
  );
}
