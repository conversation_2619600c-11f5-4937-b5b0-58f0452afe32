import React, { useEffect, useState } from "react";
import { getAnswerById, formGetOneData } from "@/api";
import { Rate, Progress } from "antd";
import { Loading } from "@/components";
import styled from "styled-components";
const Root = styled.div``;
const FormItem = styled.div`
  width: 100%;
  .title {
    word-break: break-word;
  }
  & + & {
    margin-top: 20px;
  }
  .formItemTitle {
    word-break: break-word;
    font-size: 14px;
    font-weight: bold;
    color: #262626;
    margin-bottom: 12px;
  }
  .checked {
    color: #007aff;
  }
`;

function FormTitle({ column }) {
  return (
    <FormItem>
      {column.label && <div className="title">{column.label}</div>}
      {column.value && (
        <div className="title" style={column.styles}>
          {column.value}
        </div>
      )}
    </FormItem>
  );
}

function FormInput({ column, value }) {
  return (
    <FormItem>
      <div className="formItemTitle">{column.label}</div>
      <div style={{ wordBreak: "break-word", whiteSpace: "normal" }}>
        {value ? value : "暂无描述"}
      </div>
    </FormItem>
  );
}

function FormTextarea({ column, value }) {
  return (
    <FormItem>
      <div className="formItemTitle">{column.label}</div>
      <div style={{ wordBreak: "break-word", whiteSpace: "normal" }}>
        {value ? value : "暂无描述"}
      </div>
    </FormItem>
  );
}

function FormRadio({ column, value }) {
  return (
    <FormItem>
      <div className="formItemTitle">{column.label}</div>
      <div>
        {column.dicData.map((item, index) => {
          return (
            <div
              style={{ wordBreak: "break-word" }}
              className={`mt-2 ${item.value === value ? "checked" : ""}`}
              key={index}
            >
              {item.label}
            </div>
          );
        })}
      </div>
    </FormItem>
  );
}

function FormCheckbox({ column, value = "" }) {
  return (
    <FormItem>
      <div className="formItemTitle">{column.label}</div>
      <div>
        {column.dicData.map((item, index) => {
          return (
            <div
              style={{ wordBreak: "break-word" }}
              className={`mt-2 ${
                value.indexOf(item.value) !== -1 ? "checked" : ""
              }`}
              key={index}
            >
              {item.label}
            </div>
          );
        })}
      </div>
    </FormItem>
  );
}

function FormRate({ column, value }) {
  return (
    <FormItem>
      <div className="formItemTitle">{column.label}</div>
      <Rate disabled value={value} count={column.max} />
    </FormItem>
  );
}

function FormNumber({ column, value = 0 }) {
  const percent = (value / column.maxRows) * 100;
  return (
    <FormItem>
      <div className="formItemTitle">{column.label}</div>
      <Progress
        format={() => `${value} / ${column.maxRows}`}
        percent={percent}
      />
    </FormItem>
  );
}

export function FormRes({
  formTemplateAnswerId,
  formInfoId,
  formTemplateInfoId,
  style,
}) {
  const [column, setColumn] = useState(null);
  const [anwser, setAnswer] = useState(null);
  useEffect(() => {
    getAnswerById({
      id: formTemplateAnswerId,
      templateUsedId: formInfoId,
    }).then((res) => {
      if (res.code === 0) {
        if (!res.data[0]) {
          setAnswer({});
        } else {
          setAnswer(res.data[0]);
        }
      }
    });

    formGetOneData({ formId: formTemplateInfoId }).then((res) => {
      if (res.code === 0 && res.data) {
        const optionJson = JSON.parse(res.data.formInfo);
        const column = optionJson.column || [];
        const group = optionJson.group || [];
        const temp = [...column, ...group];
        setColumn(temp);
      }
    });
  }, [formTemplateAnswerId, formInfoId, formTemplateInfoId]);

  if (!column || !anwser) return <Loading />;

  function FormItemCreator(columns) {
    return columns.map((column, index) => {
      if (column.type === "title") {
        return <FormTitle key={index} column={column} />;
      }
      if (column.type === "input") {
        return (
          <FormInput value={anwser[column.prop]} key={index} column={column} />
        );
      }
      if (column.type === "textarea") {
        return (
          <FormTextarea
            value={anwser[column.prop]}
            key={index}
            column={column}
          />
        );
      }
      if (column.type === "radio") {
        return (
          <FormRadio value={anwser[column.prop]} key={index} column={column} />
        );
      }
      if (column.type === "checkbox") {
        return (
          <FormCheckbox
            value={anwser[column.prop]}
            key={index}
            column={column}
          />
        );
      }
      if (column.type === "rate") {
        return (
          <FormRate value={anwser[column.prop]} key={index} column={column} />
        );
      }
      if (column.type === "number") {
        return (
          <FormNumber value={anwser[column.prop]} key={index} column={column} />
        );
      }
      if (!column.type && column.column) {
        return <div key={index}>{FormItemCreator(column.column)}</div>;
      }
      return null;
    });
  }
  return <Root style={style}>{FormItemCreator(column)}</Root>;
}
