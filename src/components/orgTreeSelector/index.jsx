import {useEffect, useState} from "react"
import {TreeSelect} from "antd"
import {getAllOrgById} from "@/api"

function data2Tree(data, pid) {
  if(!Array.isArray(data)) {
    throw Error('data2Tree function need array parmas')
  }
  const _data = JSON.parse(JSON.stringify(data));
  return _data.filter(p => {
    const _childrens = _data.filter(c => c.pid === p.id);
    _childrens.length && (p.children = _childrens);
    return p.pid === pid;
  })
}

export function OrgTreeSelector({onOrgChange}) {
  const [list, setList]  = useState([]);
  const [data, setData] = useState([]);
  const onChange = (value) => {
    if(value) {
      const target = list.find(item => item.id === value);
      if(target) {
        onOrgChange(target.title)
      }
    } else {
      onOrgChange('');
    }
  }
  useEffect(() => {
    getAllOrgById({ orgId: 0 }).then(result => {
      const list = result.data.map(org => ({
        id: org.id,
        pid: org.parentId,
        value: org.id,
        title: org.orgName
      }))
      if(list.length > 0) {
        setList(list);
        setData(data2Tree(list, list[0].pid))
      }
    })
  }, [])
  
  return <TreeSelect
    showSearch
    style={{ width: '100%' }}
    // value={value}
    dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
    placeholder="请选择机构"
    allowClear
    treeDefaultExpandAll
    onChange={onChange}
    treeData={data}
  />
}