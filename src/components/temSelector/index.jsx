import React,{ useState, useEffect } from "react";
import styled from "styled-components";
import { YsEmpt, Loading } from "@/components";
import { useSearchParams } from "react-router-dom"
import { formGetAllData } from "@/api"
import { Button } from "antd";
import { PlusOutlined } from '@ant-design/icons';

const Root = styled.div`
  width: 328px;
  background: #fff;
  box-shadow: 0px 0px 16px 0px rgba(0,0,0,0.1);
  border-radius: 4px;
`
const Header = styled.div`
  display: flex;
  height: 46px;
  border-bottom: 1px solid #F0F0F0;
  div {
    border-bottom: 2px solid transparent;
    cursor: pointer;
    line-height: 46px;
    color: #262626;
    text-align: center;
    flex: 1;
  }
  div.active {
    color: #007AFF;
    border-bottom-color: #007AFF;
  }
`
const Content = styled.div`
  position: relative;
  height: 200px;
  overflow-y: auto;
  div.tempItem {
    cursor: pointer;
    height: 40px;
    padding: 0 12px;
    line-height: 40px;
    color: #262626;
    &:hover {
      background: #F5F5F5;
    }
    &.active {
      background: #EBF5FF;
      color: #007AFF;
    }
  }
`

export function TemSelector({
  value,
  isFree = false,
  onChange
}) {
  const navs = isFree ? [
    {
      label: '公共表单',
      active: 1
    },
    {
      label: '私有表单',
      active: 2
    },
  ] : [
    {
      label: '私有表单',
      active: 2
    },
    {
      label: '公共表单',
      active: 1
    },
  ]

  const [params] = useSearchParams();
  const bureauId = params.get('bureauId')
  const [active, setActive] = useState(isFree ? 1 : 2);
  console.log("active", isFree);

  const [list, setList] = useState([])
  const [loading, setLoading] = useState(false);

  const getList = async (isPubic) => {
    setLoading(true)
    const result = await formGetAllData({isPubic});
    setLoading(false)
    setList(result.data)
  }

  const onChangeTab = (tab) => {
    if(active === tab) return;
    setActive(tab);
    getList(tab);
  }

  const renderList = () => {
    if(loading) {
      return <Loading />
    }
    if(active === 1 && list.length === 0) {
      return <YsEmpt msg="暂无公共表单"/>
    }
    return <>
      {
        active === 2 &&  <div className="p-3">
          {
            bureauId ?  <Button target="_blank" href={"/yskt/a/#/customForm?isPublic=2&bureauId="+bureauId} icon={<PlusOutlined />} type="dashed" block>新建表单</Button> :
            <Button target="_blank" href={"/yskt/a/#/customForm?isPublic=2"} icon={<PlusOutlined />} type="dashed" block>新建表单</Button>
          }

        </div>
      }
      {
        list.map((item,index) => <div key={index} onClick={() => onChange(item)} className={`tempItem truncate ${value===item.formTemplateInfoId?'active':''}`}>{item.formName}</div>)
      }
    </>
  }

  useEffect(() => {
    getList(isFree ? 1 : 2);
  }, [])

  return <Root>
    <Header>
      {
        navs.map(item =>
          <div
            className={item.active === active ? 'active' : ''}
            key={item.active}
            onClick={() => onChangeTab(item.active)}>
              {item.label}
          </div>)
      }
    </Header>
    <Content>
      {
        renderList()
      }
    </Content>
  </Root>
}
