import { useState, forwardRef, useImper<PERSON><PERSON><PERSON><PERSON>, useEffect } from "react";
import { Form, AutoComplete, Cascader } from "antd";
import {
  searchByUserName,
  getGradeStageTreeByOrgId,
  getStageGradeByOrgId,
} from "@/api";
import { $hidePhone, findItemRecursive } from "@/tools";
const { Option } = AutoComplete;

/* 
  人员-年级-班级 form表单
*/

async function getGradeClassInfo(type) {
  const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
  let result;
  if (type === "class") {
    result = await getGradeStageTreeByOrgId({ orgId: userInfo.orgId });
  } else {
    result = await getStageGradeByOrgId({ orgId: userInfo.orgId });
  }
  if (result.code === 0) {
    console.log('result.data', result.data)
    getFinilChildern(result.data)
    return result.data || [];
  }
}
const getFinilChildern = (list) => {
  for (let i of list) {
    if (i.children && i.children.length > 0) {
      getFinilChildern(i.children)
    }
    else {
      delete i.children
    }
  }
}
function useClassOptions() {
  const [options, setOptions] = useState([]);
  useEffect(() => {
    getGradeClassInfo("class").then((optionList) => {
      // setOptions(optionList)
      if (optionList && optionList.length === 1 && optionList[0].children) {
        setOptions(optionList[0].children);
      } else {
        setOptions(optionList);
      }
    });
  }, []);
  return options;
}
function useGradeOptions() {
  const [options, setOptions] = useState([]);
  useEffect(() => {
    getGradeClassInfo().then((optionList) => {
      setOptions(optionList);
    });
  }, []);
  return options;
}

export const TourObjInlineForm = forwardRef(function (
  {
    personalIsFill,
    personalIsShow,
    gradeIsFill,
    gradeIsShow,
    classIsFill,
    classIsShow,

    defaultUserId,
    defaultUserLabel,
    defaultClassId,
    defaultClassName,
    defaultGradeId,
    defaultGradeName,
  },
  ref
) {
  const classOptions = useClassOptions();
  const gradeOptions = useGradeOptions();

  const [userInfo, setUserInfo] = useState({
    value: "",
    label: "",
    validateStatus: "sucess",
    errorMsg: "",
  });

  useEffect(() => {
    if (!defaultUserId) {
      setUserInfo({
        value: "",
        label: "",
        validateStatus: "sucess",
        errorMsg: "",
      });
      return;
    }
    setUserInfo({
      value: defaultUserId,
      label: defaultUserLabel,
      validateStatus: "sucess",
      errorMsg: "",
    });
  }, [defaultUserId, defaultUserLabel]);

  const [classInfo, setClassInfo] = useState({
    value: null,
    label: "",
    validateStatus: "sucess",
    errorMsg: "",
  });

  useEffect(() => {
    if (!defaultClassId) {
      setClassInfo({
        value: "",
        label: "",
        validateStatus: "sucess",
        errorMsg: "",
      });
      return;
    }
    const paths = findItemRecursive(classOptions, defaultClassId);
    if (paths) {
      const value = paths.map((path) => path.id);
      setClassInfo({
        value,
        label: defaultClassName,
        validateStatus: "sucess",
        errorMsg: "",
      });
    }
  }, [defaultClassId, defaultClassName, classOptions]);

  const [gradeInfo, setGradeInfo] = useState({
    value: null,
    label: "",
    validateStatus: "sucess",
    errorMsg: "",
  });

  useEffect(() => {
    if (!defaultGradeId) {
      setGradeInfo({
        value: "",
        label: "",
        validateStatus: "sucess",
        errorMsg: "",
      });
      return;
    }
    const paths = findItemRecursive(gradeOptions, defaultGradeId);
    if (paths) {
      const value = paths.map((path) => path.id);
      setGradeInfo({
        value: value,
        label: defaultGradeName,
        validateStatus: "sucess",
        errorMsg: "",
      });
    }
  }, [defaultGradeId, defaultGradeName, gradeOptions]);

  const [options, setOptions] = useState([]);
  const onSearch = async (userName) => {
    if (!userName) {
      setOptions([]);
    } else {
      const result = await searchByUserName({ userName });
      if (result.code === 0) {
        const data = result.data.map((item) => ({
          value: item.coreUserInfoId,
          label: item.userName,
          phone: item.phone,
        }));
        setOptions(data.slice(0, 5));
      }
    }
  };
  const onSelect = async (value) => {
    const target = options.find((item) => item.value === value);
    setUserInfo((pre) => ({
      ...pre,
      label: target.label,
      value: target.value,
      validateStatus: "sucess",
      errorMsg: "",
    }));
  };

  const onGradeChange = (value, selectedOptions) => {
    value = value?.length === 0 ? null : value;
    const grade = selectedOptions?.find((item) => item.pid !== "-1");
    // 必填
    if (gradeIsFill) {
      if (!grade) {
        setGradeInfo((pre) => ({
          ...pre,
          value,
          label: "",
          validateStatus: "error",
          errorMsg: "请选择正确的年级",
        }));
      } else {
        setGradeInfo((pre) => ({
          ...pre,
          value,
          label: grade.name,
          validateStatus: "sucess",
          errorMsg: "",
        }));
      }
    } else {
      // 非必填
      // 填写了
      if (value) {
        if (!grade) {
          setGradeInfo((pre) => ({
            ...pre,
            value,
            label: "",
            validateStatus: "error",
            errorMsg: "请选择正确的年级",
          }));
        } else {
          setGradeInfo((pre) => ({
            ...pre,
            value,
            label: grade.name,
            validateStatus: "sucess",
            errorMsg: "",
          }));
        }
      } else {
        setGradeInfo((pre) => ({
          ...pre,
          value,
          label: "",
          validateStatus: "sucess",
          errorMsg: "",
        }));
      }
    }
  };

  const onClassChange = (value, selectedOptions) => {
    value = value?.length === 0 ? null : value;
    // 填写是否正确
    const classInfo = selectedOptions?.find((item) => item.nodeType === 3);
    console.log('classIsFill', classIsFill)
    console.log('value', value)
    console.log('selectedOptions', selectedOptions)
    console.log('classInfo', classInfo)
    // 必填
    if (classIsFill) {
      console.log(111)
      if (!classInfo) {
        setClassInfo((pre) => ({
          ...pre,
          value,
          label: "",
          validateStatus: "error",
          errorMsg: "请选择正确的班级",
        }));
      } else {
        setClassInfo((pre) => ({
          ...pre,
          value,
          label: classInfo.name,
          validateStatus: "sucess",
          errorMsg: "",
        }));
      }
    } else {
      console.log(222)
      // 非必填
      // 填写了
      if (value) {
        if (!classInfo) {
          setClassInfo((pre) => ({
            ...pre,
            value,
            label: "",
            validateStatus: "error",
            errorMsg: "请选择正确的班级",
          }));
        } else {
          setClassInfo((pre) => ({
            ...pre,
            value,
            label: classInfo.name,
            validateStatus: "sucess",
            errorMsg: "",
          }));
        }
      } else {
        setClassInfo((pre) => ({
          ...pre,
          value,
          label: "",
          validateStatus: "sucess",
          errorMsg: "",
        }));
      }
    }
  };

  useImperativeHandle(ref, () => ({
    /* 任务 */
    validate() {
      if (personalIsFill && !userInfo.value) {
        setUserInfo((pre) => ({
          ...pre,
          validateStatus: "error",
          errorMsg: "请选择人员",
        }));
        return false;
      }
      if (gradeIsFill && !gradeInfo.value) {
        setGradeInfo((pre) => ({
          ...pre,
          validateStatus: "error",
          errorMsg: "请选择年级",
        }));
        return false;
      }
      if (classIsFill && !classInfo.value) {
        setClassInfo((pre) => ({
          ...pre,
          validateStatus: "error",
          errorMsg: "请选择班级",
        }));
        return false;
      }
      return {
        userInfo,
        classInfo,
        gradeInfo,
      };
    },
    resetValues() {
      setUserInfo({
        value: "",
        label: "",
        validateStatus: "sucess",
        errorMsg: "",
      });
      setGradeInfo({
        value: "",
        label: "",
        validateStatus: "sucess",
        errorMsg: "",
      });
      setClassInfo({
        value: "",
        label: "",
        validateStatus: "sucess",
        errorMsg: "",
      });
    },
  }));

  return (
    <>
      {personalIsShow === 1 && (
        <Form.Item
          label=""
          required={personalIsFill === 1}
          validateStatus={userInfo.validateStatus}
          help={userInfo.errorMsg}
        >
          <AutoComplete
            className="mb-4"
            style={{ width: 200 }}
            value={userInfo.label}
            defaultValue={userInfo.label}
            onChange={(label) => {
              setUserInfo((pre) => ({
                ...pre,
                label,
                value: "",
              }));
            }}
            onSelect={onSelect}
            onSearch={onSearch}
            placeholder="请输入姓名搜索"
          >
            {options.map((option) => {
              return (
                <Option key={option.value}>
                  <div className="flex justify-between">
                    <span className="flex-1 truncate">{option.label}</span>
                    <span>{$hidePhone(option.phone)}</span>
                  </div>
                </Option>
              );
            })}
          </AutoComplete>
        </Form.Item>
      )}
      {classIsShow === 1 && (
        <Form.Item
          label=""
          required={classIsFill === 1}
          validateStatus={classInfo.validateStatus}
          help={classInfo.errorMsg}
        >
          <Cascader
            style={{ width: 200 }}
            value={classInfo.value}
            options={classOptions}
            onChange={onClassChange}
            placement="bottomLeft"
            placeholder="请选择班级"
            fieldNames={{ label: "name", value: "id", children: "children" }}
          />
        </Form.Item>
      )}
      {gradeIsShow === 1 && (
        <Form.Item
          label=""
          required={gradeIsFill === 1}
          validateStatus={gradeInfo.validateStatus}
          help={gradeInfo.errorMsg}
        >
          <Cascader
            style={{ width: 200 }}
            value={gradeInfo.value}
            options={gradeOptions}
            onChange={onGradeChange}
            placeholder="请选择年级"
            fieldNames={{ label: "name", value: "id", children: "children" }}
          />
        </Form.Item>
      )}
    </>
  );
});
