import { useIsFreeUser } from "./useIsFreeUser";
import { useRouterGo } from "./useRouterGo";
import { useReplyList } from "./useReplyList";
import { useTableAndAnswer } from "./useTableAndAnswer";
import { useIsClassteacher } from "./useIsClassteacher";
import { useTeacherRoom } from "./useTeacherRoom";
import { useTeacherRoomDevice } from "./useTeacherRoomDevice";
import { useTourInfo } from "./useTourInfo";
import { useDomObserver } from "./useDomObserver";
import { useIactiveLoop } from "./useIactiveLoop";
import { useMqtt } from "./useMqtt";
import { useAudio } from "./useAudio";
import { useWatermark } from './useWatermark'
export {
  useIsFreeUser,
  useRouterGo,
  useReplyList,
  useTableAndAns<PERSON>,
  useIsClassteacher,
  useTeach<PERSON><PERSON><PERSON>,
  useTeacherRoomDevice,
  useTourInfo,
  useDomObserver,
  useIactiveLoop,
  useMqtt,
  useAudio,
  useWatermark
};
