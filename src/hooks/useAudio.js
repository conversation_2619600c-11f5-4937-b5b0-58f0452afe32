import { useState, useRef, useCallback, useEffect } from "react";
import { magicOnlineTalk } from "@/api";
import { message } from "antd";

/**
 * 音频自定义Hook
 * @param {string} roomId - 房间ID
 * @returns {Object} 音频通话相关的状态和方法
 */
export const useAudio = (roomId, clientId) => {
  // 连接状态: 0-未连接, 1-连接中, 2-已连接
  const [connectionState, setConnectionState] = useState(0);
  // 音频流
  const [stream, setStream] = useState(null);
  // 麦克风状态
  const [micEnabled, setMicEnabled] = useState(false);
  // WebRTC连接引用
  const peerConnectionRef = useRef(null);
  // 存储最新的clientId
  const clientIdRef = useRef(clientId);

  // 当clientId变化时更新ref
  useEffect(() => {
    clientIdRef.current = clientId;
  }, [clientId]);

  /**
   * 获取音频流
   * @returns {Promise<MediaStream>} 音频流
   */
  const getAudioStream = useCallback(async () => {
    try {
      return await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      });
    } catch (err) {
      throw new Error("无法获取麦克风权限: " + err.message);
    }
  }, []);

  /**
   * 创建PeerConnection
   * @returns {RTCPeerConnection} PeerConnection实例
   */
  const createPeerConnection = useCallback(() => {
    const pc = new RTCPeerConnection({
      iceServers: [{ urls: "stun:stun.l.google.com:19302" }],
    });
    return pc;
  }, []);

  /**
   * 发送WHIP请求
   * @param {RTCSessionDescription} offer - WebRTC offer
   * @param {string} pushWebRTCUrl - WHIP服务URL
   * @returns {Promise<string>} 返回Location头信息
   */
  const sendWhipRequest = useCallback(async (offer, pushWebRTCUrl) => {
    try {
      const response = await fetch(pushWebRTCUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/sdp",
          Accept: "application/sdp",
        },
        body: offer.sdp,
      });
      console.log("WHIP请求正在发送");
      console.log(response);
      if (!response.ok) {
        throw new Error(`WHIP请求失败: ${response.status} ${response.statusText}`);
      }
      const answer = await response.text();
      console.log("answer", answer);
      await peerConnectionRef.current.setRemoteDescription({
        type: "answer",
        sdp: answer,
      });

      return response.headers.get("Location"); // 保存后续DELETE用的URL
    } catch (err) {
      throw new Error("WHIP通信失败: " + err.message);
    }
  }, []);

  /**
   * 开始推流
   * @returns {Promise<void>}
   */
  const startPublish = useCallback(async () => {
    // 设置状态为连接中
    setConnectionState(1);

    try {
      const params = {
        option: "1", // 1:开始喊话
        roomId,
        webIndex: clientIdRef.current,
      };

      let res = await magicOnlineTalk(params);
      if (res.code === 0) {
        const data = res.data;

        // 1. 获取音频流
        const audioStream = await getAudioStream();
        console.log("获取音频流成功");
        // 默认关闭麦克风
        audioStream.getAudioTracks().forEach(track => {
          track.enabled = false;
        });
        setStream(audioStream);

        // 2. 创建PeerConnection
        peerConnectionRef.current = createPeerConnection();

        // 3. 添加音轨
        audioStream.getTracks().forEach((track) => {
          peerConnectionRef.current.addTrack(track, audioStream);
        });

        // 4. 创建Offer
        const offer = await peerConnectionRef.current.createOffer({
          offerToReceiveAudio: false,
          offerToReceiveVideo: false,
        });

        // 5. 设置LocalDescription
        await peerConnectionRef.current.setLocalDescription(offer);

        // 6. 发送WHIP请求
        await sendWhipRequest(offer, data.pushWebRTCUrl);

        // 设置状态为已连接
        setConnectionState(2);
        console.log("推流成功启动");
      } else {
        message.error(res.msg);
      }
    } catch (err) {
      message.error(err.message);
      console.error("启动通话失败:", err);
      stopPublish();
      // 连接失败，恢复为未连接状态
      setConnectionState(0);
    }
  }, [roomId, getAudioStream, createPeerConnection, sendWhipRequest]);

  /**
   * 停止推流
   * @returns {Promise<void>}
   */
  const stopPublish = useCallback(async () => {
    console.log("connectionState", connectionState);
    // if (connectionState === 1) return;
    try {
      const params = {
        option: "2", // 2:结束喊话
        roomId: roomId,
        webIndex: clientIdRef.current,
      };

      const res = await magicOnlineTalk(params);
      if (res.code === 0) {
        console.log("推流已关闭");
      }
    } catch (err) {
      console.error("关闭推流失败:", err);
    } finally {
      // 关闭PeerConnection
      if (peerConnectionRef.current) {
        peerConnectionRef.current.close();
        peerConnectionRef.current = null;
      }

      // 停止音频轨道
      if (stream) {
        stream.getTracks().forEach((track) => track.stop());
        setStream(null);
      }

      // 重置状态
      setConnectionState(0);
      setMicEnabled(false);
    }
  }, [roomId, stream]);

  /**
   * 切换麦克风状态
   * @param {boolean} enabled - 是否启用麦克风
   */
  const toggleMicrophone = useCallback((enabled) => {
    if (!stream) return;

    // 如果没有传入参数，则切换当前状态
    const newState = enabled !== undefined ? enabled : !micEnabled;

    stream.getAudioTracks().forEach((track) => {
      track.enabled = newState;
    });

    setMicEnabled(newState);
  }, [stream, micEnabled]);

  return {
    connectionState,  // 0-未连接, 1-连接中, 2-已连接
    stream,
    micEnabled,
    startPublish,
    stopPublish,
    toggleMicrophone
  };
};