import { useEffect, useState } from "react";

export function useDomObserver(dom) {
  const [domInfo, setDomInfo] = useState(null);
  /* 动态监听dom的信息 */
  useEffect(() => {
    const targetElement = document.getElementById(dom);
    if (!targetElement) return;
    let data = null;
    const observer = new ResizeObserver((entries) => {
      for (let entrie of entries) {
        data = entrie.contentRect;
        setDomInfo(data);
      }
    });
    observer.observe(targetElement);
    return () => {
      observer.disconnect();
    };
  }, [dom]);

  return domInfo;
}
