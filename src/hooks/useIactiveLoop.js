import { useEffect, useRef } from "react";
import { getSilentStatus } from "@/api";
export function useIactiveLoop(info) {
  const timer = useRef();
  useEffect(() => {
    if (!info) return;
    const videoContentWrap = document.getElementById("videoContentWrap");
    if (!videoContentWrap) return;
    const iactive = videoContentWrap.dataset.iactive;
    if (iactive === 0) return;
    if (info.belongs == 10000) {
      if (timer.current) {
        clearInterval(timer);
        timer.current = null;
      }
      timer.current = setInterval(() => {
        getSilentStatus({ nodeId: info.monitoringNodeId });
      }, 5000);
    }
    return () => {
      if (timer.current) {
        clearInterval(timer.current);
        timer.current = null;
      }
    };
  }, [info]);
}
