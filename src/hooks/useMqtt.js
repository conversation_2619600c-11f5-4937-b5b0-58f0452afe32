import React, { useState, useEffect, useRef, useCallback } from "react";
import { mqtt } from "@/tools";
import { getTokenFromCookie } from "@/tools";
import { getPlatformConfigv2 } from "@/api";

/**
 * MQTT连接管理Hook
 * @returns {Object} MQTT客户端和相关状态
 */
export const useMqtt = () => {
  // 使用ref存储不需要触发重渲染的数据
  const mqttRef = useRef(null);
  const webRtcUrlRef = useRef("");
  const [clientId, setClientId] = useState("");
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState(null);

  /**
   * 获取MQTT连接URL
   */
  const getUrl = useCallback(async () => {
    try {
      let baseURL = 'http://192.168.168.30/base-api'
      let isProduction = process.env.NODE_ENV === "production" ? true : false
      if (process.env.NODE_ENV === "production") {
        baseURL = "";
      }

      console.log('process.env.NODE_ENV', process.env.NODE_ENV)
      const res = await window.ysCommonToolLib.p_configCenter({
        baseUrl: isProduction ? baseURL + '/base-api' : baseURL,
        manually: 'none',
        v2: true,
      })
      console.log('配置中心', res)
      console.log('res.data.mqttWebsocketConnectUrl', res.data.mqttWebsocketConnectUrl)
      if (res.code === 0 && res.data) {
        webRtcUrlRef.current = res.data.mqttWebsocketConnectUrl;
        return true;
      } else {
        setError("获取MQTT连接地址失败");
        return false;
      }
    } catch (err) {
      setError(`获取MQTT连接地址出错: ${err.message}`);
      return false;
    }
  }, []);

  /**
   * 生成随机主题
   * @returns {number} 随机数
   */
  const randomNum = useCallback(() => {
    return Math.floor(
      Math.random() * (9999999999 - 1000000000 + 1) + 1000000000
    );
  }, []);

  /**
   * 连接MQTT服务
   */
  const linkMqtt = useCallback(() => {
    if (!webRtcUrlRef.current) {
      setError("MQTT连接地址未获取，无法连接");
      return;
    }

    try {
      const wb = `wb_${randomNum()}`;
      const options = {
        url: webRtcUrlRef.current,
        token: getTokenFromCookie().split(" ")[1],
        clientId: wb,
        theme: [
          {
            type: `ys/iactive/web`,
            clientId: wb,
          },
        ],
      };

      // 创建MQTT实例
      mqttRef.current = new mqtt(options);

      // 连接成功事件
      mqttRef.current.on("connect", (data) => {
        console.log("mqtt连接成功===》", data);
        setClientId(data.clientId || "");
        setIsConnected(true);
        setError(null);
      });

      // 消息接收事件
      mqttRef.current.on("message", (data) => {
        console.log("后台推送===》", data);
      });

      // 错误事件
      mqttRef.current.on("error", (err) => {
        console.error("MQTT连接错误:", err);
        setError(`MQTT连接错误: ${err.message || JSON.stringify(err)}`);
        setIsConnected(false);
      });
    } catch (err) {
      setError(`MQTT连接初始化失败: ${err.message}`);
    }
  }, []);

  /**
   * 断开MQTT连接
   */
  const disconnect = useCallback(() => {
    if (mqttRef.current) {
      mqttRef.current.closeMqtt && mqttRef.current.closeMqtt();
      mqttRef.current = null;
      setIsConnected(false);
    }
  }, []);

  // 初始化MQTT连接
  useEffect(() => {
    const initMqtt = async () => {
      const urlSuccess = await getUrl();
      if (urlSuccess) {
        linkMqtt();
      }
    };

    initMqtt();

    // 组件卸载时清理资源
    return () => {
      disconnect();
    };
  }, []);

  // 返回MQTT客户端和状态
  return {
    mqttClient: mqttRef.current,
    clientId,
    isConnected,
    error,
    webRtcUrl: webRtcUrlRef.current,
    disconnect,
    reconnect: linkMqtt,
  };
};
