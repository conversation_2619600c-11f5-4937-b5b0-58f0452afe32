import { useEffect,useState } from "react";
import {getQuickReplyList} from "@/api"

export function useReplyList(initPagination = {pageNo: 1, pageSize: 10}) {
  const [pagination, setPagination] = useState(() => initPagination);
  const [data, setData] = useState({
    list: [],
    total: 0,
  })
  useEffect(() => {
    const getList = async () => {
      const result = await getQuickReplyList(pagination);
      if(result && result.code === 0) {
        setData({
          list: result.data,
          total: Number(result.totalDatas),
        })
      }
    }
    getList();
  }, [pagination])
  return {
    ...data,
    pagination,
    setPagination
  }
}