import { useNavigate, useSearchParams } from "react-router-dom"

export function useRouterGo() {
  const navigate = useNavigate()
  const [params] = useSearchParams()
  return (url, config) => {
    const bureauId = params.get("bureauId")
    let jd = params.get("jd")
    if (jd === "null") {
      jd = 0
    }
    if (bureauId && url !== -1) {
      if (url.indexOf("?") !== -1) {
        navigate(`${url}&bureauId=${bureauId}&jd=${jd}`, config)
      } else {
        navigate(`${url}?bureauId=${bureauId}&jd=${jd}`, config)
      }
    } else {
      navigate(url, config)
    }
  }
}
