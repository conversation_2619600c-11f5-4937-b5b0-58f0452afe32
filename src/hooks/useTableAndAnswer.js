import { useState, useEffect } from "react";
import { getAnswerById, formGetOneData } from "@/api";
/* 
  获取表单和以及配套答案
*/
export function useTableAndAnswer(
  formInfoId,
  formTemplateInfoId,
  formTemplateAnswerId,
  refresh
) {
  const [formStr, setFormStr] = useState("");
  const [column, setColumn] = useState(null);
  const [anwser, setAnswer] = useState({});
  useEffect(() => {
    if (!formTemplateAnswerId || !formTemplateInfoId || !formTemplateAnswerId)
      return;
    getAnswerById({
      id: formTemplateAnswerId,
      templateUsedId: formInfoId,
    }).then((res) => {
      if (res.code === 0) {
        const answerStr = res.data[0];
        if (answerStr) {
          const keys = Object.keys(answerStr);
          keys.forEach((key) => {
            const value = answerStr[key];
            if (
              value &&
              typeof value === "string" &&
              value.indexOf(":") !== -1
            ) {
              answerStr[key] = value.split(":");
            }
          });
          setAnswer(answerStr);
        }
      }
    });

    formGetOneData({ formId: formTemplateInfoId }).then((res) => {
      if (res.code === 0 && res.data) {
        const optionJson = JSON.parse(res.data.formInfo);
        const column = optionJson.column || [];
        const group = optionJson.group || [];
        const temp = [...column, ...group];
        setColumn(temp);
        setFormStr(res.data.formInfo);
      }
    });
  }, [formTemplateAnswerId, formInfoId, formTemplateInfoId, refresh]);

  return {
    formStr,
    column,
    anwser,
  };
}
