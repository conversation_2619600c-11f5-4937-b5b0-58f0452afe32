import { useState } from "react"
import styled from "styled-components"
export const TourObjRoot = styled.div`
  .tourItem + .tourItem {
    margin-top: 16px;
  }
  .tourItem {
    border: 1px solid #f5f5f5;
    background: #f5f5f5;
    border-radius: 4px;
    height: 36px;
  }
  .tourItem.error {
    border-color: #f53f3f;
  }
`
export const defaultTourInfo = {
  personalIsFill: 0,
  personalIsShow: 0,
  gradeIsFill: 0,
  gradeIsShow: 0,
  classIsFill: 0,
  classIsShow: 0,
  validateStatus: "sucess",
  errorMsg: "",
}
const validateTourInfo = info => {
  const { validateStatus, errorMsg, ...reset } = info
  const isEmpt = Object.values(reset).every(value => value === 0)
  if (isEmpt) {
    return {
      validateStatus: "error",
      errorMsg: "至少选择一个字段显示，至少选择一个字段必填",
    }
  }
  const showFieldList = []
  const fillFieldList = []
  Object.keys(reset).forEach(key => {
    if (key.indexOf("Show") !== -1) {
      showFieldList.push(reset[key])
    } else {
      fillFieldList.push(reset[key])
    }
  })
  const isShowEmpt = showFieldList.every(value => value === 0)
  const isFillEmpt = fillFieldList.every(value => value === 0)
  if (isShowEmpt) {
    return {
      validateStatus: "error",
      errorMsg: "至少选择一个字段显示",
    }
  }
  if (isFillEmpt) {
    return {
      validateStatus: "error",
      errorMsg: "至少选择一个字段必填",
    }
  }

  return {
    validateStatus: "sucess",
    errorMsg: "",
  }
}

export function useTourInfo() {
  const [tourInfo, setTourInfo] = useState(() => defaultTourInfo)
  function onChangeTourInfo(key) {
    const keyPrefix = key.slice(0, -4)
    const type = key.slice(-4)

    setTourInfo(pre => {
      const keyValue = pre[key]

      const info = {
        ...pre,
        [key]: keyValue === 0 ? 1 : 0,
      }
      // 必填 ----> 选中必填
      if (type === "Fill" && keyValue === 0) {
        info[keyPrefix + "Show"] = 1
      }
      // 显示 ----> 显示取消
      if (type === "Show" && keyValue === 1) {
        info[keyPrefix + "Fill"] = 0
      }

      return {
        ...info,
        ...validateTourInfo(info),
      }
    })
  }
  /* 校验 */
  function validateCheckTourInfo() {
    return new Promise((resolve, reject) => {
      const { validateStatus } = validateTourInfo(tourInfo)

      setTourInfo(pre => {
        return {
          ...pre,
          ...validateTourInfo(pre),
        }
      })

      if (validateStatus === "sucess") {
        resolve("ok")
      } else {
        resolve("error")
      }
    })
  }
  return {
    tourInfo,
    setTourInfo,
    onChangeTourInfo,
    validateCheckTourInfo,
  }
}
