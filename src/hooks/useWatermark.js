import { useEffect, useRef, useState } from 'react';

export const useWatermark = (enabled = true) => {
  const containerRef = useRef(null);
  const [userInfo] = useState(() => {
    const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
    return userInfo;
  });
  const watermarkText = userInfo 
    ? `${userInfo.name || '未命名用户'}  ${userInfo.phoneNumber?.slice(-4) || '****'}` 
    : '未登录用户';

  useEffect(() => {
    if (!enabled) return;
    if (!containerRef.current) return;

    const container = containerRef.current;

    const existingWatermark = container.querySelector('.watermark');
    if (existingWatermark) existingWatermark.remove();

    // 创建水印画布
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = 200;
    canvas.height = 150;

    // 绘制水印文本
    ctx.font = `18px  Microsoft YaHei UI, Microsoft YaHei UI`;
    ctx.fillStyle = '#000000' ;
    ctx.globalAlpha ='0.35';
    ctx.rotate((-22 * Math.PI) / 180);
    ctx.fillText(watermarkText, 10, 80);

    // 生成背景图 URL
    const watermarkUrl = canvas.toDataURL('image/png');

    // 创建水印容器
    const watermarkDiv = document.createElement('div');
    watermarkDiv.style.position = 'absolute';
    watermarkDiv.style.top = '0';
    watermarkDiv.style.left = '0';
    watermarkDiv.style.width = '100%';
    watermarkDiv.style.height = '100%';
    watermarkDiv.style.backgroundImage = `url(${watermarkUrl})`;
    watermarkDiv.style.backgroundRepeat = 'repeat';
    watermarkDiv.style.pointerEvents = 'none'; 
    watermarkDiv.style.zIndex = '9999';

    // 添加到容器
    containerRef.current.style.position = 'relative';
    containerRef.current.style.zIndex = '9';
    containerRef.current.appendChild(watermarkDiv);

    const handleResize = () => {
      if (container.querySelector('.watermark')) {
        container.removeChild(watermarkDiv);
        container.appendChild(watermarkDiv);
      }
    };

    window.addEventListener('resize', handleResize);
    // 清理函数
    return () => {
      if (watermarkDiv.parentNode === containerRef.current) {
        containerRef.current.removeChild(watermarkDiv);
      }
    };
  }, [watermarkText, enabled ]);

  return containerRef;
};
