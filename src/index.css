/* @tailwind base; */
@tailwind components;
@tailwind utilities;
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-width: 1366px;
}
@media (max-width: 600px) {
  body {
    min-width: 0;
  }
}

::-webkit-scrollbar {
  width: 6px;
}
::-webkit-scrollbar-thumb {
  background-color: #9093994d;
  border-radius: 3px;
}

/* table组件样式 */
.ant-table-thead > tr > th {
  color: #262626;
}
/* .ant-table-tbody > tr > td {
  border-bottom: none;
} */
.ant-table-thead > tr > th::before {
  display: none;
}

.courseTourAdmin.ant-layout-sider {
  background: #263238;
}
.courseTourAdmin .ant-menu-root {
  background: #263238;
}
.courseTourAdmin
  .ant-menu-dark.ant-menu-dark:not(.ant-menu-horizontal)
  .ant-menu-item-selected {
  background: #515b60;
}
.courseTourAdmin .ant-layout-sider-trigger {
  background: #263238;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* .ant-tabs-top > .ant-tabs-nav::before {
  border-bottom: none;
} */
/* tree */
.ant-tree {
  background: transparent;
}

/* menu */
.ant-menu-item {
  /* margin-top: 0 !important; */
}

/* mobile */
.mobileAuto .ant-select-selection-search {
  padding-left: 100px;
}
.mobileAuto .ant-select-selection-search input {
  text-align: right;
}

/* loading */
#loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* background: rgba(0, 0, 0, 0.75); */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  /* font-size: 20px; */
}

/* video::-webkit-media-controls-fullscreen-button {
  display: none;
} */

.headControlPatrolOverlay .ant-popover-inner-content {
  padding: 0 8px;
}

.ant-cascader-menu {
  max-height: 150px;
}
