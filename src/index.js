import React from "react";
import ReactDOM from "react-dom";
import "antd/dist/antd.css";
import "./index.css";
import App from "./App";
import { ConfigProvider, message } from "antd";
import zhCN from "antd/es/locale/zh_CN";
import moment from "moment";
import "moment/locale/zh-cn";
import "webrtc-adapter";
moment.locale("zh-cn");

function injectScript(src) {
  return new Promise((resolve, reject) => {
    const script = document.createElement("script");
    script.src = src;
    script.async = false;
    document.head.appendChild(script);
    script.onload = () => {
      resolve("sucess");
    };
    script.onerror = () => {
      reject("fail");
    };
  });
}
// window.BASEURL = "https://www.ecscc.net";
// window.BASEURL = "http://**************";
// window.BASEURL = "https://ysotjxyt.ysclass.net";
if (process.env.NODE_ENV == "development") {
  window.BASEURL = "http://**************";
} else if (process.env.NODE_ENV == "production") {
  window.BASEURL = "";
}
// if (process.env.NODE_ENV === "production") {
//   window.BASEURL = "";
// }
async function boostrap() {
  await injectScript(
    window.BASEURL + "/yskt/ys/ysConfig/lib/yskjConfig.iife.js"
  );

  await window.yskjConfig.config({
    baseURL: window.BASEURL,
    onError: (value) => {
      message.error(value);
    },
    onSucess: (value) => {
      message.success(value);
    },
  });

  ReactDOM.render(
    <ConfigProvider locale={zhCN}>
      <App />
    </ConfigProvider>,
    document.getElementById("root")
  );
}

boostrap();
console.log("ok");
window.sessionStorage.setItem(
  "vmsLoginConfig",
  JSON.stringify({ webBrowserPlayerVersion: "V4.2.2.10" })
);
