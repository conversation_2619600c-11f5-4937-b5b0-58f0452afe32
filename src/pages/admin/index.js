import React from "react";
import { Outlet } from "react-router-dom";
import AdminMenu from "./menu";
import { PrevContextProvider } from "./../home/<USER>/prevContext";
import styled from "styled-components";
const Root = styled.div`
  height: calc(100vh - 60px);
  display: flex;
  background: #f0f2f5;
  .content {
    flex: 1;
  }
`;
function CourseTourAdmin() {
  return (
    <Root>
      <AdminMenu />
      <div className="content overflow-y-auto">
        <PrevContextProvider>
          <Outlet />
        </PrevContextProvider>
      </div>
    </Root>
  );
}

export default CourseTourAdmin;
