import React, { forwardRef, useImperativeHandle, useState } from "react"
import { Drawer, Timeline } from "antd"
import moment from "moment"
import { CloseOutlined } from "@ant-design/icons"
import { getAdminRecordDetail, getAdminRecordEditDetail } from "@/api"
import styled from "styled-components"
import {FormRes} from "@/components"
import PicBox from "@/pages/common/picBox"
import PreviewTool from "../../common/previewTool"

const Tab = styled.div`
  margin-bottom: 20px;
`
const TabItem = styled.span`
  margin-right: 32px;
  cursor: pointer;
  padding-bottom: 6px;
  border-bottom: 2px solid transparent;
  &.active {
    color: #007aff;
    border-block-color: #007aff;
  }
  &:hover {
    color: #007aff;
  }
`
function logLable(type) {
  switch(type){
    case 1:
      return "创建";
    case 2:
      return "编辑";
    case 3:
      return "编辑量表";
    default:
      return "编辑"
  }
}
const DetailDrawer = (props, ref) => {
  const [active, setActive] = useState(1) //1基础信息 2编辑记录
  const [show, setShow] = useState(false)

  const [info, setInfo] = useState(null)
  const [list, setList] = useState([])
  useImperativeHandle(ref, () => ({
    showDrawer(patrolRecordId) {
      initDetail(patrolRecordId)
      initEditDetail(patrolRecordId)
      setActive(1)
      setShow(true)
    },
  }))
  const initDetail = (patrolRecordId) => {
    getAdminRecordDetail({ patrolRecordId }).then((res) => {
      setInfo(res.data)
    })
  }

  const initEditDetail = (patrolRecordId) => {
    getAdminRecordEditDetail({ patrolRecordId }).then((res) => {
      setList(res.data)
    })
  }
  const handleCloseDrader = () => {
    setShow(false)
  }
  const handleChangeTab = (tab) => {
    setActive(tab)
  }
  return (
    <Drawer
      destroyOnClose
      title="详情"
      width={480}
      closable={false}
      visible={show}
      extra={
        <CloseOutlined onClick={handleCloseDrader} className="cursor-pointer" />
      }
    >
      <Tab>
        <TabItem
          onClick={() => handleChangeTab(1)}
          className={active === 1 ? "active" : ""}
        >
          基础信息
        </TabItem>
        {
          info && info.useTable === 1 && <TabItem
            onClick={() => handleChangeTab(3)}
            className={active === 3 ? "active" : ""}
          >
            量表
          </TabItem>
        }
        <TabItem
          onClick={() => handleChangeTab(2)}
          className={active === 2 ? "active" : ""}
        >
          编辑记录
        </TabItem>
      </Tab>
      {active === 1 && <BasicInfo info={info} />}
      {active === 2 && (
        <Timeline>
          {list.map((item, index) => (
            <Timeline.Item key={index}>
              <span>
                {moment(item.operationTime).format("YYYY-MM-DD HH:mm")}
              </span>
              <span className="ml-4">{item.operatorUserName}</span>
              <span className="ml-4">
                {logLable(item.operationType)}
              </span>
            </Timeline.Item>
          ))}
        </Timeline>
      )}
      {
        active === 3 && <FormRes
          formTemplateAnswerId={info.formTemplateAnswerId} 
          formInfoId={info.formInfoId} 
          formTemplateInfoId={info.formTemplateInfoId} 
        />
      }
    </Drawer>
  )
}

const BasicRoot = styled.div`
  .item {
    margin-bottom: 28px;
    display: flex;
    .content {
      margin-left: 10px;
      flex: 1;
    }
  }
`
function BasicInfo({ info }) {
  const [prevInfo, setPreview] = useState({
    show: false,
    list: [],
    index: -1,
  })

  if (!info) return null

  const handle2Preview = (index) => {
    const list = info.patrolRecordAttrList.map((item) => item.attrPath)
    setPreview({
      list,
      show: true,
      index,
    })
  }
  const handle2ClosePreview = () => {
    setPreview({
      show: false,
      list: [],
      index: -1,
    })
  }
  return (
    <BasicRoot>
      <div className="item">
        <div className="label">人员:</div>
        <div className="content">{info.recordedUserName}</div>
      </div>
      <div className="item">
        <div className="label">时间:</div>
        <div className="content">
          {moment(info.patrolTime).format("YYYY-MM-DD HH:mm")}
        </div>
      </div>
      <div className="item">
        <div className="label">评价:</div>
        <div className="content break-all">{info.patrolComment}</div>
      </div>
      <div className="item">
        <div className="label">截图:</div>
        <div className="content">
          {info.patrolRecordAttrList.map((item, index) => (
            <PicBox
              key={index}
              onPreview={() => handle2Preview(index)}
              src={item.attrPic ? item.attrPic : item.attrPath}
              realPath={item.attrPath}
              hideDelete
            />
          ))}
        </div>
      </div>
      {prevInfo.show && (
        <PreviewTool onClose={handle2ClosePreview} {...prevInfo} />
      )}
    </BasicRoot>
  )
}

export default forwardRef(DetailDrawer)
