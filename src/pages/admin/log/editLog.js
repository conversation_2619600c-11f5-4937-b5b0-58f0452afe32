import React, { useState, useRef, useImperativeHandle, forwardRef } from "react"
import moment from "moment"
import {
  Drawer,
  Form,
  Input,
  Button,
  DatePicker,
  AutoComplete,
  message,
} from "antd"
import {
  PlusOutlined,
  EyeOutlined,
  DeleteOutlined,
  CloseOutlined,
} from "@ant-design/icons"
import { searchByUserName, addPatrolRecord } from "@/api"
import { $upload, $isCdn } from "@/tools"
import styled from "styled-components"
const { Option } = AutoComplete
const UploadList = styled.div``
const UploadItem = styled.div`
  display: inline-block;
  position: relative;
  height: 100px;
  width: 100px;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  margin: 0 12px 12px 0;
  overflow: hidden;
  &:hover .cover {
    display: block;
  }
  .cover {
    transition: all 0.3s;
    display: none;
    background: rgba(0, 0, 0, 0.4);
  }
`
function EditLog(props, ref) {
  const inputRef = useRef()
  const [form] = Form.useForm()
  const [log, setLog] = useState({ show: false, patrolRecordId: 0 })

  const [userInfo, setUserInfo] = useState(null)
  const [attr, setAttr] = useState([])
  const [options, setOptions] = useState([])
  useImperativeHandle(ref, () => ({
    showDrawer(patrolRecordId) {
      setLog({ show: true, patrolRecordId })
    },
  }))
  const onSearch = async (userName) => {
    if (!userName) {
      setOptions([])
    } else {
      const result = await searchByUserName({ userName })
      if (result.code === 0) {
        const data = result.data.map((item) => ({
          value: item.coreUserInfoId,
          label: item.userName,
          phone: item.phone,
        }))
        setOptions(data)
      }
    }
  }
  const onSelect = async (value) => {
    const target = options.find((item) => item.value === value)
    setUserInfo(target)
    form.setFieldsValue({ recordedUserName: target.label })
  }

  const handleCloseDrader = () => {
    form.resetFields()
    setAttr([])
    setUserInfo(null)

    setLog({ ...log, show: false })
  }
  const beforeUpload = (file) => {
    const isImage = file.type.indexOf("image/") !== -1
    const isVideo = file.type.indexOf("video/") !== -1
    const isLt100M = file.size / 1024 / 1024 < 100
    if (!isLt100M && isVideo) {
      message.error("视频文件大小小于100M")
    }
    if (!(isImage || isVideo)) {
      message.error("请上传正确的格式")
    }
    return (isImage || isVideo) && isLt100M
  }
  const fileChange = async (e) => {
    if (!beforeUpload(e.target.files[0])) {
      e.target.value = ""
      return
    }
    const result = await $upload(e)
    if (!result.screenUrl) {
      // 图片
      setAttr([
        ...attr,
        {
          attrPath: result.fileUrl,
          screenUrl: "",
          attrType: 1,
          patrolRecordId: log.patrolRecordId,
        },
      ])
    } else {
      // video
      setAttr([
        ...attr,
        {
          attrPath: result.fileUrl,
          screenUrl: result.screenUrl,
          attrType: 2,
          patrolRecordId: log.patrolRecordId,
        },
      ])
    }
  }
  const handleDeleteAttr = (index) => {
    const newAttr = [...attr]
    newAttr.splice(index, 1)
    setAttr(newAttr)
    message.success("删除成功")
  }
  const submit = async (values) => {
    if (!values) return
    if (!userInfo) {
      return message.error("请选择正确的人员")
    }
    if (userInfo.label !== form.getFieldValue("recordedUserName")) {
      return message.error("请选择正确的人员")
    }
    const patrolRecordAttrList = attr.map((item) => ({
      attrPath: item.attrPath,
      attrType: item.attrType,
      patrolRecordId: item.patrolRecordId,
    }))
    const data = {
      patrolComment: values.patrolComment,
      recordedUserName: values.recordedUserName,
      recordedUserId: userInfo.value,
      patrolRecordId: log.patrolRecordId,
      patrolRecordAttrList,
      patrolTime: moment(values.time).format("YYYY-MM-DD HH:mm:ss"),
    }
    const result = await addPatrolRecord(data)
    if (result.code === 0) {
      message.success("保存成功")
    }
  }
  return (
    <Drawer
      destroyOnClose
      title="添加巡课记录"
      width={480}
      closable={false}
      visible={log.show}
      extra={
        <CloseOutlined onClick={handleCloseDrader} className="cursor-pointer" />
      }
    >
      <input
        ref={(c) => (inputRef.current = c)}
        onChange={fileChange}
        type="file"
        className="hidden"
      />
      <Form
        form={form}
        labelCol={{ span: 3 }}
        wrapperCol={{ span: 21 }}
        onFinish={submit}
        autoComplete="off"
      >
        <Form.Item
          label="人员"
          name="recordedUserName"
          rules={[{ required: true, message: "请输入姓名" }]}
        >
          <AutoComplete
            className="w-full"
            onSelect={onSelect}
            onSearch={onSearch}
            placeholder="请输入姓名搜索"
          >
            {options.map((option) => {
              return (
                <Option key={option.value}>
                  <div className="flex justify-between">
                    <span className="flex-1 truncate">{option.label}</span>
                    <span>{option.phone}</span>
                  </div>
                </Option>
              )
            })}
          </AutoComplete>
        </Form.Item>

        <Form.Item
          label="时间"
          name="time"
          rules={[{ required: true, message: "请选择时间" }]}
        >
          <DatePicker
            placeholder="请选择时间"
            className="w-full"
            showTime
            format="YYYY-MM-DD HH:mm:ss"
          />
        </Form.Item>

        <Form.Item
          label="评价"
          name="patrolComment"
          rules={[{ required: true, message: "请输入评价" }]}
        >
          <Input.TextArea rows={4} maxLength={200} placeholder="请输入评价" />
        </Form.Item>

        <Form.Item label="截图">
          <div className="mt-1 mb-2">
            <div
              onClick={() => inputRef.current.click()}
              className="inline-block mr-3 cursor-pointer"
              style={{ color: "#007AFF" }}
            >
              <PlusOutlined />
              <span> 添加</span>
            </div>
            <span style={{ color: "#BFBFBF" }}>视频文件不超过100mb</span>
          </div>
          <UploadList>
            {attr.map((item, index) => (
              <UploadItem key={index}>
                <img
                  className="w-full h-full"
                  src={$isCdn(item.screenUrl ? item.screenUrl : item.attrPath)}
                  alt=""
                />
                <div className="cover absolute left-0 top-0 right-0 bottom-0">
                  <div className="flex h-full justify-center items-center">
                    <EyeOutlined className="text-white mr-2 text-lg cursor-pointer" />
                    <DeleteOutlined
                      onClick={() => handleDeleteAttr(index)}
                      className="text-white text-lg cursor-pointer"
                    />
                  </div>
                </div>
              </UploadItem>
            ))}
          </UploadList>
        </Form.Item>

        <Form.Item wrapperCol={{ offset: 3, span: 16 }}>
          <Button type="primary" htmlType="submit">
            确定
          </Button>
        </Form.Item>
      </Form>
    </Drawer>
  )
}

export default forwardRef(EditLog)
