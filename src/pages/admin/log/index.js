import React, { useState, useEffect, useRef } from "react";
import {
  Input,
  Button,
  Select,
  Space,
  Table,
  Pagination,
  Divider,
  Tooltip,
  Modal,
  message,
  DatePicker,
  Form,
  Row,
  Col,
  Popover,
  Cascader,
} from "antd";
import {
  getPatrolRecordList,
  deleteRecord,
  getAdminTasks,
  getGradeStageTreeByOrgId,
  getStageGradeByOrgId,
} from "@/api";
import axios from "axios";
import { useSearchParams } from "react-router-dom";
import moment from "moment";
import { FormContextProvider } from "@/components";
import { $hidePhone, getTokenFromCookie } from "@/tools";
import { PlusOutlined, QuestionCircleFilled } from "@ant-design/icons";
import styled from "styled-components";
import TourLog from "./tourLogDrawer";
import TourFreeLog from "./tourFreeLogDrawer";
import ImpModal from "./impModal";
/* 公用HOME Task */
import DetailDrawer from "./detailDrawer";
const { RangePicker } = DatePicker;
const Root = styled.div`
  padding: 16px 24px 24px;
  .title {
    color: #262626;
  }
  .content {
    padding: 24px;
    margin-top: 16px;
    background: #fff;
    border-radius: 4px;
  }
`;

async function getGradeClassInfo(type) {
  const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
  let result;
  if (type === "class") {
    result = await getGradeStageTreeByOrgId({ orgId: userInfo.orgId });
  } else {
    result = await getStageGradeByOrgId({ orgId: userInfo.orgId });
  }
  if (result.code === 0) {
    return result.data || [];
  }
}

function AdminLog() {
  const [params] = useSearchParams();
  const bureauId = params.get("bureauId");

  const impRef = useRef();
  const detailDrawerRef = useRef();
  const tourLogRef = useRef();
  const tourFreeLogRef = useRef();

  const columns = [
    {
      title: "序号",
      width: 60,
      align: "center",
      key: "index",
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: "被记录人",
      dataIndex: "recordedUserName",
      align: "center",
      key: "recordedUserName",
      render: (_, record) => (
        <>
          {_ ? (
            <Popover
              placement="bottomLeft"
              content={
                <div>
                  <div>{_}</div>
                  <div className="my-1">
                    {$hidePhone(record.recordedUserInfoVo?.phone)}
                  </div>
                  <div>{record.recordedUserInfoVo?.orgName}</div>
                </div>
              }
              arrowPointAtCenter
            >
              <span>{_}</span>
            </Popover>
          ) : (
            "--"
          )}
        </>
      ),
    },
    {
      title: "被记录班级",
      width: 160,
      dataIndex: "className",
      align: "center",
      key: "className",
      render: (_) => <span>{_ || "--"}</span>,
      // sorter: (a, b) => a.patrolTime - b.patrolTime,
    },
    {
      title: "被记录年级",
      width: 160,
      dataIndex: "gradeName",
      align: "center",
      key: "gradeName",
      render: (_) => <span>{_ || "--"}</span>,
      // render: _ => moment(_).format("YYYY-MM-DD HH:mm"),
      // sorter: (a, b) => a.patrolTime - b.patrolTime,
    },
    {
      title: "巡课时间",
      width: 160,
      dataIndex: "patrolTime",
      align: "center",
      key: "patrolTime",
      render: (_) => moment(_).format("YYYY-MM-DD HH:mm"),
      sorter: (a, b) => a.patrolTime - b.patrolTime,
    },
    {
      title: "记录人",
      dataIndex: "recordUserName",
      align: "center",
      key: "recordUserName",
      render: (_, record) => (
        <Popover
          placement="bottomLeft"
          content={
            <div>
              <div>{_}</div>
              <div className="my-1">
                {$hidePhone(record.recordUserInfoVo?.phone)}
              </div>
              <div>{record.recordUserInfoVo?.orgName}</div>
            </div>
          }
          arrowPointAtCenter
        >
          <span>{_}</span>
        </Popover>
      ),
    },
    {
      title: "记录提交时间",
      width: 160,
      dataIndex: "recordTime",
      align: "center",
      key: "recordTime",
      render: (_) => moment(_).format("YYYY-MM-DD HH:mm"),
      sorter: (a, b) => a.recordTime - b.recordTime,
    },
    {
      title: "图片",
      dataIndex: "picCount",
      align: "center",
      key: "picCount",
    },
    {
      title: "量表",
      dataIndex: "useTable",
      align: "center",
      key: "useTable",
      render: (_, record) => `${record.useTable === 2 ? "无" : "有"}`,
    },
    {
      title: "视频",
      dataIndex: "videoCount",
      align: "center",
      key: "videoCount",
    },
    {
      title: "评语",
      dataIndex: "patrolComment",
      align: "center",
      key: "patrolComment",
      ellipsis: {
        showTitle: false,
      },
      render: (patrolComment) => (
        <Tooltip placement="topLeft" title={patrolComment}>
          {patrolComment}
        </Tooltip>
      ),
    },
    {
      title: "操作",
      width: 160,
      key: "control",
      align: "center",
      render: (_, record) => (
        <div>
          <span
            onClick={() => handle2Delete(record)}
            className="cursor-pointer text-blue-500"
          >
            删除
          </span>
          <Divider type="vertical" />
          <span
            onClick={() => handle2Edit(record)}
            className="cursor-pointer text-blue-500"
          >
            编辑
          </span>
          <Divider type="vertical" />
          <span
            onClick={() => handle2Detail(record)}
            className="cursor-pointer text-blue-500"
          >
            详情
          </span>
        </div>
      ),
    },
  ];

  const [taskList, setTaskList] = useState([]);

  // const [recordTime, setRecordTime] = useState([])
  const [classGradeValue, setClassGradeValue] = useState(null);
  const [search, setSearch] = useState({
    userType: 1,
    userName: "",
    taskId: -1,
    patrolType: -1,
    recordTime: [],
    patrolTime: [],
    classId: "",
    gradeId: "",
  });

  const [pagination, setPagination] = useState({
    pageNo: 1,
    pageSize: 10,
  });

  const [data, setData] = useState({ list: [], total: 0 });
  const [options, setOptions] = useState([]);
  const [optionShow, setOptionShow] = useState(false);
  useEffect(() => {
    getAdminTasks({ pageSize: 9999, pageNo: 1, taskName: "", date: "" }).then(
      (res) => {
        if (res.code === 0) {
          setTaskList(res.data);
        }
      }
    );
    getGradeClassInfo("class").then((optionList) => {
      if (optionList && optionList.length === 1 && optionList[0].children) {
        setOptions(optionList[0].children);
      } else {
        setOptions(optionList);
      }
    });
  }, []);

  useEffect(() => {
    const data = { ...pagination, ...search };
    if (data.recordTime && data.recordTime[0]) {
      data.recordStartDate = data.recordTime[0].format("YYYY-MM-DD");
      data.recordEndDate = data.recordTime[1].format("YYYY-MM-DD");
      delete data.recordTime;
    }
    if (data.patrolTime && data.patrolTime[0]) {
      data.patrolStartDate = data.patrolTime[0].format("YYYY-MM-DD");
      data.patrolEndDate = data.patrolTime[1].format("YYYY-MM-DD");
      delete data.patrolTime;
    }
    getPatrolRecordList(data).then((res) => {
      if (res.code === 0) {
        setData({
          list: res.data,
          total: res.totalDatas * 1,
        });
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pagination]);

  const handle2Delete = ({ patrolRecordId }) => {
    Modal.confirm({
      title: "确定删除这条巡课记录吗？",
      icon: <QuestionCircleFilled />,
      onOk() {
        deleteRecord({ patrolRecordId }).then(({ code }) => {
          if (code === 0) {
            message.success("删除成功");
            setPagination({ pageNo: 1, pageSize: 10 });
          }
        });
      },
    });
  };
  const handle2Edit = ({ patrolRecordId, taskId }) => {
    if (taskId) {
      handle2AddTaskLog(patrolRecordId, taskId);
    } else {
      handle2AddFreeLog(patrolRecordId);
    }
  };
  const handle2Detail = ({ patrolRecordId }) => {
    detailDrawerRef.current.showDrawer(patrolRecordId);
  };

  const handle2AddTaskLog = (patrolRecordId, taskId) => {
    tourLogRef.current.showDrawer(patrolRecordId, taskId);
  };

  const handle2AddFreeLog = (patrolRecordId) => {
    tourFreeLogRef.current.showDrawer(patrolRecordId);
  };

  const dropdownRender = (menus) => (
    <div>
      {menus}
      <Divider style={{ margin: 0 }} />
      <div style={{ padding: 8, textAlign: "right" }}>
        <Button
          type="primary"
          size="small"
          onClick={() => {
            setOptionShow(false);
          }}
        >
          确定
        </Button>
      </div>
    </div>
  );
  const handleExport = () => {
    let url = "/sd-api/patrol/patrol/record/downloadPatrolRecord";
    let config = {
      method: "get",
      headers: {
        Authorization: getTokenFromCookie(),
        bureauId,
      },
      url,
      responseType: "blob",
    };
    if (process.env.NODE_ENV === "production") {
      config = window.ysCommonToolLib.encryption(config);
    }
    axios(config).then((res) => {
      let blob = new Blob([res.data], {
        type: "application/vnd.ms-excel",
      });
      let url = window.URL.createObjectURL(blob);
      let a = document.createElement("a");
      a.setAttribute("href", url);
      a.setAttribute("download", "记录详情.xls");
      a.click();
      window.URL.revokeObjectURL(url);
    });
  };
  return (
    <Root>
      <div className="title">巡课记录</div>

      <div className="content" style={{ paddingBottom: 0 }}>
        <Form
          // form={form}
          name="advanced_search"
          className="ant-advanced-search-form"
          labelCol={{ span: 6 }}
        >
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item label="搜索">
                <Input.Group className="flex">
                  <Select
                    onSelect={(userType) => {
                      setSearch((pre) => ({
                        ...pre,
                        userType,
                      }));
                    }}
                    value={search.userType}
                    style={{ width: 90 }}
                  >
                    <Select.Option value={1}>记录</Select.Option>
                    <Select.Option value={2}>被记录</Select.Option>
                  </Select>
                  <Input
                    style={{ flex: 1 }}
                    placeholder="请输入姓名搜索"
                    value={search.userName}
                    onChange={(e) => {
                      setSearch((pre) => ({
                        ...pre,
                        userName: e.target.value,
                      }));
                    }}
                  />
                </Input.Group>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="任务名称">
                <Select
                  showSearch
                  onSelect={(taskId) => {
                    setSearch((pre) => ({
                      ...pre,
                      taskId,
                    }));
                  }}
                  value={search.taskId}
                  placeholder="请选择任务名称"
                  className="w-56"
                  filterOption={(input, option) =>
                    option.children.toLowerCase().includes(input.toLowerCase())
                  }
                  optionFilterProp="children"
                >
                  <Select.Option key={-1} value={-1}>
                    全部任务
                  </Select.Option>
                  {taskList.map((item) => (
                    <Select.Option key={item.taskId} value={item.taskId}>
                      {item.taskName}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="类型">
                <Select
                  onSelect={(patrolType) => {
                    setSearch((pre) => ({
                      ...pre,
                      patrolType,
                    }));
                  }}
                  value={search.patrolType}
                  className="w-56"
                >
                  <Select.Option value={-1}>全部</Select.Option>
                  <Select.Option value={1}>任务巡课</Select.Option>
                  <Select.Option value={2}>自由巡课</Select.Option>
                </Select>
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item label="提交时间">
                <RangePicker
                  onChange={(_) => {
                    setSearch((pre) => ({
                      ...pre,
                      recordTime: _,
                    }));
                  }}
                  value={search.recordTime}
                  // format="YYYY-MM-DD"
                  className="w-full"
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="巡课时间">
                <RangePicker
                  onChange={(_) => {
                    setSearch((pre) => ({
                      ...pre,
                      patrolTime: _,
                    }));
                  }}
                  value={search.patrolTime}
                  format="YYYY-MM-DD"
                  style={{ width: "100%" }}
                />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item label="年级/班级">
                <Cascader
                  value={classGradeValue}
                  onChange={(value, selectedOptions) => {
                    const classObj = selectedOptions?.find(
                      (item) => item.nodeType === 3
                    );
                    const gradeObj = selectedOptions?.find(
                      (item) => item.nodeType === 2
                    );
                    setClassGradeValue(value);
                    setSearch((pre) => ({
                      ...pre,
                      classId: classObj?.id,
                      gradeId: value && value.length === 2 ? gradeObj?.id : "",
                    }));
                  }}
                  open={optionShow}
                  onDropdownVisibleChange={(value) => {
                    setOptionShow(value);
                  }}
                  changeOnSelect
                  options={options}
                  dropdownRender={dropdownRender}
                  placeholder="请选择"
                  fieldNames={{
                    label: "name",
                    value: "id",
                    children: "children",
                  }}
                />
              </Form.Item>
            </Col>

            <Col>
              <Form.Item
                wrapperCol={{
                  offset: 10,
                  span: 6,
                }}
              >
                <Space>
                  <Button
                    onClick={() => {
                      setPagination({
                        ...pagination,
                        pageNo: 1,
                      });
                    }}
                    type="primary"
                  >
                    查询
                  </Button>
                  <Button
                    onClick={() => {
                      setClassGradeValue(null);

                      setSearch({
                        userType: 1,
                        userName: "",
                        taskId: -1,
                        patrolType: -1,
                        recordTime: [],
                        patrolTime: [],
                        classId: "",
                        gradeId: "",
                      });
                      setPagination({
                        ...pagination,
                        pageNo: 1,
                      });
                    }}
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>

      <div className="content">
        <div className="flex justify-between items-center">
          <div>记录列表</div>
          <Space>
            <Button onClick={handleExport}>导出</Button>
            <Button
              onClick={() => {
                impRef.current.showModal();
              }}
            >
              导入
            </Button>
            <Button
              onClick={() => handle2AddFreeLog(null)}
              icon={<PlusOutlined />}
              type="primary"
            >
              添加
            </Button>
          </Space>
        </div>
        <Table
          className="mt-5"
          rowKey={(columns) => columns.patrolRecordId}
          dataSource={data.list}
          columns={columns}
          pagination={false}
        />
        <div className="text-right mt-5">
          <Pagination
            className="inline-block"
            current={pagination.pageNo}
            pageSize={pagination.pageSize}
            onChange={(page, pageSize) =>
              setPagination({ pageNo: page, pageSize })
            }
            total={data.total}
            showSizeChanger
            showQuickJumper
            showTotal={(total) => `总共 ${total} 条`}
          />
        </div>
      </div>

      <FormContextProvider>
        <TourLog
          onSuccess={() => setPagination({ ...pagination })}
          ref={(c) => (tourLogRef.current = c)}
        />
      </FormContextProvider>

      <FormContextProvider>
        <TourFreeLog
          onSuccess={() => setPagination({ ...pagination })}
          ref={(c) => (tourFreeLogRef.current = c)}
        />
      </FormContextProvider>

      <DetailDrawer ref={(c) => (detailDrawerRef.current = c)} />
      <ImpModal
        onSuccess={() => setPagination({ ...pagination })}
        ref={(c) => (impRef.current = c)}
      />
    </Root>
  );
}

export default AdminLog;
