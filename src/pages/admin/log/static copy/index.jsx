import React, { useState, useEffect } from "react";
import {
  Input,
  Button,
  Space,
  Table,
  Pagination,
  Form,
  Row,
  Col,
  Select,
} from "antd";
import axios from "axios";
import { schoolCalendars, getPatrolStatistics } from "@/api";

import { getUrlQuery, getTokenFromCookie } from "@/tools";

import styled from "styled-components";

/* 公用HOME Task */

const Root = styled.div`
  padding: 16px 24px 24px;
  .title {
    color: #262626;
  }
  .content {
    padding: 24px;
    margin-top: 16px;
    background: #fff;
    border-radius: 4px;
  }
`;

function AdminLogStatic() {
  const columns = [
    {
      title: "序号",
      width: 60,
      align: "center",
      dataIndex: "index",
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: "姓名",
      align: "center",
      dataIndex: "userName",
    },
    {
      title: "手机号",
      align: "center",
      dataIndex: "phone",
    },
    {
      title: "机构",
      align: "center",
      dataIndex: "orgName",
    },
    {
      title: "量表数",
      align: "center",
      dataIndex: "tableNum",
      sorter: (a, b) => a.tableNum - b.tableNum,
    },
    {
      title: "平均分",
      align: "center",
      dataIndex: "averageScore",
      sorter: (a, b) => a.averageScore - b.averageScore,
      render: (_) => Number(_).toFixed(2),
    },
  ];

  const [data, setData] = useState({ list: [], total: 0 });
  const [schoolYearList, setSchoolYearList] = useState([]);

  const [pagination, setPagination] = useState({
    currPage: 1,
    pageSize: 10,
  });

  const [filterData, setFilterData] = useState({
    keyWord: "",
    startTime: "",
    endTime: "",
    currentYear: "",
  });

  useEffect(() => {
    const { keyWord, startTime, endTime } = filterData;
    getPatrolStatistics({
      keyWord,
      startTime,
      endTime,
      ...pagination,
    }).then((result) => {
      if (result.code === 0) {
        setData({
          list: result.data.records,
          total: Number(result.data.total),
        });
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pagination]);

  useEffect(() => {
    schoolCalendars({
      pageIndex: 1,
      pageSize: 999,
      orgId: getUrlQuery("bureauId"),
    }).then((res) => {
      if (res.code === 0) {
        setSchoolYearList(res.data);
      }
    });
  }, []);

  async function handle2Export() {
    let config = {
      method: "get",
      params: {
        startTime: filterData.startTime,
        endTime: filterData.endTime,
        keyWord: filterData.keyWord,
      },
      headers: {
        Authorization: getTokenFromCookie(),
        bureauId: getUrlQuery("bureauId"),
      },
      url: `/sd-api/patrol/statistics/export.do`,
      responseType: "blob",
    };
    if (process.env.NODE_ENV === "production") {
      config = window.ysCommonToolLib.encryption(config);
    }
    axios(config).then((res) => {
      let blob = new Blob([res.data], {
        type: "application/vnd.ms-excel",
      });
      let url = window.URL.createObjectURL(blob);
      let a = document.createElement("a");
      a.setAttribute("href", url);
      a.setAttribute("download", "mould.xls");
      a.click();
      window.URL.revokeObjectURL(url);
    });
  }

  return (
    <Root>
      <div className="title">巡课统计</div>

      <div className="content" style={{ paddingBottom: 0 }}>
        <Form
          name="advanced_search"
          className="ant-advanced-search-form"
          labelCol={{ span: 6 }}
        >
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item label="搜索">
                <Input
                  placeholder="姓名或手机号"
                  value={filterData.keyWord}
                  onChange={(e) => {
                    setFilterData((pre) => ({
                      ...pre,
                      keyWord: e.target.value,
                    }));
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="学期">
                <Select
                  value={filterData.currentYear}
                  onSelect={(id) => {
                    if (id) {
                      const targetYear = schoolYearList.find(
                        (item) => item.id === id
                      );
                      setFilterData((pre) => ({
                        ...pre,
                        currentYear: id,
                        startTime: targetYear.startDate,
                        endTime: targetYear.holidayDate,
                      }));
                    } else {
                      setFilterData((pre) => ({
                        ...pre,
                        currentYear: id,
                        startTime: "",
                        endTime: "",
                      }));
                    }
                  }}
                >
                  <Select.Option value={""}>全部学期</Select.Option>
                  {schoolYearList.map((item) => (
                    <Select.Option key={item.id} value={item.id}>
                      {item.schoolYear}{" "}
                      {item.schoolTerm === "0" ? "上学期" : "下学期"}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col>
              <Form.Item
                wrapperCol={{
                  span: 6,
                }}
              >
                <Space>
                  <Button
                    onClick={() => {
                      setPagination({
                        ...pagination,
                        currPage: 1,
                      });
                    }}
                    type="primary"
                  >
                    查询
                  </Button>
                  <Button
                    onClick={() => {
                      setFilterData((pre) => ({
                        keyWord: "",
                        startTime: "",
                        endTime: "",
                        currentYear: "",
                      }));
                      setPagination({
                        ...pagination,
                        currPage: 1,
                      });
                    }}
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>

      <div className="content">
        <div className="flex justify-between items-center">
          <div>统计列表</div>
          <Button onClick={handle2Export} type="primary">
            <i
              className="iconfont icondaochu1"
              style={{ verticalAlign: "middle", marginRight: 4 }}
            ></i>
            导出
          </Button>
        </div>
        <Table
          className="mt-5"
          dataSource={data.list}
          columns={columns}
          pagination={false}
        />
        <div className="text-right mt-5">
          <Pagination
            className="inline-block"
            current={pagination.currPage}
            pageSize={pagination.pageSize}
            onChange={(page, pageSize) => {
              setPagination({ currPage: page, pageSize });
            }}
            total={data.total}
            showSizeChanger
            showQuickJumper
            showTotal={(total) => `总共 ${total} 条`}
          />
        </div>
      </div>
    </Root>
  );
}

export default AdminLogStatic;
