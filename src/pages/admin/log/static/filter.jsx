import { Input, Button, Space, Form, Row, Col, Select, DatePicker } from "antd";
import { useState, useEffect } from "react";
import moment from "moment";
import { schoolCalendars } from "@/api";
import { getUrlQuery } from "@/tools";
export default function Filter({ touredUserData, onChangeFilter }) {
  const [schoolYearList, setSchoolYearList] = useState([]);
  const [dateType, setDateType] = useState("1");

  useEffect(() => {
    schoolCalendars({
      pageIndex: 1,
      pageSize: 999,
      orgId: getUrlQuery("bureauId"),
    }).then((res) => {
      if (res.code === 0) {
        setSchoolYearList(res.data);
      }
    });
  }, []);

  const Node =
    dateType === "1" ? (
      <Select
        style={{ width: "70%" }}
        value={touredUserData.currentYear}
        placeholder="请选择"
        onSelect={(id) => {
          if (id) {
            const targetYear = schoolYearList.find((item) => item.id === id);
            onChangeFilter({
              currentYear: id,
              startTime: targetYear.startDate,
              endTime: targetYear.holidayDate,
              pageNo: 1,
              run: true,
            });
          } else {
            onChangeFilter({
              currentYear: "",
              startTime: "",
              endTime: "",
              pageNo: 1,
              run: true,
            });
          }
        }}
      >
        <Select.Option value={""}>全部学期</Select.Option>
        {schoolYearList.map((item) => (
          <Select.Option key={item.id} value={item.id}>
            {item.schoolYear} {item.schoolTerm === "0" ? "上学期" : "下学期"}
          </Select.Option>
        ))}
      </Select>
    ) : (
      <DatePicker.RangePicker
        style={{ width: "70%" }}
        value={
          touredUserData.startTime
            ? [moment(touredUserData.startTime), moment(touredUserData.endTime)]
            : null
        }
        onChange={(v) => {
          if (v) {
            onChangeFilter({
              currentYear: "",
              startTime: v[0].format("YYYY-MM-DD"),
              endTime: v[1].format("YYYY-MM-DD"),
              pageNo: 1,
              run: true,
            });
          } else {
            onChangeFilter({
              currentYear: "",
              startTime: "",
              endTime: "",
              pageNo: 1,
              run: true,
            });
          }
        }}
      />
    );

  return (
    <div>
      <Form
        name="advanced_search"
        className="ant-advanced-search-form"
        labelCol={{ span: 6 }}
      >
        <Row gutter={24}>
          <Col span={6}>
            <Form.Item label="搜索">
              <Input
                placeholder="姓名或手机号"
                value={touredUserData.keyWord}
                onChange={(e) => {
                  onChangeFilter({
                    keyWord: e.target.value,
                  });
                }}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="筛选">
              <Input.Group compact>
                <Select
                  style={{ width: "30%" }}
                  value={dateType}
                  onChange={(e) => {
                    onChangeFilter({
                      currentYear: "",
                      startTime: "",
                      endTime: "",
                    });
                    setDateType(e);
                  }}
                >
                  <Select.Option value="1">学期</Select.Option>
                  <Select.Option value="2">日期</Select.Option>
                </Select>
                {Node}
              </Input.Group>
            </Form.Item>
          </Col>
          <Col>
            <Form.Item
              wrapperCol={{
                span: 6,
              }}
            >
              <Space>
                <Button
                  onClick={() => {
                    onChangeFilter({
                      pageNo: 1,
                      run: true,
                    });
                  }}
                  type="primary"
                >
                  查询
                </Button>
                <Button
                  onClick={() => {
                    onChangeFilter({
                      currentYear: "",
                      pageNo: 1,
                      keyWord: "",
                      startTime: "",
                      endTime: "",
                      run: true,
                    });
                  }}
                >
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  );
}
