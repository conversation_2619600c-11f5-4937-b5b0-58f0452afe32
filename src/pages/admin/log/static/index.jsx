import React, { useState, useEffect, useRef } from "react";
import {
  Input,
  Button,
  Space,
  Table,
  Pagination,
  Form,
  Row,
  Col,
  Select,
  Tabs,
} from "antd";
import axios from "axios";
import {
  schoolCalendars,
  getPatrolStatistics,
  statisticsbyrecorded,
  statisticsbyrecord,
} from "@/api";
import { getUrlQuery, getTokenFromCookie } from "@/tools";
import TouredUser from "./touredUser";
import LogUser from "./logUser";
import Filter from "./filter";
import Show from "@/components/show";

import styled from "styled-components";

/* 公用HOME Task */

const Root = styled.div`
  padding: 16px 24px 24px;
  .title {
    color: #262626;
  }
  .content {
    padding: 8px 24px 0;
    margin-top: 16px;
    background: #fff;
    border-radius: 4px;
  }
`;

function AdminLogStatic() {
  const [tabActive, setActive] = useState("1");
  const tabActibeRef = useRef("1");

  const [touredUserData, setTouredUserData] = useState({
    list: [],
    tital: 0,

    pageNo: 1,
    pageSize: 10,
    keyWord: "",
    currentYear: "",
    startTime: "",
    endTime: "",
    type: 0, // 0.平均分 1.单次最高 2.单次最低
  });

  function onChangeTab(key) {
    setActive(key);
    tabActibeRef.current = key;
    onChangeFilter({
      list: [],
      tital: 0,
      pageNo: 1,
      pageSize: 10,
      keyWord: "",
      currentYear: "",
      startTime: "",
      endTime: "",
      type: 0,
      run: true,
    });
  }
  function onChangeFilter(val) {
    setTouredUserData((pre) => {
      const data = {
        ...pre,
        ...val,
      };

      if (val.run) {
        onFetchData(data);
      }

      return data;
    });
  }
  function onFetchData(data) {
    if (tabActibeRef.current === "2") {
      getTourStatistics(data);
      return;
    }
    if (data.type === 0) {
      getTouredAvage(data);
    } else {
      getStatistics(data);
    }
  }
  /* 单次 */
  async function getStatistics(data) {
    const params = {
      currPage: data.pageNo,
      pageSize: data.pageSize,
      keyWord: data.keyWord,
      startTime: data.startTime,
      endTime: data.endTime,
      type: data.type,
      order: 2,
    };
    const result = await statisticsbyrecorded(params);
    if (result.code === 0) {
      setTouredUserData((pre) => ({
        ...pre,
        list: result.data.records,
        total: Number(result.data.total),
      }));
    }
  }
  /* 获取平均分 */
  async function getTouredAvage(data) {
    const params = {
      currPage: data.pageNo,
      pageSize: data.pageSize,
      keyWord: data.keyWord,
      startTime: data.startTime,
      endTime: data.endTime,
      type: data.type,
    };
    const result = await getPatrolStatistics(params);
    if (result.code === 0) {
      setTouredUserData((pre) => ({
        ...pre,
        list: result.data.records,
        total: Number(result.data.total),
      }));
    }
  }
  /* 记录人 */
  async function getTourStatistics(data) {
    const params = {
      currPage: data.pageNo,
      pageSize: data.pageSize,
      keyWord: data.keyWord,
      startDate: data.startTime,
      endDate: data.endTime,
    };
    const result = await statisticsbyrecord(params);
    if (result.code === 0) {
      setTouredUserData((pre) => ({
        ...pre,
        list: result.data.records,
        total: Number(result.data.total),
      }));
    }
  }

  useEffect(() => {
    getTouredAvage(touredUserData);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Root>
      <div className="title">巡课统计</div>

      <div className="content">
        <Tabs
          activeKey={tabActive}
          onChange={onChangeTab}
          destroyInactiveTabPane
        >
          <Tabs.TabPane tab="被巡查人" key="1">
            <Filter
              touredUserData={touredUserData}
              onChangeFilter={onChangeFilter}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="记录人" key="2">
            <Filter
              touredUserData={touredUserData}
              onChangeFilter={onChangeFilter}
            />
          </Tabs.TabPane>
        </Tabs>
      </div>

      <Show when={tabActive === "1"}>
        <TouredUser
          touredUserData={touredUserData}
          onChangeFilter={onChangeFilter}
        />
      </Show>

      <Show when={tabActive === "2"}>
        <LogUser
          touredUserData={touredUserData}
          onChangeFilter={onChangeFilter}
        />
      </Show>
    </Root>
  );
}

export default AdminLogStatic;
