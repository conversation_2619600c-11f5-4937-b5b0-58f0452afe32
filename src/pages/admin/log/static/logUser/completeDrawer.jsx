import React, { useState, useEffect } from "react";
import { Table, Pagination } from "antd";
import styled from "styled-components";
import { statisticsbycomplete } from "@/api";

const Root = styled.div``;
export default function CompleteDrawer({ userId, touredUserData }) {
  const column = [
    {
      title: "序号",
      width: 60,
      align: "center",
      dataIndex: "index",
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: "任务",
      align: "center",
      dataIndex: "task_name",
    },

    {
      title: "完成情况",
      align: "center",
      dataIndex: "complete_count",
      width: 140,
      render: (_, record) => {
        const { patrol_record_ask, record_count = 0 } = record;
        if (!patrol_record_ask) {
          return <span>{record_count}</span>;
        }
        return (
          <span>
            {record_count} / {patrol_record_ask}
          </span>
        );
      },
    },
  ];

  const [data, setData] = useState({
    list: [],
    total: 1,
  });
  const [pagination, setPagination] = useState({
    pageNo: 1,
    pageSize: 10,
  });

  async function getCompleteList(userId, currPage, pageSize) {
    if (!userId) return;
    const result = await statisticsbycomplete({
      userId,
      currPage,
      pageSize,
      startDate: touredUserData.startTime,
      endDate: touredUserData.endTime,
    });
    if (result.code === 0) {
      setData({
        list: result.data.records,
        total: result.data.total,
      });
    }
  }

  useEffect(() => {
    getCompleteList(userId, pagination.pageNo, pagination.pageSize);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId, pagination.pageNo, pagination.pageSize]);
  return (
    <Root>
      <Table dataSource={data.list} columns={column} pagination={false} />
      <div className="text-right mt-5">
        <Pagination
          className="inline-block"
          current={pagination.pageNo}
          pageSize={pagination.pageSize}
          onChange={(page, pageSize) => {
            setPagination({
              pageNo: page,
              pageSize,
            });
          }}
          total={data.total}
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `总共 ${total} 条`}
        />
      </div>
    </Root>
  );
}
