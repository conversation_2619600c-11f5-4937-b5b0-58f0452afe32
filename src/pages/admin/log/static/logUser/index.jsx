import { Table, Pagination, Drawer } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import styled from "styled-components";
import { $hidePhone } from "@/tools";
import { useState } from "react";
import CompleteDrawer from "./completeDrawer";

const Root = styled.div`
  background: #ffffff;
  border-radius: 4px;
  padding: 16px 24px;
  margin-top: 16px;
`;

export default function LogUser({ touredUserData, onChangeFilter }) {
  const column = [
    {
      title: "序号",
      width: 60,
      align: "center",
      dataIndex: "index",
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: "姓名",
      align: "center",
      dataIndex: "userName",
    },
    {
      title: "手机号",
      align: "center",
      dataIndex: "phone",
      render: (_) => <span>{$hidePhone(_)}</span>,
    },
    {
      title: "机构",
      align: "center",
      dataIndex: "displayName",
    },
    {
      title: "任务总数",
      align: "center",
      dataIndex: "task_count",
    },
    {
      title: "任务记录总数",
      align: "center",
      dataIndex: "record_count",
    },
    {
      title: "完成情况",
      align: "center",
      dataIndex: "complete_count",
      render: (_, record) => {
        const { complete_count, patrol_record_ask, ask_count } = record;
        if (Number(patrol_record_ask) === 0) {
          return "-";
        }
        let p = (Number(complete_count) / Number(ask_count)) * 100;
        if (p >= 100) {
          return (
            <span
              onClick={() => {
                setCompleteInfo({ show: true, userId: record.user_id });
              }}
              style={{ color: "#17BE6B", cursor: "pointer" }}
            >
              已完成 ({p.toFixed(2)}%)
            </span>
          );
        }
        return (
          <span
            onClick={() => {
              setCompleteInfo({ show: true, userId: record.user_id });
            }}
            style={{ color: "#FFAA00", cursor: "pointer" }}
          >
            未完成 ({p.toFixed(2)}%)
          </span>
        );
      },
    },
  ];

  const [completeInfo, setCompleteInfo] = useState({
    show: false,
    userId: "",
  });

  return (
    <Root>
      <div>记录人列表</div>
      <Table
        className="mt-5"
        dataSource={touredUserData.list}
        columns={column}
        pagination={false}
      />
      <div className="text-right mt-5">
        <Pagination
          className="inline-block"
          current={touredUserData.pageNo}
          pageSize={touredUserData.pageSize}
          onChange={(page, pageSize) => {
            onChangeFilter({
              pageNo: page,
              pageSize,
              run: true,
            });
          }}
          total={touredUserData.total}
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `总共 ${total} 条`}
        />
      </div>

      <Drawer
        destroyOnClose
        title="完成情况"
        width={708}
        closable={false}
        visible={completeInfo.show}
        extra={
          <CloseOutlined
            onClick={() => {
              setCompleteInfo({ show: false, userId: "" });
            }}
            className="cursor-pointer"
          />
        }
      >
        <CompleteDrawer
          userId={completeInfo.userId}
          touredUserData={touredUserData}
        />
      </Drawer>
    </Root>
  );
}
