import { Button, Space, Table, Pagination } from "antd";
import { useRef } from "react";
import styled from "styled-components";
import DetailDrawer from "./../../detailDrawer";
import { statisticExport } from "@/api";
import { getUrlQuery, getTokenFromCookie } from "@/tools";
import axios from "axios";

const Root = styled.div`
  background: #ffffff;
  border-radius: 4px;
  padding: 16px 24px;
  margin-top: 16px;
  .head {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .btn {
    color: #595959;
    font-size: 14px;
    padding: 4px 16px;
    cursor: pointer;
    &.active {
      background: #f0f1f3;
      border-radius: 900px;
      color: #007aff;
    }
  }
`;

export default function TouredUser({ touredUserData, onChangeFilter }) {
  const detailDrawerRef = useRef();
  function getColumns() {
    const columns = [
      {
        title: "序号",
        width: 60,
        align: "center",
        dataIndex: "index",
        render: (_, record, index) => `${index + 1}`,
      },
      {
        title: "姓名",
        align: "center",
        dataIndex: "userName",
      },
      {
        title: "手机号",
        align: "center",
        dataIndex: "phone",
      },
      {
        title: "机构",
        align: "center",
        dataIndex: "orgName",
      },
    ];
    if (touredUserData.type == 0) {
      columns.push(
        ...[
          {
            title: "量表数",
            align: "center",
            dataIndex: "tableNum",
            sorter: (a, b) => a.tableNum - b.tableNum,
          },
          {
            title: "平均分",
            align: "center",
            dataIndex: "averageScore",
            sorter: (a, b) => a.averageScore - b.averageScore,
            render: (_) => Number(_).toFixed(2),
          },
        ]
      );
    } else {
      columns.push(
        ...[
          {
            title: "得分",
            align: "center",
            dataIndex: "score",
            sorter: (a, b) => a.score - b.score,
          },
          {
            title: "操作",
            align: "center",
            key: "control",
            width: 80,
            render: (_, record) => (
              <span
                onClick={() => {
                  handle2Detail(record);
                }}
                className="cursor-pointer text-blue-500"
              >
                详情
              </span>
            ),
          },
        ]
      );
    }

    return columns;
  }

  function handle2Detail({ recordId }) {
    detailDrawerRef.current.showDrawer(recordId);
  }

  async function handle2Export() {
    /* 平均分导出 */
    let downloadName = "平均分.xls";
    let config = {
      method: "get",
      params: {
        endTime: touredUserData.endTime,
        startTime: touredUserData.startTime,
        keyWord: touredUserData.keyWord,
      },
      headers: {
        Authorization: getTokenFromCookie(),
        bureauId: getUrlQuery("bureauId"),
      },
      url: `/sd-api/patrol/statistics/export.do`,
      responseType: "blob",
    };
    if (touredUserData.type !== 0) {
      if (touredUserData.type === 1) {
        downloadName = "单次最高.xls";
      } else {
        downloadName = "单次最低.xls";
      }
      /* 最低最高导出 */
      config.params = {
        endTime: touredUserData.endTime,
        startTime: touredUserData.startTime,
        keyWord: touredUserData.keyWord,
        type: touredUserData.type,
        order: 2,
      };
      config.url = `/sd-api/patrol/statisticsbyrecorded/export.do.do`;
    }

    if (process.env.NODE_ENV === "production") {
      config = window.ysCommonToolLib.encryption(config);
    }
    axios(config).then((res) => {
      let blob = new Blob([res.data], {
        type: "application/vnd.ms-excel",
      });
      let url = window.URL.createObjectURL(blob);
      let a = document.createElement("a");
      a.setAttribute("href", url);
      a.setAttribute("download", downloadName);
      a.click();
      window.URL.revokeObjectURL(url);
    });
  }

  return (
    <Root>
      <div className="head">
        <Space size={30}>
          <div
            onClick={() => onChangeFilter({ type: 0, pageNo: 1, run: true })}
            className={touredUserData.type === 0 ? "active btn" : "btn"}
          >
            平均分
          </div>
          <div
            onClick={() => onChangeFilter({ type: 1, pageNo: 1, run: true })}
            className={touredUserData.type === 1 ? "active btn" : "btn"}
          >
            单次最高
          </div>
          <div
            onClick={() => onChangeFilter({ type: 2, pageNo: 1, run: true })}
            className={touredUserData.type === 2 ? "active btn" : "btn"}
          >
            单次最低
          </div>
        </Space>
        <Button type="primary" onClick={handle2Export}>
          <i
            className="iconfont icondaochu1"
            style={{ verticalAlign: "middle", marginRight: 4 }}
          ></i>
          导出
        </Button>
      </div>

      <Table
        className="mt-5"
        dataSource={touredUserData.list}
        columns={getColumns()}
        pagination={false}
      />
      <div className="text-right mt-5">
        <Pagination
          className="inline-block"
          current={touredUserData.pageNo}
          pageSize={touredUserData.pageSize}
          onChange={(page, pageSize) => {
            onChangeFilter({
              pageNo: page,
              pageSize,
              run: true,
            });
          }}
          total={touredUserData.total}
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `总共 ${total} 条`}
        />
      </div>

      <DetailDrawer ref={(c) => (detailDrawerRef.current = c)} />
    </Root>
  );
}
