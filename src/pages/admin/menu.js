import React, { useContext } from "react";
import { useLocation } from "react-router-dom";
import { Menu, Layout } from "antd";
import { useRouterGo } from "@/hooks/useRouterGo";
import {
  SettingOutlined,
  ProfileOutlined,
  FileSearchOutlined,
  ContainerOutlined
} from "@ant-design/icons";
import { MenuContext } from "@/pages/layout";
const { Sider } = Layout;

function BackIcon() {
  return (
    <i className="iconfont iconfanhui  anticon-profile ant-menu-item-icon"></i>
  );
}

function AdminMenu() {
  const menu = useContext(MenuContext);
  const navigate = useRouterGo();
  const { pathname } = useLocation();
  const [collapsed, setCollapse] = React.useState(false);
  const handleChangePath = ({ key }) => {
    navigate(key);
  };
  return (
    <Sider
      className="courseTourAdmin"
      collapsible
      collapsed={collapsed}
      onCollapse={() => setCollapse(!collapsed)}
    >
      <Menu
        theme="dark"
        defaultSelectedKeys={[pathname]}
        onClick={handleChangePath}
        mode="inline"
      >
        <Menu.Item key="/" icon={<BackIcon />}>
          回到用户端
        </Menu.Item>
        {menu.includes(1) ? (
          <Menu.Item key="/admin/tasks" icon={<ProfileOutlined />}>
            巡课任务
          </Menu.Item>
        ) : null}

        {/* {menu.includes(2) ? (
          <Menu.Item
            key="/admin/log"
            icon={<FileSearchOutlined />}
          >
            巡课记录
          </Menu.Item>
        ) : null} */}

        {menu.includes(2) ? (
          <Menu.SubMenu
            key="/admin/log"
            title="巡课记录"
            icon={<FileSearchOutlined />}
          >
            <Menu.Item key="/admin/log/detail">详细记录</Menu.Item>
            <Menu.Item key="/admin/log/static">巡课统计</Menu.Item>
          </Menu.SubMenu>
        ) : null}

        {menu.includes(4) ? (
          <Menu.Item key="/admin/operationLog" icon={<ContainerOutlined />}>
            操作日志
          </Menu.Item>
        ) : null}

        {menu.includes(3) ? (
          <Menu.SubMenu
            key="/admin/setting"
            title="设置"
            icon={<SettingOutlined />}
          >
            <Menu.Item key="/admin/setting/quickreply">快捷回复</Menu.Item>
            <Menu.Item key="/admin/setting/freetour">自由巡课</Menu.Item>
            <Menu.Item key="/admin/setting/sys">系统设置</Menu.Item>
          </Menu.SubMenu>
        ) : null}
      </Menu>
    </Sider>
  );
}

export default AdminMenu;
