import React, { useState, useRef, useEffect } from "react";
import {
  Button,
  message,
  Form,
  Row,
  Col,
  Input,
  Select,
  DatePicker,
  Table,
  Pagination,
} from "antd";
import styled from "styled-components";
import moment from "moment";
import { logList } from "@/api";

const Root = styled.div`
  padding: 16px 24px 24px;
  .title {
    color: #262626;
  }
  .tips {
    margin-left: 12px;
    color: #8c8c8c;
  }
  .filterBox {
    padding: 24px;
    margin-top: 16px;
    background: #fff;
    border-radius: 4px;
  }
  .content {
    padding: 24px;
    margin-top: 16px;
    background: #fff;
    border-radius: 4px;
  }
`;
export default function Operationlog() {
  const columns = [
    {
      title: "操作时间",
      key: "operationTime",
      dataIndex: "operationTime",
      width: 220,
      render: (_, record) => (
        <div>
          <span>{moment(record.operationTime).format("YYYY-MM-DD HH:mm")}</span>
        </div>
      ),
    },
    {
      title: "操作人",
      dataIndex: "userName",
      key: "userName",
    },
    {
      title: "操作类型",
      dataIndex: "type",
      key: "type",
      render: (_, record) => (
        <div>
          <span>
            {record.type === 1
              ? "添加自由巡课权限"
              : record.type === 2
              ? "删除自由巡课权限"
              : record.type === 3
              ? "编辑任务巡课权限"
              : "删除任务巡课权限"}
          </span>
        </div>
      ),
    },
    {
      title: "影响人员",
      key: "users",
      dataIndex: "users",
    },
    {
      title: "影响任务",
      dataIndex: "tasks",
      key: "tasks",
      render: (_, record) => (
        <div>
          {record.tasks || "--"}
        </div>
      ),
    },
  ];
  const [form] = Form.useForm();
  const [pagination, setPagination] = useState({
    keywords: "",
    date: "",
    type: null,
    pageNo: 1,
    pageSize: 10,
  });
  const onFinish = (values) => {
    setPagination({
      keywords: values.keywords,
      date: values.date,
      type: values.type,
      pageNo: 1,
      pageSize: 10,
    });
  };
  const onReset = () => {
    form.resetFields();
    setPagination({
      keywords: "",
      date: "",
      type: null,
      pageNo: 1,
      pageSize: 10,
    });
  };

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState({
    list: [],
    total: 0,
  });

  const getLogListData = async () => {
    try {
      setLoading(true);
      const params = {
        currPage: pagination.pageNo,
        pageSize: pagination.pageSize,
        keywords: pagination.keywords,
        startDate: pagination.date
          ? moment(pagination.date[0]).format("YYYY-MM-DD")
          : "",
        endDate: pagination.date
          ? moment(pagination.date[1]).format("YYYY-MM-DD")
          : "",
        type: pagination.type,
      };
      const { data } = await logList(params);
      setData({ list: data.records || [], total: data.total * 1 });
      setLoading(false);
    } catch (error) {
      console.log("error", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getLogListData();
  }, [pagination]);

  return (
    <Root>
      <div className="title">
        操作日志
        <span className="tips">仅记录影响用户查看监控范围的操作</span>
      </div>
      <div className="filterBox">
        <Form onFinish={onFinish} form={form} name="filterForm">
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item name="keywords" label="搜索" className="ml-8">
                <Input placeholder="操作人/影响人员" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="date" label="操作日期">
                <DatePicker.RangePicker />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="type" label="操作类型">
                <Select placeholder="请选择">
                  <Select.Option value={1}>添加自由巡课权限</Select.Option>
                  <Select.Option value={2}>删除自由巡课权限</Select.Option>
                  <Select.Option value={3}>编辑任务巡课权限</Select.Option>
                  <Select.Option value={4}>删除任务巡课权限</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item>
                <Button htmlType="submit" type="primary">
                  查询
                </Button>
                <Button onClick={onReset} className="ml-2">
                  重置
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
      <div className="content">
        <div className="content-title flex justify-between items-center">
          <div>日志列表</div>
        </div>
        <Table
          loading={loading}
          className="mt-5"
          rowKey={(columns) => columns.operationId}
          dataSource={data.list}
          columns={columns}
          pagination={false}
        />
        <div className="text-right mt-5">
          <Pagination
            className="inline-block"
            current={pagination.pageNo}
            pageSize={pagination.pageSize}
            onChange={(page, pageSize) =>
              setPagination({ ...pagination, pageNo: page, pageSize })
            }
            total={data.total}
            showSizeChanger
            showQuickJumper
            showTotal={(total) => `总共 ${total} 条`}
          />
        </div>
      </div>
    </Root>
  );
}
