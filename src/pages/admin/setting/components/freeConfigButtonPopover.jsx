import { useState, useRef } from "react";
import { Switch, Select, Tooltip } from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import styled from "styled-components";
import { useClickAway } from "ahooks";

const Root = styled.div`
  position: relative;
  display: inline-block;
  .btn {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    padding: 0 16px;
    border: 1px dashed #0062d9;
    border-radius: 4px;
    height: 36px;
    color: #0062d9;
  }
  .body {
    z-index: 2;
    position: absolute;
    left: 0;
    top: 38px;
    width: 302px;
    display: inline-block;
    padding: 16px;
    border-radius: 4px;
    background: #ffffff;
    box-shadow: 0px 8px 16px 0px rgba(51, 51, 51, 0.2);
  }
  .title {
    color: #000000;
    font-weight: 700;
  }

  .item {
    display: flex;
    align-items: center;
    margin-top: 16px;
  }
`;
const TooltipRoot = styled.div`
  .title {
    font-weight: 700;
  }
  .content {
    margin-top: 8px;
    color: rgba(255, 255, 255, 0.8);
  }
  .note {
    color: #ffaa00;
    margin-top: 20px;
  }
`;

export default function FreeConfigButtonPopover({
  classSwitch,
  roomSwitch,
  monitorSwitch,
  equipmentSwitch,
  livingSwitch,
  freeMode,
  nameIsFull,
  classIsFull,
  gradeIsFull,
  setPatrolFreeSetting,
}) {
  const ref = useRef(null);
  const [showPanel, setShow] = useState(false);
  useClickAway(() => {
    setShow(false);
  }, ref);

  function getTooltipNode() {
    return (
      <TooltipRoot className="p-3">
        <div className="title">普通模式</div>
        <div className="content">
          可以同时查看多个教室的画面，教室存在多画面时，需要切换查看
        </div>
        <div className="title mt-5">教室模式</div>
        <div className="content">
          只看一个教室的画面，教室存在多画面时，可以同时查看，可以查看当时课表信息，查看其他教室需要切换查看
        </div>
        <div className="note">该设置在任务巡课-教室模式下同样生效</div>
      </TooltipRoot>
    );
  }
  return (
    <Root ref={ref}>
      <div onClick={() => setShow(true)} className="btn">
        <i className="iconfont iconshezhi1 mr-2"></i>
        <span>设置</span>
      </div>
      {showPanel && (
        <div className="body">
          <div className="title">巡课菜单</div>
          <div className="item">
            <div className="w-14">班级</div>
            <Switch
              checked={classSwitch === 1}
              onChange={(checked) => {
                setPatrolFreeSetting((pre) => ({
                  ...pre,
                  classSwitch: checked ? 1 : 0,
                }));
              }}
              className="ml-2 mr-6"
            ></Switch>
          </div>
          <div className="item">
            <div className="w-14">教室</div>
            <Switch
              checked={roomSwitch === 1}
              onChange={(checked) => {
                setPatrolFreeSetting((pre) => ({
                  ...pre,
                  roomSwitch: checked ? 1 : 0,
                }));
              }}
              className="ml-2 mr-6"
            ></Switch>
            <Select
              value={freeMode}
              getPopupContainer={() => ref.current}
              onChange={(v) => {
                setPatrolFreeSetting((pre) => ({
                  ...pre,
                  freeMode: v,
                }));
              }}
              style={{ width: 100 }}
              options={[
                {
                  value: 101,
                  label: "普通模式",
                },
                {
                  value: 102,
                  label: "教室模式",
                },
              ]}
            />
            <Tooltip
              overlayInnerStyle={{ width: 300 }}
              placement="bottom"
              title={getTooltipNode}
            >
              <InfoCircleOutlined
                className="ml-3"
                style={{ color: "#8C8C8C", fontSize: 20 }}
              />
            </Tooltip>
          </div>
          <div className="item">
            <div className="w-14">监控</div>
            <Switch
              checked={monitorSwitch === 1}
              onChange={(checked) => {
                setPatrolFreeSetting((pre) => ({
                  ...pre,
                  monitorSwitch: checked ? 1 : 0,
                }));
              }}
              className="ml-2 mr-6"
            ></Switch>
          </div>
          {/* <div className="item">
            <div className="w-14">录播设备</div>
            <Switch
              checked={equipmentSwitch === 1}
              onChange={(checked) => {
                setPatrolFreeSetting((pre) => ({
                  ...pre,
                  equipmentSwitch: checked ? 1 : 0,
                }));
              }}
              className="ml-2 mr-6"
            ></Switch>
          </div>
          <div className="item">
            <div className="w-14">直播</div>
            <Switch
              checked={livingSwitch === 1}
              onChange={(checked) => {
                setPatrolFreeSetting((pre) => ({
                  ...pre,
                  livingSwitch: checked ? 1 : 0,
                }));
              }}
              className="ml-2 mr-6"
            ></Switch>
          </div> */}
          <div className="title mt-6">巡课填写</div>
          <div className="item">
            <div className="w-14">姓名</div>
            <Switch
              checked={nameIsFull === 1}
              onChange={(checked) => {
                setPatrolFreeSetting((pre) => ({
                  ...pre,
                  nameIsFull: checked ? 1 : 0,
                }));
              }}
              className="ml-2 mr-6"
            ></Switch>
          </div>{" "}
          <div className="item">
            <div className="w-14">班级</div>
            <Switch
              checked={classIsFull === 1}
              onChange={(checked) => {
                setPatrolFreeSetting((pre) => ({
                  ...pre,
                  classIsFull: checked ? 1 : 0,
                }));
              }}
              className="ml-2 mr-6"
            ></Switch>
          </div>{" "}
          <div className="item">
            <div className="w-14">年级</div>
            <Switch
              checked={gradeIsFull === 1}
              onChange={(checked) => {
                setPatrolFreeSetting((pre) => ({
                  ...pre,
                  gradeIsFull: checked ? 1 : 0,
                }));
              }}
              className="ml-2 mr-6"
            ></Switch>
          </div>
        </div>
      )}
    </Root>
  );
}
