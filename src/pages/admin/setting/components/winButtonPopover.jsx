import { useState } from "react";
import { Switch, Checkbox } from "antd";
import styled from "styled-components";
import { Clickoutside } from "../../../../components/clickoutside";

const Root = styled.div`
  position: relative;
  display: inline-block;
  .btn {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    padding: 0 16px;
    border: 1px dashed #0062d9;
    border-radius: 4px;
    height: 36px;
    color: #0062d9;
  }
  .body {
    z-index: 2;
    position: absolute;
    left: 0;
    top: 38px;
    min-width: 220px;
    display: inline-block;
    padding: 16px;
    border-radius: 4px;
    background: #ffffff;
    box-shadow: 0px 8px 16px 0px rgba(51, 51, 51, 0.2);
  }
  .body-item + .body-item {
    margin-top: 24px;
  }
  .body-item {
    display: flex;
    align-items: center;
    justify-content: space-around;
    .title {
      min-width: 44px;
      text-align: right;
    }
  }
`;

export default function WinButtonPopover({
  oneFrame,
  oneFrameDefault,
  two<PERSON>rame,
  two<PERSON>rameDef<PERSON>,
  three<PERSON>rame,
  three<PERSON>rameDefault,
  four<PERSON>rame,
  four<PERSON>rameDefault,
  six<PERSON>rame,
  sixFrameDefault,
  nineFrame,
  nineFrameDefault,
  sixteenFrame,
  sixteenFrameDefault,
  setPatrolSystemSetting,
  adaptFrame,
  adaptFrameDefault,
}) {
  const [show, setShow] = useState(false);
  function onSwicth(type, checked) {
    if (checked) {
      setPatrolSystemSetting((pre) => ({
        ...pre,
        [type]: 1,
      }));
    } else {
      setPatrolSystemSetting((pre) => ({
        ...pre,
        [type]: 0,
        [type + "Default"]: 0,
      }));
    }
  }
  function onCheck(type, e) {
    const checked = e.target.checked;

    if (checked) {
      setPatrolSystemSetting((pre) => ({
        ...pre,
        oneFrameDefault: 0,
        twoFrameDefault: 0,
        threeFrameDefault: 0,
        fourFrameDefault: 0,
        sixFrameDefault: 0,
        nineFrameDefault: 0,
        sixteenFrameDefault: 0,
        adaptFrameDefault: 0,
        [type]: 1,
        [type.replace("Default", "")]: 1,
      }));
    } else {
      setPatrolSystemSetting((pre) => ({
        ...pre,
        [type]: 0,
      }));
    }
  }
  return (
    <Root>
      <Clickoutside
        cb={() => {
          setShow(false);
        }}
      >
        <div onClick={() => setShow(true)} className="btn">
          <i className="iconfont iconshezhi1 mr-2"></i>
          <span>设置</span>
        </div>

        {show && (
          <div className="body">
            <div className="body-item">
              <div className="title">1x1</div>
              <Switch
                checked={oneFrame === 1}
                onChange={(value) => onSwicth("oneFrame", value)}
                className="ml-2 mr-6"
              ></Switch>
              <Checkbox
                checked={oneFrameDefault === 1}
                onChange={(value) => onCheck("oneFrameDefault", value)}
              >
                默认
              </Checkbox>
            </div>
            <div className="body-item">
              <div className="title">1x2</div>
              <Switch
                checked={twoFrame === 1}
                onChange={(value) => onSwicth("twoFrame", value)}
                className="ml-2 mr-6"
              ></Switch>
              <Checkbox
                checked={twoFrameDefault === 1}
                onChange={(value) => onCheck("twoFrameDefault", value)}
              >
                默认
              </Checkbox>
            </div>
            <div className="body-item">
              <div className="title">3分</div>
              <Switch
                checked={threeFrame === 1}
                onChange={(value) => onSwicth("threeFrame", value)}
                className="ml-2 mr-6"
              ></Switch>
              <Checkbox
                checked={threeFrameDefault === 1}
                onChange={(value) => onCheck("threeFrameDefault", value)}
              >
                默认
              </Checkbox>
            </div>
            <div className="body-item">
              <div className="title">2x2</div>
              <Switch
                checked={fourFrame === 1}
                onChange={(value) => onSwicth("fourFrame", value)}
                className="ml-2 mr-6"
              ></Switch>
              <Checkbox
                checked={fourFrameDefault === 1}
                onChange={(value) => onCheck("fourFrameDefault", value)}
              >
                默认
              </Checkbox>
            </div>
            <div className="body-item">
              <div className="title">2x3</div>
              <Switch
                checked={sixFrame === 1}
                onChange={(value) => onSwicth("sixFrame", value)}
                className="ml-2 mr-6"
              ></Switch>
              <Checkbox
                checked={sixFrameDefault === 1}
                onChange={(value) => onCheck("sixFrameDefault", value)}
              >
                默认
              </Checkbox>
            </div>
            <div className="body-item">
              <div className="title">3x3</div>
              <Switch
                checked={nineFrame === 1}
                onChange={(value) => onSwicth("nineFrame", value)}
                className="ml-2 mr-6"
              ></Switch>
              <Checkbox
                checked={nineFrameDefault === 1}
                onChange={(value) => onCheck("nineFrameDefault", value)}
              >
                默认
              </Checkbox>
            </div>
            <div className="body-item">
              <div className="title">4x4</div>
              <Switch
                checked={sixteenFrame === 1}
                onChange={(value) => onSwicth("sixteenFrame", value)}
                className="ml-2 mr-6"
              ></Switch>
              <Checkbox
                checked={sixteenFrameDefault === 1}
                onChange={(value) => onCheck("sixteenFrameDefault", value)}
              >
                默认
              </Checkbox>
            </div>
            <div className="body-item">
              <div className="title">自适应</div>
              <Switch
                checked={adaptFrame === 1}
                onChange={(value) => onSwicth("adaptFrame", value)}
                className="ml-2 mr-6"
              ></Switch>
              <Checkbox
                checked={adaptFrameDefault === 1}
                onChange={(value) => onCheck("adaptFrameDefault", value)}
              >
                默认
              </Checkbox>
            </div>
          </div>
        )}
      </Clickoutside>
    </Root>
  );
}
