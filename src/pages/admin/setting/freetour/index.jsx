import React,{useEffect, useState} from "react";
import {searchByUserName,
  add<PERSON>ree<PERSON>ser,editFreeUser,delFree<PERSON>ser,freeUserList,
  getFreeUser
} from "@/api"
import styled from "styled-components";
import {Space, Button,Table,Tooltip,Divider,
  Form,AutoComplete,
  Modal,Input, message,Pagination} from "antd";
import { PlusOutlined,QuestionCircleFilled,InfoCircleFilled } from "@ant-design/icons"
const { confirm } = Modal
const { Option } = AutoComplete

const Root = styled.div`
  padding: 16px 24px 24px;
  .title {
    display: flex;
    align-items: center;
    color: #262626;
    .label {
      color: #8C8C8C;
    }
  }
  .content {
    padding: 24px;
    margin-top: 16px;
    min-height: calc(100vh - 138px);
    background: #fff;
    border-radius: 4px;
  }
`

export default function FreeTour() {
  const [data, setData] = useState({
    list: [],
    total: 0,
  })
  
  const [freeUserModalInfo, setFreeUserModalInfo] = useState({
    show: false,
    id: '',
  })
  const [pagination, setPagination] = useState({
    pageNo: 1,
    pageSize: 10,
  })

  const columns = [
    {
      title: "序号",
      width: 60,
      // align: "center",
      key: "index",
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: "姓名",
      // align: "center",
      dataIndex: "userName",
      key: "userName",
    },
    {
      title: "机构",
      dataIndex: "orgName",
      key: "orgName",
      ellipsis: {
        showTitle: false,
      },
    },
    {
      title: "手机号",
      // align: "center",
      dataIndex: "phone",
      key: "phone",
    },
    {
      title: "备注",
      // align: "center",
      dataIndex: "remarks",
      key: "remarks",
      ellipsis: {
        showTitle: false,
      },
      render: (remarks) => (
        <Tooltip placement="topLeft" title={remarks}>
          {remarks}
        </Tooltip>
      ),
    },
    {
      title: "操作",
      // align: "center",
      width: 160,
      key: "control",
      render: (_, record) => (
        <div>
          <span
            onClick={() => {
              setFreeUserModalInfo({
                show: true,
                id: record.id
              })
              // setPagination(pre => ({...pre}))
            }}
            className="cursor-pointer text-blue-500"
          >
            编辑
          </span>
          <Divider type="vertical" />
          <span
            onClick={() => {
              confirm({
                title: "确定删除吗？",
                icon: <QuestionCircleFilled />,
                content: "",
                centered: true,
                onOk() {
                  delFreeUser({ id: record.id }).then(({ code }) => {
                    if (code === 0) {
                      message.success("删除成功");
                      setPagination(pre => ({...pre, pageNo:1}))
                    }
                  })
                },
              })
            }}
            className="cursor-pointer text-blue-500"
          >
            删除
          </span>
        </div>
      ),
    },
  ]
 
  useEffect(() => {
    const getList = async () => {
      const result = await freeUserList(pagination);
      if(result && result.code === 0) {
        setData({
          list: result.data,
          total: Number(result.totalDatas),
        })
      }
    }
    getList();
  }, [pagination])

  return <Root>
    <div className="title">
      <span className="mr-3">自由巡课</span>
      <div>
        <InfoCircleFilled
          className="cursor-default"
          style={{ color: "#BFBFBF" }}
        />
        <span className="label ml-1">下列名单中的用户可以随时进行巡课，不需要设置巡课任务</span>
      </div>
    </div>
    <div className="content">
    <div className="content-title flex justify-between items-center">
      <div>自由巡课列表</div>
        <Space>
          <Button
            onClick={() => {
              setFreeUserModalInfo({
                show: true,
                id: '',
              })
            }}
            icon={<PlusOutlined />}
            type="primary"
          >
            添加
          </Button>
        </Space>
    </div>
    <Table
      className="mt-5"
      rowKey={(columns) => columns.id}
      dataSource={data.list}
      columns={columns}
      pagination={false}
        />
        <div className="text-right mt-5">
          <Pagination
            className="inline-block"
            current={pagination.pageNo}
            pageSize={pagination.pageSize}
            onChange={(page, pageSize) => {
              setPagination(pre => ({...pre, pageNo: page, pageSize}))
            }}
            total={data.total}
            showSizeChanger
            showQuickJumper
            showTotal={(total) => `总共 ${total} 条`}
          />
        </div>
    </div>
    <FreeUserModal 
      {...freeUserModalInfo}
      onOk={() => {
        setFreeUserModalInfo(pre => ({...pre, show:false}))
        setPagination(pre => ({...pre}))
      }}
      onCancel={() => {
        setFreeUserModalInfo(pre => ({...pre, show:false}))
      }}
      />
  </Root>
}

function FreeUserModal({
  show,
  id,
  onOk,
  onCancel
}) {
  const [options, setOptions] = useState([])
  const [userInfo, setUserInfo] = useState(null)
  const [form] = Form.useForm()
  let title = id ? "编辑" : "添加"
  const handleOk = async () => {
    form.validateFields().then(values => {
      const {userName, remark} = values;
      if(!userInfo || userInfo.label !== userName) {
        return message.error('请选择正确的人员');
      }
      const data = {
        id,
        phone: userInfo.phone,
        remark,
        userId: userInfo.value,
        userName: userInfo.label
      }
      if(!id) {
        addFreeUser(data).then(result => {
          if(result && result.code === 0) {
            message.success('保存成功')
            onOk();
          }
        })
      } else {
        editFreeUser(data).then(result => {
          if(result && result.code === 0) {
            message.success('保存成功')
            onOk();
          }
        })
      }
    })
  

  }
  const onSearch = async (userName) => {
    if (!userName) {
      setOptions([])
    } else {
      const result = await searchByUserName({ userName })
      if (result.code === 0) {
        const data = result.data.map((item) => ({
          value: item.coreUserInfoId,
          label: item.userName,
          phone: item.phone,
          orgName: item.orgName,
        }))
        setOptions(data)
      }
    }
  }
  const onSelect = async (value) => {
    const target = options.find((item) => item.value === value)
    setUserInfo(target)
    form.setFieldsValue({ userName: target.label })
  }
  useEffect(() => {
    if(!show) {
      form.resetFields();
      setOptions([]);
      setUserInfo(null);
    }
  }, [show, form])
  useEffect(() => {
    if(!id || !show) return;
    getFreeUser({id}).then(result => {
      setUserInfo({
        phone: result.data.phone,
        value: result.data.recordUserId,
        label: result.data.userName
      })
      form.setFields([
        {
          name: 'userName',
          value: result.data.userName
        },
        {
          name: 'remark',
          value: result.data.remarks
        },
      ])
    })
  }, [id, form, show])

  return <Modal
    width={460}
    centered
    title={title}
    visible={show}
    onOk={handleOk}
    destroyOnClose
    onCancel={onCancel}>
    <Form
        form={form}
        labelCol={{ span: 3 }}
        wrapperCol={{ span: 21 }}
        autoComplete="off"
      >
        <Form.Item
          label="人员"
          name="userName"
          rules={[{ required: true, message: "请输入姓名" }]}
        >
          <AutoComplete
            className="w-full"
            onSelect={onSelect}
            onSearch={onSearch}
            placeholder="请输入姓名搜索"
          >
            {options.map((option) => {
              return (
                <Option key={option.value}>
                  <div className="flex justify-between">
                    <span className="flex-1 truncate">{option.label}</span>
                    <span className="flex-1 truncate mx-4">{option.orgName}</span>
                    <span className="flex-1 truncate">{option.phone}</span>
                  </div>
                </Option>
              )
            })}
          </AutoComplete>
        </Form.Item>


        <Form.Item
          label="备注"
          name="remark"
        >
          <Input.TextArea rows={4} maxLength={200} placeholder="请输入备注" />
        </Form.Item>

        
      </Form>
  </Modal>
}