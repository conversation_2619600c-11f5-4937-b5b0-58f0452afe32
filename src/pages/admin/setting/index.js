import React, { useEffect, useState, useRef } from "react";
import WinButtonPopover from "./components/winButtonPopover";
import FreeConfigButtonPopover from "./components/freeConfigButtonPopover";
import styled from "styled-components";
import { $upload, $isCdn, $message } from "@/tools";
import { getSetting, updateSetting } from "@/api";
import {
  Form,
  Input,
  Button,
  message,
  Checkbox,
  Tooltip,
  Radio,
  Select,
  Switch
} from "antd";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { useVendorList } from "./useVendorList";
const Root = styled.div`
  padding: 16px 24px 24px;
  .title {
    color: #262626;
  }
  .content {
    padding: 24px;
    margin-top: 16px;
    min-height: calc(100vh - 138px);
    background: #fff;
    border-radius: 4px;
  }
  .imgContainer {
    width: 70px;
    height: 70px;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    padding: 10px;
    text-align: center;
    line-height: 50px;
    img {
      width: 100%;
      height: 100%;
    }
  }
`;
function AdminSetting() {
  const { vendorList } = useVendorList();
  const inputRef = useRef();

  const [patrolSystemSetting, setPatrolSystemSetting] = useState({
    systemName: "在线巡课",
    systemLogo: null,
    systemSettingId: "0",
    /* 监控预览方式 */
    prevType: 1,
    vendor: "",
    vendorId: "",
    // 巡课画面数量
    oneFrame: 1,
    oneFrameDefault: 1,
    twoFrame: 1,
    twoFrameDefault: 0,
    threeFrame: 1,
    threeFrameDefault: 0,
    fourFrame: 1,
    fourFrameDefault: 0,
    sixFrame: 1,
    sixFrameDefault: 0,
    nineFrame: 1,
    nineFrameDefault: 0,
    sixteenFrame: 1,
    sixteenFrameDefault: 0,
    adaptFrame: 1,
    adaptFrameDefault: 0,
    openWaterMark: 1,
    // 允许查看与自己相关的巡课记录
    allowViewOwnRecords: 1,
    // 移动端巡课模式
    videoTour: 1,
    offlineTour: 1,
    freeAutoJump: 1
  });
  const [patrolFreeSetting, setPatrolFreeSetting] = useState({
    classIsFull: 1,
    equipmentSwitch: 1,
    freeMode: 101,
    freeSettingId: "",
    gradeIsFull: 1,
    livingSwitch: 1,
    monitorSwitch: 1,
    nameIsFull: 1,
    classSwitch: 1,
    roomSwitch: 1,
    systemSettingId: "",
  });

  useEffect(() => {
    getSetting().then((res) => {
      if (res.code === 0) {
        setPatrolSystemSetting(res.data.patrolSystemSetting);
        setPatrolFreeSetting(res.data.patrolFreeSetting);
      }
    });
  }, []);
  const fileChange = async (e) => {
    const data = await $upload(e);
    if (data.smallPath) {
      message.success("上传成功");
      setPatrolSystemSetting((pre) => ({
        ...pre,
        systemLogo: data.smallPath,
      }));
    }
  };
  const submit = () => {
    if (!patrolSystemSetting.systemName.trim()) {
      message.error("请输入系统名称");
      return;
    }
    /* 监控预览方式 */
    if (patrolSystemSetting.prevType === 2 && !patrolSystemSetting.vendorId) {
      message.error("插件模式请选择厂商");
      return;
    }
    /* 巡课模式 */
    if (
      patrolSystemSetting.videoTour === 0 &&
      patrolSystemSetting.offlineTour === 0
    ) {
      message.error("巡课模式至少选择一个");
      return;
    }

    /* 巡课画面数量 */
    if (
      patrolSystemSetting.oneFrame === 0 &&
      patrolSystemSetting.twoFrame === 0 &&
      patrolSystemSetting.threeFrame === 0 &&
      patrolSystemSetting.fourFrame === 0 &&
      patrolSystemSetting.sixFrame === 0 &&
      patrolSystemSetting.nineFrame === 0 &&
      patrolSystemSetting.sixteenFrame === 0
    ) {
      message.error("巡课画面数量至少选择一个");
      return;
    }
    /* 巡课画面默认数量 */
    if (
      patrolSystemSetting.oneFrameDefault === 0 &&
      patrolSystemSetting.twoFrameDefault === 0 &&
      patrolSystemSetting.threeFrameDefault === 0 &&
      patrolSystemSetting.fourFrameDefault === 0 &&
      patrolSystemSetting.sixFrameDefault === 0 &&
      patrolSystemSetting.nineFrameDefault === 0 &&
      patrolSystemSetting.sixteenFrameDefault === 0 &&
      patrolSystemSetting.adaptFrameDefault == 0
    ) {
      message.error("巡课画面数量默认至少选择一个");
      return;
    }

    /* 自由巡课配菜单 */
    if (
      patrolFreeSetting.equipmentSwitch === 0 &&
      patrolFreeSetting.roomSwitch === 0 &&
      patrolFreeSetting.livingSwitch === 0 &&
      patrolFreeSetting.monitorSwitch === 0 &&
      patrolFreeSetting.classSwitch === 0
    ) {
      message.error("自由巡课配菜单至少选择一个");
      return;
    }
    /* 自由巡课填写项 */
    if (
      patrolFreeSetting.nameIsFull === 0 &&
      patrolFreeSetting.classIsFull === 0 &&
      patrolFreeSetting.gradeIsFull === 0
    ) {
      message.error("自由巡课填写项至少选择一个");
      return;
    }

    const data = {
      ...patrolSystemSetting,
      ...patrolFreeSetting,
    };
    delete data.isdeleted;
    delete data.tenantid;
    delete data.versionnum;
    delete data.status;
    delete data.orgid;
    delete data.publishOrgid;
    delete data.freeSettingId;
    updateSetting(data).then((res) => {
      if (res.code === 0) {
        message.success("保存成功");
        $message.emit("sysChange", {
          systemName: patrolSystemSetting.systemName,
          systemLogo: patrolSystemSetting.systemLogo,
          openWaterMark: patrolSystemSetting.openWaterMark,
        });
      }
    });
  };
  return (
    <Root>
      <div className="title">系统设置</div>
      <div className="content">
        <Form
          style={{ width: 980 }}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 18 }}
        >
          <Form.Item label="系统名称" required>
            <Input
              value={patrolSystemSetting.systemName}
              maxLength="50"
              placeholder="请输入"
              onChange={(e) => {
                setPatrolSystemSetting((pre) => ({
                  ...pre,
                  systemName: e.target.value,
                }));
              }}
              style={{width:' 376px'}}
            />
          </Form.Item>

          <Form.Item label="Logo">
            <div
              onClick={() => inputRef.current.click()}
              className="imgContainer inline-block align-bottom cursor-pointer"
            >
              <input
                ref={(c) => (inputRef.current = c)}
                type="file"
                onChange={fileChange}
                style={{ display: "none" }}
              />
              {patrolSystemSetting.systemLogo ? (
                <img src={$isCdn(patrolSystemSetting.systemLogo)} alt="" />
              ) : (
                <img src="/yskt/ys/ysConfig/images/logo.png" alt="" />
              )}
            </div>

            <div className="mt-2 text-gray-400">建议尺寸：32*32 px</div>
          </Form.Item>

          <Form.Item label="监控预览方式">
            <Radio.Group
              value={patrolSystemSetting.prevType}
              onChange={(e) => {
                setPatrolSystemSetting((pre) => ({
                  ...pre,
                  prevType: e.target.value,
                }));
              }}
            >
              <div className="mt-2">
                <Radio value={1}>直播流模式</Radio>
              </div>
              <div className="mt-4">
                <Radio value={2}>插件模式（RTSP）</Radio>
                <Select
                  value={patrolSystemSetting.vendorId}
                  placeholder="请选择"
                  style={{ width: 210 }}
                  onChange={(vendorId) => {
                    const vendor = vendorList.find(
                      (item) => item.value === vendorId
                    ).vendor;
                    setPatrolSystemSetting((pre) => ({
                      ...pre,
                      vendor,
                      vendorId,
                    }));
                  }}
                >
                  {vendorList.map((item) => (
                    <Select.Option key={item.value} value={item.value}>
                      {item.label}
                    </Select.Option>
                  ))}
                </Select>
              </div>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            label={
              <div>
                <span className="mr-1">巡课画面数量</span>
                <Tooltip placement="bottom" title="仅影响PC端巡课">
                  <QuestionCircleOutlined />
                </Tooltip>
              </div>
            }
          >
            <WinButtonPopover
              {...patrolSystemSetting}
              setPatrolSystemSetting={setPatrolSystemSetting}
            />
          </Form.Item>
          <Form.Item
            label="画面水印"
          >
            <Switch
              checked={patrolSystemSetting.openWaterMark === 1}
              onChange={(checked) => {
                setPatrolSystemSetting((pre) => ({
                  ...pre,
                  openWaterMark: checked ? 1 : 2
                }));
              }}
            />
            <span className="ml-3 text-gray-400">
              开启后，PC端和移动端巡课时画面上方将增加内容为：“观看者姓名+手机号后四位”的水印，且不支持单个画面全屏
            </span>
          </Form.Item>

          <Form.Item label="巡课记录">
            <Checkbox
              onChange={(e) => {
                setPatrolSystemSetting((pre) => ({
                  ...pre,
                  allowViewOwnRecords: e.target.checked ? 1 : 0,
                }));
              }}
              checked={patrolSystemSetting.allowViewOwnRecords === 1}
            >
              允许查看与自己相关的巡课记录
            </Checkbox>
          </Form.Item>

          <Form.Item
            label={
              <div>
                <span className="mr-1">移动端巡课模式</span>
                <Tooltip placement="bottom" title="控制移动端巡课的方式">
                  <QuestionCircleOutlined />
                </Tooltip>
              </div>
            }
          >
            <Checkbox
              onChange={(e) => {
                setPatrolSystemSetting((pre) => ({
                  ...pre,
                  videoTour: e.target.checked ? 1 : 0,
                }));
              }}
              checked={patrolSystemSetting.videoTour === 1}
            >
              视频巡课
            </Checkbox>

            <Checkbox
              onChange={(e) => {
                setPatrolSystemSetting((pre) => ({
                  ...pre,
                  offlineTour: e.target.checked ? 1 : 0,
                }));
              }}
              checked={patrolSystemSetting.offlineTour === 1}
            >
              线下巡课
            </Checkbox>
          </Form.Item>

          <Form.Item label="自由巡课配置">
            <FreeConfigButtonPopover
              {...patrolFreeSetting}
              setPatrolFreeSetting={setPatrolFreeSetting}
            />
          </Form.Item>

          <Form.Item
            label={
              <div>
                <span className="mr-1">自由巡课自动跳转</span>
                <Tooltip placement="bottom" title="开启后，拥有自由巡课权限的教师进入PC端在线巡课后自动跳转至自由巡课页面">
                  <QuestionCircleOutlined />
                </Tooltip>
              </div>
            }
          >
            <Switch
              checked={patrolSystemSetting.freeAutoJump === 1}
              onChange={(checked) => {
                setPatrolSystemSetting((pre) => ({
                  ...pre,
                  freeAutoJump: checked ? 1 : 2
                }));
              }}
            />
          </Form.Item>

          <Form.Item wrapperCol={{ offset: 4, span: 16 }}>
            <Button type="primary" onClick={submit}>
              保存
            </Button>
          </Form.Item>
        </Form>
      </div>
    </Root>
  );
}

export default AdminSetting;
