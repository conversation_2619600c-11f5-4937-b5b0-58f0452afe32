import React,{useEffect, useState, useMemo} from "react";
import {addQuickReply,editQuickReply,
  getQuickReply<PERSON>ist,delQuickReply,
  updateNumTypeTwo, updateNumTypeOne} from "@/api"
import styled from "styled-components";
import {Space, Button,Table,Tooltip,Divider,Dropdown,Menu,Modal,Input, message,Pagination} from "antd";
import { PlusOutlined,QuestionCircleFilled,MenuOutlined } from "@ant-design/icons"
import { SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc';
import { arrayMoveImmutable } from 'array-move';
import "./drag.css"
const { confirm } = Modal
const Root = styled.div`
  padding: 16px 24px 24px;
  .title {
    color: #262626;
  }
  .content {
    padding: 24px;
    margin-top: 16px;
    min-height: calc(100vh - 138px);
    background: #fff;
    border-radius: 4px;
  }
`
const DragHandle = SortableHandle(() => (
  <MenuOutlined
    style={{
      cursor: 'grab',
      color: '#999',
    }}
  />
));
const SortableItem = SortableElement((props) => <tr {...props} />);
const SortableBody = SortableContainer((props) => <tbody {...props} />);

export default function QuickReply() {
  const [data, setData] = useState({
    list: [],
    total: 0,
  })
  const sortArr = useMemo(() => {
    return data.list.map(item => item.orderNum)
  }, [data.list])
  
  const [replyModalInfo, setReplyModalInfo] = useState({
    show: false,
    id: '',
    defaultValue: '',
  })
  const [pagination, setPagination] = useState({
    pageNo: 1,
    pageSize: 10,
  })

  const columns = [
    {
      title: '排序',
      dataIndex: 'sort',
      width: 60,
      className: 'drag-visible',
      render: () => <DragHandle />,
    },
    {
      title: "序号",
      width: 60,
      align: "center",
      key: "index",
      className: 'drag-visible',
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: "评语",
      align: "center",
      dataIndex: "content",
      key: "content",
      ellipsis: {
        showTitle: false,
      },
      className: 'drag-visible',
      render: (content) => (
        <Tooltip placement="topLeft" title={content}>
          {content}
        </Tooltip>
      ),
    },
    {
      title: "操作",
      align: "center",
      width: 160,
      key: "control",
      render: (_, record) => (
        <div>
          <span
            onClick={() => {
              setReplyModalInfo({
                show: true,
                defaultValue: record.content,
                id: record.id
              })
              // setPagination(pre => ({...pre}))
            }}
            className="cursor-pointer text-blue-500"
          >
            编辑
          </span>
          <Divider type="vertical" />
          <span
            onClick={() => {
              confirm({
                title: "确定删除这条快捷回复吗？",
                icon: <QuestionCircleFilled />,
                content: "",
                centered: true,
                onOk() {
                  delQuickReply({ id: record.id }).then(({ code }) => {
                    if (code === 0) {
                      message.success("删除成功");
                      setPagination(pre => ({...pre, pageNo:1}))
                    }
                  })
                },
              })
            }}
            className="cursor-pointer text-blue-500"
          >
            删除
          </span>
          <Divider type="vertical" />
          <Dropdown
            overlay={
              <Menu onClick={(_) => handleControlBtns(_, record)}>
                <Menu.Item key="up">
                  <span>上移</span>
                </Menu.Item>
                <Menu.Item key="down">
                  <span>下移</span>
                </Menu.Item>
              </Menu>
            }
            placement="bottomRight"
          >
            <span className="cursor-pointer text-blue-500 font-bold">···</span>
          </Dropdown>
        </div>
      ),
    },
  ]
  const handleControlBtns = async ({ key }, data) => {
    let type = key === "up" ? 1 : 2;
    const result = await updateNumTypeTwo({type, id: data.id })
    if(result&&result.code === 0) {
      message.success('排序成功')
      setPagination({...pagination})
    }
  }
 
  useEffect(() => {
    const getList = async () => {
      const result = await getQuickReplyList(pagination);
      if(result && result.code === 0) {
        const list = result.data.map((item,index) => ({
          ...item,
          index
        }))
        setData({
          list: list,
          total: Number(result.totalDatas),
        })
      }
    }
    getList();
  }, [pagination])

  const onSortEnd = ({ oldIndex, newIndex }) => {
    if (oldIndex !== newIndex) {
      const newData = arrayMoveImmutable(data.list.slice(), oldIndex, newIndex).filter(
        (el) => !!el,
      );
      const newList = newData.map((item,index) => ({
        id: item.id,
        orderNum: sortArr[index]
      }))
      updateNumTypeOne(newList).then(res => {
        if(res.code === 0) {
          message.success('排序成功')
          setData({
            list: newData,
            total: data.total
          });
        }
      })

    }
  };

  const DraggableContainer = (props) => (
    <SortableBody
      useDragHandle
      disableAutoscroll
      helperClass="row-dragging"
      onSortEnd={onSortEnd}
      {...props}
    />
  );
  const DraggableBodyRow = ({ className, style, ...restProps }) => {
    // function findIndex base on Table rowKey props and should always be a right array index
    const index = data.list.findIndex((x) => x.index === restProps['data-row-key']);
    return <SortableItem index={index} {...restProps} />;
  };

  return <Root>
    <div className="title">快捷回复</div>
    <div className="content">
    <div className="content-title flex justify-between items-center">
      <div>快捷回复列表</div>
        <Space>
          <Button
            onClick={() => {
              setReplyModalInfo({
                show: true,
                id: '',
                defaultValue: '',
              })
            }}
            icon={<PlusOutlined />}
            type="primary"
          >
            添加
          </Button>
        </Space>
    </div>
    <Table
      className="mt-5"
      rowKey="index"
      dataSource={data.list}
      columns={columns}
      pagination={false}
      components={{
        body: {
          wrapper: DraggableContainer,
          row: DraggableBodyRow,
        },
      }}
        />
      <div className="text-right mt-5">
      <Pagination
        className="inline-block"
        current={pagination.pageNo}
        pageSize={pagination.pageSize}
        onChange={(page, pageSize) => {
          setPagination(pre => ({...pre, pageNo: page, pageSize}))
        }}
        total={data.total}
        showSizeChanger
        showQuickJumper
        showTotal={(total) => `总共 ${total} 条`}
      />
    </div>
    </div>
    <ReplyModal 
      {...replyModalInfo}
      onOk={() => {
        setReplyModalInfo(pre => ({...pre, show:false}))
        setPagination(pre => ({...pre}))
      }}
      onCancel={() => {
        setReplyModalInfo(pre => ({...pre, show:false}))
      }}
      />
  </Root>
}

function ReplyModal({
  show,
  defaultValue,
  id,
  onOk,
  onCancel
}) {
  let title = id ? "编辑评语" : "添加评语"
  const [value, setValue] = useState();

  useEffect(() => {
    setValue(defaultValue)
  }, [defaultValue, show])

  const onChange = (e) => {
    setValue(e.target.value)
  };

  const handleOk = async () => {
    if(!value.trim()) {
      message.error('请输入评语')
      return 
    }
    let result = null;
    if(id) {
      result = await editQuickReply({content:value, id});
    } else {
      result = await addQuickReply({content:value, id});
    }
    if(result && result.code === 0) {
      message.success('保存成功')
      onOk();
    }

  }
  return <Modal
    width={440}
    centered
    title={title}
    visible={show}
    onOk={handleOk}
    destroyOnClose
    onCancel={onCancel}>
    <Input 
      maxLength={50}
      placeholder="请输入" 
      value={value} 
      onChange={onChange}>
    </Input>
  </Modal>
}