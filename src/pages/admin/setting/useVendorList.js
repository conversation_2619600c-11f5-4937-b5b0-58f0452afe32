import { useEffect, useState } from "react";
import { getVendorrList } from "@/api";

export function useVendorList() {
  const [vendorList, setVendorList] = useState([]);

  useEffect(() => {
    getVendorrList({
      currPage: 1,
      pageSize: 999,
    }).then((res) => {
      if (res.code === 0) {
        const list = res.data.records
          .map((item) => ({
            ...item,
            value: item.monitoringCenterId,
            label: item.name,
          }))
          .filter((item) => item.vendor === "1000" || item.vendor === "1002");
        setVendorList(list);
      }
    });
  }, []);

  return {
    vendorList,
  };
}
