import React, { useState, forwardRef, useImperative<PERSON>and<PERSON> } from "react"
import { Modal, Form, Input, DatePicker, message } from "antd"
import { copyAdminTasks } from "@/api"
function CreateModal({ onCopySuccess }, ref) {
  const [copyInfo, setCopyInfo] = useState({
    show: false,
    taskId: 0,
    taskName: "",
  })
  const [form] = Form.useForm()
  useImperativeHandle(ref, () => ({
    showModal(info) {
      setCopyInfo({ ...info, show: true })
    },
  }))
  const handleOk = async () => {
    const values = await form.validateFields()
    if (values) {
      const startTime = values.time[0].format("YYYY-MM-DD")
      const endTime = values.time[1].format("YYYY-MM-DD")
      const data = {
        startTime,
        endTime,
        taskId: copyInfo.taskId,
        taskName: values.taskName,
        remarks: values.remarks,
      }
      const { code } = await copyAdminTasks(data)
      if (code === 0) {
        message.success("复制成功")
        handleCancel()
        onCopySuccess()
      }
    }
  }
  const handleCancel = () => {
    form.resetFields()
    setCopyInfo({
      show: false,
      taskId: 0,
      taskName: "",
    })
  }
  return (
    <Modal
      width="480px"
      title="复制任务"
      visible={copyInfo.show}
      onOk={handleOk}
      onCancel={handleCancel}
    >
      <Form form={form} labelCol={{ span: 5 }}>
        <Form.Item label="复制源任务">
          <div>{copyInfo.taskName}</div>
        </Form.Item>
        <Form.Item
          label="新任务名称"
          name="taskName"
          rules={[{ required: true, message: "请输入" }]}
        >
          <Input maxLength="50" placeholder="请输入" />
        </Form.Item>
        <Form.Item
          label="新任务时间"
          name="time"
          rules={[{ required: true, message: "请选择时间" }]}
        >
          <DatePicker.RangePicker className="w-full" />
        </Form.Item>
        <Form.Item label="备注" name="remarks">
          <Input.TextArea maxLength={200} rows={4} placeholder="请输入" />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default forwardRef(CreateModal)
