import React, { useState, useImperativeHandle, forwardRef } from "react"
import { Modal, Form, Input, DatePicker, message, Checkbox } from "antd"
import { addAdminTasks } from "@/api"
import { useTourInfo, TourObjRoot, defaultTourInfo } from "@/hooks/useTourInfo"

const CreateModal = ({ onCreateSuccess }, ref) => {
  const [form] = Form.useForm()
  const [show, setShow] = useState(false)

  const { tourInfo, setTourInfo, onChangeTourInfo, validateCheckTourInfo } =
    useTourInfo()

  useImperativeHandle(ref, () => ({
    showModal() {
      setShow(true)
    },
  }))

  const handleOk = async () => {
    const tourResult = await validateCheckTourInfo()
    const values = await form.validateFields()
    if (tourResult === "error") return
    if (!values) return

    const startTime = values.time[0].format("YYYY-MM-DD")
    const endTime = values.time[1].format("YYYY-MM-DD")
    const data = {
      startTime,
      endTime,
      taskId: 0,
      taskName: values.taskName,
      remarks: values.remarks,
      personalIsFill: tourInfo.personalIsFill,
      personalIsShow: tourInfo.personalIsShow,
      gradeIsFill: tourInfo.gradeIsFill,
      gradeIsShow: tourInfo.gradeIsShow,
      classIsFill: tourInfo.classIsFill,
      classIsShow: tourInfo.classIsShow,
    }
    const { code } = await addAdminTasks(data)
    if (code === 0) {
      message.success("创建成功")
      handleCancel()
      onCreateSuccess()
    }
  }

  const handleCancel = () => {
    form.resetFields()
    setTourInfo(defaultTourInfo)
    setShow(false)
  }

  return (
    <Modal
      width="480px"
      title="添加任务"
      visible={show}
      onOk={handleOk}
      onCancel={handleCancel}
    >
      <Form
        form={form}
        labelCol={{ span: 5 }}
      >
        <Form.Item
          label="任务名称"
          name="taskName"
          rules={[{ required: true, message: "请输入" }]}
        >
          <Input
            maxLength="50"
            placeholder="请输入"
          />
        </Form.Item>
        <Form.Item
          label="时间"
          name="time"
          rules={[{ required: true, message: "请选择时间" }]}
        >
          <DatePicker.RangePicker className="w-full" />
        </Form.Item>
        <Form.Item
          label="巡课对象"
          validateStatus={tourInfo.validateStatus}
          help={tourInfo.errorMsg}
          required
        >
          <TourObjRoot>
            <div
              className={`tourItem flex items-center px-3 ${tourInfo.validateStatus}`}
            >
              <span className="flex-1">个人</span>
              <Checkbox
                onChange={() => onChangeTourInfo("personalIsShow")}
                checked={tourInfo.personalIsShow === 1}
              >
                显示
              </Checkbox>
              <Checkbox
                onChange={() => onChangeTourInfo("personalIsFill")}
                checked={tourInfo.personalIsFill === 1}
              >
                必填
              </Checkbox>
            </div>
            <div
              className={`tourItem flex items-center px-3 ${tourInfo.validateStatus}`}
            >
              <span className="flex-1">年级</span>
              <Checkbox
                onChange={() => onChangeTourInfo("gradeIsShow")}
                checked={tourInfo.gradeIsShow === 1}
              >
                显示
              </Checkbox>
              <Checkbox
                onChange={() => onChangeTourInfo("gradeIsFill")}
                checked={tourInfo.gradeIsFill === 1}
              >
                必填
              </Checkbox>
            </div>
            <div
              className={`tourItem flex items-center px-3 ${tourInfo.validateStatus}`}
            >
              <span className="flex-1">班级</span>
              <Checkbox
                onChange={() => onChangeTourInfo("classIsShow")}
                checked={tourInfo.classIsShow === 1}
              >
                显示
              </Checkbox>
              <Checkbox
                onChange={() => onChangeTourInfo("classIsFill")}
                checked={tourInfo.classIsFill === 1}
              >
                必填
              </Checkbox>
            </div>
          </TourObjRoot>
        </Form.Item>
        <Form.Item
          label="备注"
          name="remarks"
        >
          <Input.TextArea
            maxLength={200}
            rows={4}
            placeholder="请输入"
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default forwardRef(CreateModal)
