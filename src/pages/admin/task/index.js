import React, { useState, useRef, useEffect } from "react"
import {
  <PERSON><PERSON>,
  Space,
  Table,
  Pagination,
  Tooltip,
  Divider,
  Dropdown,
  Menu,
  message,
  Modal,
  Form,
  Input,
  DatePicker,
} from "antd"
import moment from "moment"
import { v4 as uuid } from "uuid"
import { useRouterGo } from "@/hooks/useRouterGo"
import { PlusOutlined, QuestionCircleFilled } from "@ant-design/icons"
import { getAdminTasks, updateTaskStatus, deleteTask } from "@/api"
import styled from "styled-components"
import CreateModal from "./createModal"
import CopyModal from "./copyModal"
import TaskSetting from "./setting"

const { confirm } = Modal
const Root = styled.div`
  padding: 16px 24px 24px;
  .title {
    color: #262626;
  }
  .filterBox {
    padding: 24px;
    margin-top: 16px;
    background: #fff;
    border-radius: 4px;
  }
  .content {
    padding: 24px;
    margin-top: 16px;
    /* min-height: calc(100vh - 138px); */
    background: #fff;
    border-radius: 4px;
  }
`
const StatusLabel = styled.span`
  position: relative;
  &.open::before {
    background: #17be6b;
  }
  &.close::before {
    background: #f53f3f;
  }
  &::before {
    content: "";
    position: absolute;
    top: 50%;
    left: -10px;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
`

function formatPatrolObjectLabel(patrolObject) {
  if (!patrolObject) return "-"
  const list = []
  if (patrolObject.personalIsShow === 1) {
    list.push("个人")
  }
  if (patrolObject.gradeIsShow === 1) {
    list.push("年级")
  }
  if (patrolObject.classIsShow === 1) {
    list.push("班级")
  }
  return list.join("、")
}

function AdminTask() {
  const navigate = useRouterGo()
  const [loading, setLoading] = useState(false)
  const columns = [
    {
      title: "序号",
      width: 60,
      align: "center",
      key: "index",
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: "任务名称",
      align: "center",
      dataIndex: "taskName",
      key: "taskName",
    },
    {
      title: "巡课对象",
      align: "center",
      dataIndex: "patrolObject",
      key: "patrolObject",
      render: (_, record) => (
        <div>
          <span>{formatPatrolObjectLabel(_)}</span>
        </div>
      ),
    },
    {
      title: "时间",
      align: "center",
      key: "time",
      render: (_, record) => (
        <div>
          <span>{moment(record.startTime).format("YYYY-MM-DD")}</span>
          <span> 至 </span>
          <span>{moment(record.endTime).format("YYYY-MM-DD")}</span>
        </div>
      ),
      sorter: (a, b) => a.startTime - b.startTime,
    },
    {
      title: "状态",
      align: "center",
      width: 80,
      dataIndex: "taskStatus", // 1开 2关
      key: "taskStatus",
      render: (_, record) => (
        <div>
          <StatusLabel className={record.taskStatus === 1 ? "open" : "close"}>
            {record.taskStatus === 1 ? "开启" : "关闭"}
          </StatusLabel>
        </div>
      ),
    },
    {
      title: "巡课记录",
      align: "center",
      width: 120,
      dataIndex: "patrolRecordCount",
      key: "patrolRecordCount",
      render: (_, record) => (
        <div>
          <span>{_}</span>
          {record.usePatrolRule === 1 && (
            <Dropdown
              overlay={
                <Menu>
                  {record.incompleteCount > 0 && (
                    <Menu.Item key="setting">
                      <span style={{ color: "#FFAA00" }}>
                        {record.incompleteCount}人考核未完成
                      </span>
                    </Menu.Item>
                  )}
                  <Menu.Item key="delete">
                    <span style={{ color: "#17BE6B" }}>
                      {record.completeCount}人考核完成
                    </span>
                  </Menu.Item>
                </Menu>
              }
              placement="bottomLeft"
              arrow={{ pointAtCenter: true }}
            >
              <i
                className="iconfont iconbaogao"
                style={{
                  position: "relative",
                  top: 1,
                  marginLeft: 4,
                  color: record.incompleteCount > 0 ? "#FFAA00" : "#17BE6B",
                }}
              ></i>
            </Dropdown>
          )}
        </div>
      ),
    },
    {
      title: "备注",
      align: "center",
      dataIndex: "remarks",
      key: "remarks",
      ellipsis: {
        showTitle: false,
      },
      render: remarks => (
        <Tooltip
          placement="topLeft"
          title={remarks}
        >
          {remarks}
        </Tooltip>
      ),
    },
    {
      title: "操作",
      align: "center",
      width: 160,
      key: "control",
      render: (_, record) => (
        <div>
          <span
            onClick={() => handleToggle(record)}
            className="cursor-pointer text-blue-500"
          >
            {record.taskStatus === 1 ? "关闭" : "开启"}
          </span>
          <Divider type="vertical" />
          <span
            onClick={() => handleCopyTask(record)}
            className="cursor-pointer text-blue-500"
          >
            复制
          </span>
          <Divider type="vertical" />
          <Dropdown
            overlay={
              <Menu onClick={_ => handleControlBtns(_, record)}>
                <Menu.Item key="detail">
                  <span
                    onClick={() =>
                      navigate(`/task/${record.taskId}?from=${uuid()}`)
                    }
                  >
                    详情
                  </span>
                </Menu.Item>
                <Menu.Item key="setting">
                  <span>设置</span>
                </Menu.Item>
                <Menu.Item key="delete">
                  <span>删除</span>
                </Menu.Item>
              </Menu>
            }
            placement="bottomRight"
          >
            <span className="cursor-pointer text-blue-500 font-bold">···</span>
          </Dropdown>
        </div>
      ),
    },
  ]
  const createModalRef = useRef()
  const copyModalRef = useRef()
  const taskSettingRef = useRef()

  const [pagination, setPagination] = useState({
    taskName: "",
    date: "",
    pageNo: 1,
    pageSize: 10,
  })

  const [data, setData] = useState({
    list: [],
    total: 0,
  })

  useEffect(() => {
    async function fetchList() {
      setLoading(true)
      const { data, totalDatas } = await getAdminTasks(pagination)
      setData({ list: data, total: totalDatas * 1 })
      setLoading(false)
    }
    fetchList()
  }, [pagination])
  /* 关闭、开启 */
  const handleToggle = async ({ taskId, taskStatus }) => {
    const params = {
      taskId,
      status: taskStatus === 1 ? 2 : 1,
    }
    const { code } = await updateTaskStatus(params)
    if (code === 0) {
      message.success("修改成功")
      setPagination({ ...pagination })
    }
  }
  /* 控制··· */
  const handleControlBtns = async ({ key }, data) => {
    if (key === "delete") {
      onDelete(data.taskId)
    }
    if (key === "setting") {
      taskSettingRef.current.showModal(data.taskId)
    }
  }
  const onDelete = taskId => {
    confirm({
      title: "确定删除这条巡课任务吗？",
      icon: <QuestionCircleFilled />,
      content: "删除这条巡课任务，所有此任务内的数据都会一并删除",
      onOk() {
        deleteTask({ taskId }).then(({ code }) => {
          if (code === 0) {
            message.success("删除成功")
            setPagination({ ...pagination, pageNo: 1, pageSize: 10 })
          }
        })
      },
    })
  }
  const handleCopyTask = ({ taskId, taskName }) => {
    copyModalRef.current.showModal({ taskId, taskName })
  }

  const [form] = Form.useForm()
  const onReset = () => {
    form.resetFields()
    setPagination({
      taskName: "",
      date: "",
      pageNo: 1,
      pageSize: 10,
    })
  }
  const onFinish = values => {
    let date = values.date ? moment(values.date).format("YYYY-MM-DD") : ""
    let taskName = values.taskName ? values.taskName : ""
    setPagination({
      taskName,
      date,
      pageNo: 1,
      pageSize: 10,
    })
  }
  return (
    <Root>
      <div className="title">巡课任务</div>
      <div className="filterBox">
        <Form
          onFinish={onFinish}
          form={form}
          layout="inline"
          name="filterForm"
        >
          <Form.Item
            name="taskName"
            label="搜索"
          >
            <Input
              style={{ width: 200 }}
              placeholder="请输入任务名称"
            />
          </Form.Item>
          <Form.Item
            name="date"
            label="日期"
          >
            <DatePicker />
          </Form.Item>
          <Form.Item>
            <Button
              htmlType="submit"
              type="primary"
            >
              查询
            </Button>
          </Form.Item>
          <Form.Item>
            <Button onClick={onReset}>重置</Button>
          </Form.Item>
        </Form>
      </div>
      <div className="content">
        <div className="content-title flex justify-between items-center">
          <div>任务列表</div>
          <Space>
            <Button
              onClick={() => createModalRef.current.showModal()}
              icon={<PlusOutlined />}
              type="primary"
            >
              添加
            </Button>
          </Space>
        </div>
        <Table
          loading={loading}
          className="mt-5"
          rowKey={columns => columns.taskId}
          dataSource={data.list}
          columns={columns}
          pagination={false}
        />
        <div className="text-right mt-5">
          <Pagination
            className="inline-block"
            current={pagination.pageNo}
            pageSize={pagination.pageSize}
            onChange={(page, pageSize) =>
              setPagination({ ...pagination, pageNo: page, pageSize })
            }
            total={data.total}
            showSizeChanger
            showQuickJumper
            showTotal={total => `总共 ${total} 条`}
          />
        </div>
      </div>
      <CreateModal
        onCreateSuccess={() => setPagination({ ...pagination })}
        ref={createModalRef}
      />
      <CopyModal
        onCopySuccess={() => setPagination({ ...pagination })}
        ref={copyModalRef}
      />
      <TaskSetting
        onSetSuccess={() => setPagination({ ...pagination })}
        ref={taskSettingRef}
      />
    </Root>
  )
}

export default AdminTask
