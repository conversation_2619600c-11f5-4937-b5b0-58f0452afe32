/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from "react";
import { v4 as uuid } from "uuid";
import { Modal, Checkbox } from "antd";
import { getTaskById, updateTaskById, forminfoSaveData } from "@/api";
import { QuestionCircleFilled } from "@ant-design/icons";
import { Clickoutside, TemSelector } from "@/components";
import { useTourInfo, TourObjRoot } from "@/hooks/useTourInfo";
import moment from "moment";
import {
  Form,
  Input,
  DatePicker,
  Radio,
  Tooltip,
  Button,
  TimePicker,
  message,
  Switch,
} from "antd";
import {
  InfoCircleFilled,
  MinusCircleOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import styled from "styled-components";
const FormTitle = styled.div`
  position: relative;
  font-weight: bold;
  color: #262626;
  margin-bottom: 24px;
  padding-left: 10px;
  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background: #007aff;
  }
`;
const LookTimeAdd = styled.div`
  border: 1px dashed #d9d9d9;
  width: 274px;
  height: 36px;
  margin-top: 16px;
  &:hover {
    border-color: #007aff;
    i,
    span {
      color: #007aff;
    }
  }
`;

function TaskBasicSetting({ taskId }) {
  const [look, setLook] = useState({
    type: 1,
    list: [{ id: 0, time: [] }],
  });
  const [form] = Form.useForm();

  useEffect(() => {
    getTaskById({ taskId }).then((res) => {
      if (res.code === 0 && res.data) {
        /* 允许控制云平台摄像头 */
        const type = res.data.limitType ? res.data.limitType : 1;
        const list = res.data.patrolTaskCustomTimes.map((item, index) => {
          return {
            id: index === 0 ? 0 : uuid(),
            time: [item.startTime, item.endTime],
          };
        });
        setLook({ type, list });
        form.setFields([
          {
            name: "taskName",
            value: res.data.taskName,
          },
          {
            name: "remarks",
            value: res.data.remarks,
          },
          {
            name: "time",
            value: [moment(res.data.startTime), moment(res.data.endTime)],
          },
        ]);
        const {
          useTable,
          formInfoId,
          formInfoName,
          formTemplateInfoId,
          patrolObject,
        } = res.data;
        const {
          personalIsFill,
          personalIsShow,
          gradeIsFill,
          gradeIsShow,
          classIsFill,
          classIsShow,
        } = patrolObject;
        setTourInfo({
          personalIsFill,
          personalIsShow,
          gradeIsFill,
          gradeIsShow,
          classIsFill,
          classIsShow,
          validateStatus: "sucess",
          errorMsg: "",
        });
        setTempInfo({
          showSelector: false,
          useTable, // 1开 2关
          formInfoId,
          formInfo: "",
          formName: formInfoName,
          formTemplateInfoId,
        });
      }
    });
  }, [taskId, form]);

  const handleChangeType = (e) => {
    setLook({
      ...look,
      type: e.target.value,
    });
  };

  const timeChange = (_, timeString, id) => {
    const list = look.list;
    const target = list.find((item) => item.id === id);
    if (!target) return;
    target.time = timeString;
    setLook({
      type: look.type,
      list,
    });
  };

  const handleDelete = (index) => {
    const list = look.list;
    list.splice(index, 1);
    setLook({
      type: look.type,
      list,
    });
  };

  const handleAddTime = () => {
    const list = look.list;
    list.push({ id: uuid(), time: [] });
    setLook({
      type: look.type,
      list,
    });
  };

  const submit = async (values) => {
    const tourResult = await validateCheckTourInfo();
    if (tourResult === "error") return;
    if (!values) return;
    let patrolTaskCustomTimes = [];
    if (look.type === 2) {
      patrolTaskCustomTimes = look.list.reduce((pre, cur) => {
        if (cur.time[0] && cur.time[1]) {
          pre.push({
            startTime: cur.time[0],
            endTime: cur.time[1],
          });
        }
        return pre;
      }, []);
    }
    if (look.type === 2 && !patrolTaskCustomTimes.length) {
      return message.error("请添加自定义时间");
    }

    const data = {
      taskName: values.taskName,
      startTime: values.time[0].format("YYYY-MM-DD"),
      endTime: values.time[1].format("YYYY-MM-DD"),
      remarks: values.remarks,
      taskId,
      limitType: look.type,
      patrolTaskCustomTimes,
      useTable: tempInfo.useTable,
      formInfoName: tempInfo.formName,
      formTemplateInfoId: tempInfo.formTemplateInfoId,
      formInfoId: tempInfo.formInfoId,
      personalIsFill: tourInfo.personalIsFill,
      personalIsShow: tourInfo.personalIsShow,
      gradeIsFill: tourInfo.gradeIsFill,
      gradeIsShow: tourInfo.gradeIsShow,
      classIsFill: tourInfo.classIsFill,
      classIsShow: tourInfo.classIsShow,
    };
    if (tempInfo.useTable === 1 && !tempInfo.formTemplateInfoId) {
      return message.error("请选择表单");
    }
    if (
      tempInfo.useTable === 1 &&
      tempInfo.formInfo &&
      !tempInfo.formInfoId &&
      tempInfo.formTemplateInfoId &&
      tempInfo.formName
    ) {
      const result = await forminfoSaveData({
        formInfo: tempInfo.formInfo,
        formTemplateInfoId: tempInfo.formTemplateInfoId,
        usedObjId: taskId,
        usedObjType: 1,
      });
      if (result.code === 0) {
        data.formInfoId = result.data.id;
        const res = await updateTaskById(data);
        if (res.code === 0) {
          message.success("保存成功");
        }
      }
    } else {
      const result = await updateTaskById(data);
      if (result.code === 0) {
        message.success("保存成功");
      }
    }
    // 表单判断
  };

  const [tempInfo, setTempInfo] = useState({
    showSelector: false,
    useTable: 1, // 1开 2关
    formInfoId: "",
    formInfo: "",
    formName: "",
    formTemplateInfoId: "",
  });
  const onChangeTemp = ({ formName, formTemplateInfoId, formInfo }) => {
    const onOk = () => {
      setTempInfo({
        showSelector: false,
        useTable: 1,
        formInfoId: "",
        formInfo,
        formName,
        formTemplateInfoId,
      });
    };
    if (tempInfo.formInfoId) {
      Modal.confirm({
        title: `确认更换表单吗？`,
        content: "更换表单将清除已经产生的表单数据",
        icon: <QuestionCircleFilled />,
        centered: true,
        onOk,
      });
    } else {
      onOk();
    }
  };
  const onSwitchTableOpen = (value) => {
    const onOk = () => {
      setTempInfo({
        showSelector: false,
        useTable: 2, // 1开 2关
        formInfoId: "",
        formInfo: "",
        formName: "",
        formTemplateInfoId: "",
      });
    };
    // 开启
    if (value) {
      setTempInfo({
        ...tempInfo,
        useTable: 1,
      });
    } else {
      // 关闭-- 有记录
      if (tempInfo.formInfoId) {
        Modal.confirm({
          title: `确认关闭使用量表吗？`,
          content: "不使用表单，将清除已经产生的表单数据",
          icon: <QuestionCircleFilled />,
          centered: true,
          onOk,
        });
      } else {
        onOk();
      }
    }
  };

  const { tourInfo, setTourInfo, onChangeTourInfo, validateCheckTourInfo } =
    useTourInfo();

  return (
    <Form
      form={form}
      labelCol={{ span: 6 }}
      style={{ width: 400 }}
      onFinish={submit}
    >
      <FormTitle>任务信息</FormTitle>
      <Form.Item
        label="任务名称"
        name="taskName"
        rules={[{ required: true, message: "请输入" }]}
      >
        <Input maxLength="50" placeholder="请输入" />
      </Form.Item>
      <Form.Item
        label="时间"
        name="time"
        rules={[{ required: true, message: "请选择时间" }]}
      >
        <DatePicker.RangePicker className="w-full" />
      </Form.Item>

      <Form.Item
        label="巡课对象"
        validateStatus={tourInfo.validateStatus}
        help={tourInfo.errorMsg}
        required
      >
        <TourObjRoot>
          <div
            className={`tourItem flex items-center px-3 ${tourInfo.validateStatus}`}
          >
            <span className="flex-1">个人</span>
            <Checkbox
              onChange={() => onChangeTourInfo("personalIsShow")}
              checked={tourInfo.personalIsShow === 1}
            >
              显示
            </Checkbox>
            <Checkbox
              onChange={() => onChangeTourInfo("personalIsFill")}
              checked={tourInfo.personalIsFill === 1}
            >
              必填
            </Checkbox>
          </div>
          <div
            className={`tourItem flex items-center px-3 ${tourInfo.validateStatus}`}
          >
            <span className="flex-1">年级</span>
            <Checkbox
              onChange={() => onChangeTourInfo("gradeIsShow")}
              checked={tourInfo.gradeIsShow === 1}
            >
              显示
            </Checkbox>
            <Checkbox
              onChange={() => onChangeTourInfo("gradeIsFill")}
              checked={tourInfo.gradeIsFill === 1}
            >
              必填
            </Checkbox>
          </div>
          <div
            className={`tourItem flex items-center px-3 ${tourInfo.validateStatus}`}
          >
            <span className="flex-1">班级</span>
            <Checkbox
              onChange={() => onChangeTourInfo("classIsShow")}
              checked={tourInfo.classIsShow === 1}
            >
              显示
            </Checkbox>
            <Checkbox
              onChange={() => onChangeTourInfo("classIsFill")}
              checked={tourInfo.classIsFill === 1}
            >
              必填
            </Checkbox>
          </div>
        </TourObjRoot>
      </Form.Item>

      <Form.Item label="备注" name="remarks">
        <Input.TextArea maxLength={200} rows={4} placeholder="请输入" />
      </Form.Item>
      <FormTitle>视频观看控制</FormTitle>
      <Form.Item label="视频观看时段">
        <div className="mt-1">
          <Radio.Group value={look.type} onChange={handleChangeType}>
            <Radio value={1}>
              <span className="mr-2">无限制</span>
              <Tooltip
                placement="bottom"
                title={"在任务时间段内，都可以查看视频监控"}
              >
                <InfoCircleFilled
                  className="cursor-default"
                  style={{ color: "#BFBFBF" }}
                />
              </Tooltip>
            </Radio>
            <Radio value={2}>
              <span className="mr-2">自定义</span>
              <Tooltip
                placement="bottom"
                title={"在任务时间段内，可以自由设定允许查看视频监控的时段"}
              >
                <InfoCircleFilled
                  className="cursor-default"
                  style={{ color: "#BFBFBF" }}
                />
              </Tooltip>
            </Radio>
          </Radio.Group>
        </div>

        {look.type === 2 &&
          look.list.map((item, index) => (
            <div key={item.id} className="flex items-center mt-4">
              <TimePicker.RangePicker
                format="HH:mm"
                value={[
                  item.time[0] ? moment(item.time[0], "HH:mm") : "",
                  item.time[1] ? moment(item.time[1], "HH:mm") : "",
                ]}
                onChange={(time, timeString) =>
                  timeChange(time, timeString, item.id)
                }
                className="flex-1 mr-3"
              ></TimePicker.RangePicker>
              <MinusCircleOutlined
                onClick={() => handleDelete(index)}
                className={`cursor-pointer text-gray-400 ${
                  index === 0 ? "invisible" : ""
                }`}
              />
            </div>
          ))}

        {look.type === 2 && (
          <LookTimeAdd
            onClick={handleAddTime}
            className="flex items-center cursor-pointer leading-9 text-center"
          >
            <div className="inline-block flex-1 text-gray-400">
              <PlusOutlined />
              <span>添加</span>
            </div>
            <MinusCircleOutlined className="cursor-pointer text-gray-400 hidden" />
          </LookTimeAdd>
        )}
      </Form.Item>
      <FormTitle>评课量表</FormTitle>

      <Form.Item label="使用量表">
        <Switch
          onChange={onSwitchTableOpen}
          checked={tempInfo.useTable === 1 ? true : false}
        />

        {tempInfo.useTable === 1 && (
          <Clickoutside
            cb={() => {
              setTempInfo((pre) => ({
                ...pre,
                showSelector: false,
              }));
            }}
          >
            <div className="absolute mt-2 z-10 bg-white">
              <Input
                onFocus={() => {
                  setTempInfo({
                    ...tempInfo,
                    showSelector: true,
                  });
                }}
                readOnly
                value={tempInfo.formName}
                style={{ width: "328px" }}
                placeholder="请选择表单"
              />
              {tempInfo.showSelector && (
                <TemSelector
                  onChange={onChangeTemp}
                  value={tempInfo.formTemplateInfoId}
                />
              )}
            </div>
          </Clickoutside>
        )}
      </Form.Item>

      <Form.Item wrapperCol={{ offset: 6, span: 16 }}>
        <Button className="mt-10" type="primary" htmlType="submit">
          确定
        </Button>
      </Form.Item>
    </Form>
  );
}

export default TaskBasicSetting;
