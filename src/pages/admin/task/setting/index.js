import React, { useState, useImperativeHandle, forwardRef } from "react"
import { Drawer } from "antd"
import { CloseOutlined } from "@ant-design/icons"
import styled from "styled-components"
import TaskBasicSetting from "./basic"
import TaskPlan from "./taskPlan"
import TaskRule from "./taskRule"

const SettingNav = styled.div`
  margin-bottom: 30px;
  span {
    cursor: pointer;
    padding-bottom: 10px;
    border-bottom: 2px solid transparent;
  }
  span.active {
    color: #007aff;
    border-bottom-color: #007aff;
  }
`

function TaskSetting(props, ref) {
  const [active, setActive] = useState(1)
  const [setInfo, changeSetInfo] = useState({ taskId: 0, show: false })
  const onClose = () => {
    setActive(1)
    props.onSetSuccess()
    changeSetInfo(c => ({ ...c, show: false }))
  }
  useImperativeHandle(ref, () => ({
    showModal(taskId) {
      changeSetInfo(c => ({ ...c, show: true, taskId }))
    },
  }))
  return (
    <Drawer
      title="设置"
      width="1200px"
      closable={false}
      visible={setInfo.show}
      extra={
        <CloseOutlined
          onClick={onClose}
          className="cursor-pointer"
        />
      }
    >
      <div className="flex flex-col h-full">
        <SettingNav>
          <span
            onClick={() => setActive(1)}
            className={`mr-8 ${active === 1 ? "active" : ""}`}
          >
            基础设置
          </span>
          <span
            onClick={() => setActive(2)}
            className={`mr-8 ${active === 2 ? "active" : ""}`}
          >
            任务安排
          </span>
          <span
            onClick={() => setActive(3)}
            className={`mr-8 ${active === 3 ? "active" : ""}`}
          >
            巡课规则
          </span>
        </SettingNav>
        <div className="flex-1 overflow-y-auto overflow-x-hidden">
          {active === 1 && <TaskBasicSetting taskId={setInfo.taskId} />}
          {active === 2 && <TaskPlan taskId={setInfo.taskId} />}
          {active === 3 && <TaskRule taskId={setInfo.taskId} />}
        </div>
      </div>
    </Drawer>
  )
}

export default forwardRef(TaskSetting)
