/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect, useRef } from "react";
import {
  searchByUserName,
  addTaskMember,
  selectMemberByPage,
  deleteTaskMember,
  updateTaskAllowControl,
  getTaskById,
  getMychannels,
} from "@/api";
import {
  Table,
  Pagination,
  Switch,
  AutoComplete,
  message,
  Divider,
  Modal,
  Tree,
  Input,
  Tabs,
} from "antd";
import { QuestionCircleFilled, PlusCircleOutlined } from "@ant-design/icons";
import { $message, $getTree, $hidePhone, getUrlQuery } from "@/tools";
import styled from "styled-components";
import TaskPlanSetting from "./taskPlanSetting";
const { Option } = AutoComplete;
const { confirm } = Modal;
const { Search } = Input;

const TaskPlan = ({ taskId }) => {
  return (
    <div className="flex h-full">
      <TaskPlanLeft taskId={taskId} />
      <TaskPlanRight></TaskPlanRight>
    </div>
  );
};
const PlanRightRoot = styled.div`
  overflow: auto;
  width: 360px;
  background: #f7f7f7;
  border-radius: 4px;
  padding: 12px 24px;
  margin: 0 -24px -24px 0;

  /* width: 460px;
  background: #f7f7f7;
  border-radius: 4px;
  padding: 12px 0;
  .title {
    font-weight: bold;
    color: #262626;
    margin-bottom: 12px;
  }
  .minitor {
    vertical-align: top;
    display: inline-block;
    height: 100%;
    width: 230px;
    border-right: 1px solid #e5e5e5;
    overflow-x: auto;
  }
  .equip {
    vertical-align: top;
    height: 100%;
    display: inline-block;
    width: 230px;
  } */
`;
function TaskPlanRight() {
  // 监控
  const [monitorNum, setMonitorNum] = useState(0);
  const [monitor, setMonitor] = useState([]);
  // 录播设备
  const [equip, setEquip] = useState([]);
  // 教室
  const [classPlace, setClassPlace] = useState([]);
  const [classPlaceNum, setClassPlaceNum] = useState(0);
  // 班级
  const [gradePlace, setGradePlace] = useState([]);
  const [gradePlaceNum, setGradePlaceNum] = useState(0);
  const onInitData = async (taskMemberId) => {
    if (!taskMemberId) {
      setMonitor([]);
      setEquip([]);
      setMonitorNum(0);
      return;
    }
    const result = await getMychannels({ taskMemberId });
    if (result.code === 0) {
      if (result.data.type1 && Array.isArray(result.data.type1)) {
        let num = 0;
        const monitorTree = result.data.type1.map((item) => {
          if (item.nodeType) {
            num++;
          }
          return {
            ...item,
            key: item.monitoringNodeId,
            title: item.name,
            parentId: item.parentNodeId,
            id: item.monitoringNodeId,
          };
        });
        setMonitor($getTree(monitorTree, "0"));
        setMonitorNum(num);
      }
      if (result.data.type2 && Array.isArray(result.data.type2)) {
        setEquip(result.data.type2);
      }
      if (result.data.type3 && Array.isArray(result.data.type3)) {
        setEquip((pre) => [...pre, ...result.data.type3]);
      }
      // 教室
      if (result.data.type4 && Array.isArray(result.data.type4)) {
        let num = 0;
        let placeList = result.data.type4.map((item) => {
          if (item.nodeType && item.nodeType === "ROOM") {
            num++;
          }
          return {
            ...item,
            key: item.id,
            title: item.value,
            parentId: item.parentId,
          };
        });

        placeList = placeList.filter((node) => node.nodeType !== "UNIT");
        setClassPlace($getTree(placeList, getUrlQuery("bureauId")));
        setClassPlaceNum(num);
      }
      // 班级
      if (result.data.type5 && Array.isArray(result.data.type5)) {
        let num = 0;
        let graderList = result.data.type5.map((item) => {
          if (!item.type) {
            num++;
          }
          return {
            ...item,
            key: item.id,
            title: item.name,
            parentId: item.parentId,
          };
        });

        setGradePlace($getTree(graderList, "0"));
        setGradePlaceNum(num);
      }
    }
  };
  useEffect(() => {
    $message.on("rowChoosed", onInitData);
    return () => {
      $message.off("rowChoosed", onInitData);
    };
  }, []);
  return (
    <PlanRightRoot>
      <Tabs defaultActiveKey="1">
        <Tabs.TabPane tab={`教室  ${classPlaceNum}`} key="1">
          {classPlace.length > 0 && (
            <Tree defaultExpandAll treeData={classPlace} />
          )}
        </Tabs.TabPane>
        <Tabs.TabPane tab={`班级  ${gradePlaceNum}`} key="2">
          {gradePlace.length > 0 && (
            <Tree defaultExpandAll treeData={gradePlace} />
          )}
        </Tabs.TabPane>
        <Tabs.TabPane tab={`监控  ${monitorNum}`} key="3">
          {monitor.length > 0 && <Tree defaultExpandAll treeData={monitor} />}
        </Tabs.TabPane>
        <Tabs.TabPane tab={`录播设备  ${equip.length}`} key="4">
          {equip.map((item, index) => (
            <div key={index} className="equip-item truncate mb-2">
              <span>{item.name}</span>
              {item.orgName && <span> / {item.orgName}</span>}
            </div>
          ))}
        </Tabs.TabPane>
      </Tabs>

      {/* todo */}
      {/* <div className="minitor px-3">
        <div className="title">监控 {monitorNum}</div>
        {monitor.length > 0 && (
          <Tree
            defaultExpandAll
            treeData={monitor}
          />
        )}
      </div>
      <div className="equip px-3">
        <div className="title">录播设备 {equip.length}</div>
        {equip.map((item, index) => (
          <div
            key={index}
            className="equip-item truncate mb-2"
          >
            <span>{item.name}</span>
            {item.orgName && <span> / {item.orgName}</span>}
          </div>
        ))}
      </div> */}
    </PlanRightRoot>
  );
}
const PlanLeftRoot = styled.div`
  overflow: auto;
`;
const FilterContainer = styled.div`
  &:hover .filterIcon {
    opacity: 1;
  }
  .filterIcon {
    font-size: 18px;
    opacity: 0;
    margin-left: 10px;
  }
`;
function TaskPlanLeft({ taskId }) {
  const columns = [
    {
      title: "序号",
      width: 80,
      key: "index",
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: "姓名",
      dataIndex: "userName",
      key: "userName",
      ellipsis: {
        showTitle: false,
      },
    },
    {
      title: "机构",
      dataIndex: "orgName",
      key: "orgName",
      ellipsis: {
        showTitle: false,
      },
    },
    {
      title: "手机号",
      width: 130,
      dataIndex: "phone",
      key: "phone",
      render: (_) => <span>{$hidePhone(_)}</span>,
    },
    {
      title: "通道数量",
      align: "center",
      dataIndex: "passagewayCount",
      key: "passagewayCount",
      sorter: (a, b) => a.passagewayCount - b.passagewayCount,
      render: (_, record) => <span>{_ + record.roomCount}</span>,
    },
    {
      title: "操作",
      width: 140,
      align: "center",
      key: "control",
      render: (_, record) => (
        <div>
          <span
            onClick={(e) => handleSetPassway(e, record)}
            className="cursor-pointer text-blue-500"
          >
            设置通道
          </span>
          <Divider type="vertical" />
          <span
            onClick={(e) => handleDel(e, record)}
            className="cursor-pointer text-blue-500"
          >
            移除
          </span>
        </div>
      ),
    },
  ];
  const setRef = useRef();
  const [data, setData] = useState({ list: [], total: 0 });
  const [pagination, setPagination] = useState({ pageNo: 1, pageSize: 10 });
  const [searchName, setSearchName] = useState("");
  // =======
  const [value, setValue] = useState("");
  const [options, setOptions] = useState([]);
  const [checked, setChecked] = useState(false);

  useEffect(() => {
    getList(1, 10, "");
    getTaskById({ taskId }).then((res) => {
      if (res.code === 0) {
        setChecked(res.data.allowControl === 1);
      }
    });
  }, [taskId]);

  function getList(pageNo, pageSize, searchName) {
    selectMemberByPage({
      sort: 0,
      status: 0,
      pageNo,
      pageSize,
      taskId,
      searchName,
    }).then((res) => {
      if (res.code === 0) {
        setData({
          list: res.data,
          total: res.totalDatas * 1,
        });
      }
    });
  }

  useEffect(() => {
    return setRef.current.hideSetting;
  }, []);
  const onSearch = async (userName) => {
    if (!userName) {
      setOptions([]);
    } else {
      const result = await searchByUserName({ userName });
      if (result.code === 0) {
        const data = result.data.map((item) => ({
          value: item.coreUserInfoId,
          label: item.userName,
          phone: item.phone,
          orgName: item.orgName,
        }));
        setOptions(data);
      }
    }
  };
  const onSelect = async (value) => {
    const target = options.find((item) => item.value === value);
    setValue(target.label);
    const result = await addTaskMember({ taskId, userId: target.value });
    if (result.code === 0) {
      message.success("添加成功");
      setPagination({ ...pagination });
      getList(pagination.pageNo, pagination.pageSize, searchName);
    }
  };

  const handleSwitchChange = async (checked) => {
    const result = await updateTaskAllowControl({
      taskId,
      allowControl: checked ? 1 : 2,
    });
    if (result.code === 0) {
      setChecked(checked);
      message.success("设置成功");
    } else {
      setChecked(!checked);
    }
  };
  const handleSetPassway = (e, { taskMemberId }) => {
    e.stopPropagation();
    setRef.current.showSetting(taskMemberId);
  };
  const handleDel = (e, { userName, taskMemberId }) => {
    e.stopPropagation();
    confirm({
      title: `确定移除${userName}吗？`,
      icon: <QuestionCircleFilled />,
      onOk() {
        deleteTaskMember({ taskMemberId }).then((res) => {
          if (res.code === 0) {
            message.success("删除成功");
            // setPagination({ pageNo: 1, pageSize: 10 })
            getList(1, pagination.pageSize, searchName);
            $message.emit("rowChoosed", 0);
          }
        });
      },
    });
  };

  const handleClickRow = ({ taskMemberId }) => {
    $message.emit("rowChoosed", taskMemberId);
  };
  const onSuccess = () => {
    handleClickRow({ taskMemberId: 0 });
    // setPagination({ ...pagination })
    getList(pagination.pageNo, pagination.pageSize, searchName);
  };
  // ==========
  const onFilter = () => {
    setPagination((pre) => ({
      pageNo: 1,
      pageSize: pre.pageSize,
    }));
    getList(1, pagination.pageSize, searchName);
  };
  return (
    <PlanLeftRoot className="flex-1 pr-6 w-0">
      <div>
        <span className="mr-2">添加人员:</span>
        <AutoComplete
          value={value}
          style={{
            width: 440,
          }}
          onSelect={onSelect}
          onSearch={onSearch}
          onChange={setValue}
          placeholder="请输入姓名搜索"
        >
          {options.map((option) => {
            return (
              <Option key={option.value}>
                <FilterContainer className="flex justify-between items-center">
                  <span className="flex-1 truncate">{option.label}</span>
                  {option.phone ? (
                    <span style={{ color: "#8C8C8C" }} className="flex-1 mx-2">
                      {$hidePhone(option.phone)}
                    </span>
                  ) : (
                    <span className="flex-1 mx-2"></span>
                  )}
                  <span
                    style={{ color: "#8C8C8C" }}
                    className="flex-1 truncate"
                  >
                    {option.orgName}
                  </span>
                  <PlusCircleOutlined
                    className="filterIcon"
                    style={{ color: "#007AFF" }}
                  />
                </FilterContainer>
              </Option>
            );
          })}
        </AutoComplete>
      </div>
      <div className="flex justify-between items-center mt-5">
        <div className="font-bold">
          <Search
            placeholder="请输入姓名查询"
            value={searchName}
            onChange={(e) => setSearchName(e.target.value)}
            onSearch={onFilter}
            style={{ width: 280 }}
          />
        </div>
        <div className="flex items-center">
          <span className="mr-3">允许控制云平台摄像头</span>
          <Switch checked={checked} onChange={handleSwitchChange} />
        </div>
      </div>
      <Table
        onRow={(record) => ({
          onClick: (e) => {
            handleClickRow(record);
          },
        })}
        className="mt-5"
        rowKey={(columns) => columns.userId}
        dataSource={data.list}
        columns={columns}
        pagination={false}
      />
      <div className="text-right mt-5">
        <Pagination
          className="inline-block"
          current={pagination.pageNo}
          pageSize={pagination.pageSize}
          onChange={(page, pageSize) => {
            setPagination((pre) => ({
              ...pre,
              pageNo: page,
              pageSize,
            }));
            getList(page, pageSize, searchName);
          }}
          total={data.total}
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `总共 ${total} 条`}
        />
      </div>
      <TaskPlanSetting
        onSuccess={onSuccess}
        ref={(c) => (setRef.current = c)}
      />
    </PlanLeftRoot>
  );
}

export default TaskPlan;
