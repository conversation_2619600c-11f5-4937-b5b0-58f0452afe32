import React, {
  useState,
  useEffect,
  useImperativeHandle,
  forwardRef,
  useRef,
  useMemo,
} from "react";
import { $getTree, getUrlQuery } from "@/tools";
import {
  getAllChannel,
  getMychannels,
  getAllGroup,
  getMyGroup,
  editMychannels,
  getMagicClassRoom,
  getPatrolClassInfo,
} from "@/api";
import styled from "styled-components";
import {
  Button,
  Space,
  Breadcrumb,
  Tree,
  message,
  Table,
  Pagination,
  Tooltip,
  Input,
  Empty,
} from "antd";
import { LeftOutlined } from "@ant-design/icons";
import { OrgTreeSelector } from "@/components";
const Root = styled.div`
  top: 55px;
  .content {
    /* overflow-y: auto; */
    .tab {
      margin-top: 20px;
      border-bottom: 1px solid #f0f0f0;
      div {
        cursor: pointer;
        padding-bottom: 10px;
        display: inline-block;
        margin-right: 20px;
        border-bottom: 2px solid transparent;
      }
      div.active {
        color: #007aff;
        border-bottom-color: #007aff;
      }
    }
  }
  .footer {
    border-top: 1px solid #e5e5e5;
  }
`;
const TaskPlanSetting = ({ onSuccess }, ref) => {
  const channelRef = useRef();
  const groupRef = useRef();
  const [show, setShow] = useState(false);
  const [id, setId] = useState("");
  const [active, setActive] = useState(1);
  // 所有教室列表
  const [placeClass, setPlaceClass] = useState([]);
  // 所有班级列表
  const [gradePlace, setGradePlace] = useState([]);
  // 所有监控列表
  const [monitor, setMonitor] = useState([]);
  // 所有录播设备列表
  const [equipList, setEquipList] = useState([]);
  // 初始选中的监控与录播设备与场所教室
  const [defaultChecked, setDefaultChecked] = useState({
    type1: [],
    type2: [],
    type3: [],
    type4: [],
    type5: [],
  });
  // 初始化选中的分组
  const [defaultGroup, setDefaultGroup] = useState([]);
  useImperativeHandle(ref, () => ({
    showSetting(id) {
      setShow(true);
      setId(id);
      initAllChannel(id);
      initMyGroup(id);
      // 场所-教室
      initAllClassPlace();
      initAllGrade();
    },
    hideSetting() {
      setShow(false);
    },
  }));
  const initAllGrade = async () => {
    const bureauId = getUrlQuery("bureauId");
    const result = await getPatrolClassInfo({ orgId: bureauId });
    if (result.data && Array.isArray(result.data)) {
      const gradeList = result.data.map((item) => ({
        ...item,
        title: item.name,
        key: item.id,
      }));
      setGradePlace($getTree(gradeList, "0"));
    }
  };
  const initAllClassPlace = async () => {
    const bureauId = getUrlQuery("bureauId");
    const result = await getMagicClassRoom({ orgId: bureauId });
    if (result.data && Array.isArray(result.data)) {
      const placeList = result.data
        .map((item) => ({
          ...item,
          title: item.value,
        }))
        .filter((node) => node.nodeType !== "UNIT");
      setPlaceClass($getTree(placeList, bureauId));
    }
  };
  const initAllChannel = async (taskMemberId) => {
    const result = await getAllChannel({ taskMemberId });
    if (result.code === 0) {
      if (result.data.type1 && Array.isArray(result.data.type1)) {
        const monitorList = result.data.type1.map((item) => ({
          ...item,
          key: item.monitoringNodeId,
          title: item.name,
          parentId: item.parentNodeId,
          id: item.monitoringNodeId,
        }));
        setMonitor($getTree(monitorList, "0"));
      }

      // 设备信息
      const list = [];
      if (result.data.type2 && Array.isArray(result.data.type2)) {
        result.data.type2.forEach((item) => {
          list.push({
            nodeId: item.monitoringNodeId,
            name: item.name,
            orgName: item.orgName,
            roomName: item.roomName,
            type: "type2",
          });
        });
      }
      if (result.data.type3 && Array.isArray(result.data.type3)) {
        result.data.type3.forEach((item) => {
          list.push({
            nodeId: item.monitoringRecordId,
            name: item.name,
            orgName: item.orgName,
            roomName: item.roomName,
            type: "type3",
          });
        });
      }
      if (result.data.type5 && Array.isArray(result.data.type5)) {
        const gradeList = result.data.type5.map((item) => ({
          ...item,
          key: item.id,
          title: item.name,
          parentId: item.parentId,
        }));
        setGradePlace($getTree(gradeList, "0"));
      }

      setEquipList(list);
      initMyChannels(taskMemberId);
    }
  };
  const initMyChannels = async (taskMemberId) => {
    const result = await getMychannels({ taskMemberId });
    if (result.code === 0) {
      setDefaultChecked(result.data);
    }
  };
  const initMyGroup = async (taskMemberId) => {
    const result = await getMyGroup({ taskMemberId });
    if (result.code === 0) {
      setDefaultGroup(result.data);
    }
  };
  const submit = async () => {
    let data = null;
    if (active === 1) {
      data = {
        ...channelRef.current.getCheckedValue(),
        taskMemberId: id,
      };
    } else {
      data = {
        taskMemberId: id,
        passagewayGroupIds: groupRef.current.getSelectedKey(),
      };
    }
    const result = await editMychannels(data);
    if (result.code === 0) {
      message.success("保存成功");
      if (active === 1) {
        initAllChannel(id);
      } else {
        initMyGroup(id);
      }
    }
  };
  const handleBack = () => {
    setActive(1);
    setShow(false);
    onSuccess();
  };
  const changeTab = (active) => {
    setActive(active);
  };
  if (!show) return null;
  return (
    <Root className="absolute z-10 left-0 bottom-0 right-0 bg-white flex flex-col">
      <div className="content flex flex-col flex-1 h-0 px-6 pt-4">
        <div className="title flex items-center justify-between">
          <Breadcrumb>
            <Breadcrumb.Item>任务安排</Breadcrumb.Item>
            <Breadcrumb.Item>设置通道</Breadcrumb.Item>
          </Breadcrumb>
          <Button onClick={handleBack} icon={<LeftOutlined />} type="link">
            返回上级
          </Button>
        </div>
        <div className="tab">
          <div
            onClick={() => changeTab(1)}
            className={active === 1 ? "active" : ""}
          >
            自定义选择
          </div>
          <div
            onClick={() => changeTab(2)}
            className={active === 2 ? "active" : ""}
          >
            按分组选择
          </div>
        </div>
        <div className="flex-1 overflow-y-auto py-6 -mr-6 pr-6">
          {active === 1 && (
            <CustomChannel
              ref={(c) => (channelRef.current = c)}
              defaultChecked={defaultChecked}
              equipList={equipList}
              placeClass={placeClass}
              gradePlace={gradePlace}
              monitor={monitor}
            />
          )}
          {active === 2 && (
            <GroupChannel
              ref={(c) => (groupRef.current = c)}
              defaultGroup={defaultGroup}
            />
          )}
        </div>
      </div>
      <Space className="footer flex items-center justify-end px-6 h-12">
        <Button onClick={handleBack}>取消</Button>
        <Button onClick={submit} type="primary">
          确定
        </Button>
      </Space>
    </Root>
  );
};

const ChannelRoot = styled.div`
  min-height: 100%;
  .sub-tab {
    margin-bottom: 24px;
    display: flex;
  }
  .sub-tab-item {
    cursor: pointer;
    line-height: 32px;
    height: 32px;
    color: #595959;
    padding: 0 16px;
    border-radius: 16px;
    margin-right: 24px;
  }
  .sub-tab-item:hover {
    background: #f0f1f3;
    color: #007aff;
  }
  .sub-tab-item.active {
    background: #f0f1f3;
    color: #007aff;
  }
  /* .title {
    font-weight: bold;
    color: #262626;
    margin-bottom: 20px;
    span {
      color: #8c8c8c;
      margin-left: 20px;
    }
  }
  .l {
    border-right: 1px solid #f0f0f0;
  } */
`;
const CustomChannel = forwardRef(function (props, ref) {
  const [subTab, setSubTab] = useState(1);
  const { monitor, equipList, defaultChecked, placeClass, gradePlace } = props;
  // 教室
  const [placeKeys, setPlaceKeys] = useState([]);
  // 班级
  const [gradeKeys, setGradeKeys] = useState([]);
  // 监控
  const [checkedKeys, setCheckedKeys] = useState([]);
  // 录播
  const [checkedBox, setCheckedBox] = useState([]);

  const [filterInfo, setFilterInfo] = useState({
    name: "",
    orgName: "",
  });
  const filteredEquipList = useMemo(() => {
    if (filterInfo.name === "" && filterInfo.orgName === "") {
      return equipList;
    }
    if (filterInfo.name && filterInfo.orgName) {
      return equipList.filter((item) => {
        const nameFilter =
          item.name.indexOf(filterInfo.name) !== -1 ||
          (item.orgName && item.orgName.indexOf(filterInfo.name) !== -1);
        const orgFilter =
          item.orgName && item.orgName.indexOf(filterInfo.orgName) !== -1;
        if (nameFilter && orgFilter) {
          return true;
        }
        return false;
      });
    }
    if (filterInfo.name && !filterInfo.orgName) {
      return equipList.filter((item) => {
        if (
          item.name.indexOf(filterInfo.name) !== -1 ||
          (item.orgName && item.orgName.indexOf(filterInfo.name) !== -1)
        ) {
          return true;
        }
        return false;
      });
    }
    if (!filterInfo.name && filterInfo.orgName) {
      return equipList.filter((item) => {
        if (item.orgName && item.orgName.indexOf(filterInfo.orgName) !== -1) {
          return true;
        }
        return false;
      });
    }
    return [];
  }, [filterInfo.name, filterInfo.orgName, equipList]);

  useImperativeHandle(ref, () => ({
    getCheckedValue() {
      // 监控
      const type1 = checkedKeys.map((item) => ({ passagewayId: item }));
      // 教室
      const type4 = placeKeys.map((item) => ({ passagewayId: item }));
      // 班级
      const type5 = gradeKeys.map((item) => ({ passagewayId: item }));
      // 录播设备 ---> type2录播  type3第三方录播
      const type2 = [];
      const type3 = [];
      checkedBox.forEach((item) => {
        const target = equipList.find((equip) => equip.nodeId === item);
        if (target.type === "type2") {
          type2.push({ passagewayId: item });
        }
        if (target.type === "type3") {
          type3.push({ passagewayId: item });
        }
      });
      return {
        type1,
        type2,
        type3,
        type4,
        type5,
      };
    },
  }));
  useEffect(() => {
    // 班级
    const checkedGradeKeys = [];
    defaultChecked.type5.forEach((item) => {
      if (!item.type) {
        checkedGradeKeys.push(item.id);
      }
    });
    setGradeKeys(checkedGradeKeys);
    // 监控
    const checkedPlaceKeys = [];
    defaultChecked.type4.forEach((item) => {
      if (item.nodeType && item.nodeType === "ROOM") {
        checkedPlaceKeys.push(item.id);
      }
    });
    setPlaceKeys(checkedPlaceKeys);

    // 监控
    const checkedKeys = [];
    defaultChecked.type1.forEach((item) => {
      if (item.nodeType) {
        checkedKeys.push(item.monitoringNodeId);
      }
    });
    setCheckedKeys(checkedKeys);

    // 录播--->录播与第三方录播
    const checkedBox = [];
    defaultChecked.type2.forEach((item) => {
      checkedBox.push(item.monitoringNodeId);
    });
    defaultChecked.type3.forEach((item) => {
      checkedBox.push(item.monitoringRecordId);
    });

    setCheckedBox(checkedBox);
  }, [defaultChecked]);

  // 监控选择
  const handleCheckNode = (checkedKeysValue, e) => {
    const checkdeValue = [];
    e.checkedNodes.forEach((item) => {
      if (item.nodeType && item.nodeType === "2") {
        checkdeValue.push(item.monitoringNodeId);
      }
    });
    setCheckedKeys(checkdeValue);
  };
  // 班级选择
  const handleCheckGradeNode = (checkedKeysValue, e) => {
    const checkdeValue = [];
    e.checkedNodes.forEach((item) => {
      if (item.type && item.type === "class") {
        checkdeValue.push(item.id);
      }
    });
    setGradeKeys(checkdeValue);
  };
  // 场所选择
  const handleCheckPlaceNode = (checkedKeysValue, e) => {
    const checkdeValue = [];
    e.checkedNodes.forEach((item) => {
      if (item.nodeType && item.nodeType === "ROOM") {
        checkdeValue.push(item.id);
      }
    });
    setPlaceKeys(checkdeValue);
  };
  const columns = [
    {
      title: "序号",
      width: 60,
      align: "center",
      key: "index",
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: "名称",
      dataIndex: "name",
    },
    {
      title: "机构",
      dataIndex: "orgName",
    },
    {
      title: "教室",
      dataIndex: "roomName",
    },
  ];
  return (
    <ChannelRoot>
      <div className="sub-tab">
        <div
          onClick={() => setSubTab(1)}
          className={`sub-tab-item ${subTab === 1 ? "active" : ""}`}
        >
          教室 {placeKeys.length}
        </div>
        <div
          onClick={() => setSubTab(2)}
          className={`sub-tab-item ${subTab === 2 ? "active" : ""}`}
        >
          班级 {gradeKeys.length}
        </div>
        <div
          onClick={() => setSubTab(3)}
          className={`sub-tab-item ${subTab === 3 ? "active" : ""}`}
        >
          监控 {checkedKeys.length}
        </div>
        <div
          onClick={() => setSubTab(4)}
          className={`sub-tab-item ${subTab === 4 ? "active" : ""}`}
        >
          录播设备 {checkedBox.length}
        </div>
      </div>

      {subTab === 1 && (
        <div>
          {placeClass.length > 0 ? (
            <Tree
              defaultExpandAll
              checkable
              // fieldNames={{
              //   title: "value",
              //   children: "next",
              // }}
              checkedKeys={placeKeys}
              onCheck={handleCheckPlaceNode}
              treeData={placeClass}
            />
          ) : (
            <Empty
              style={{ marginTop: 100 }}
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          )}
        </div>
      )}
      {subTab === 2 && (
        <div>
          {gradePlace.length > 0 ? (
            <Tree
              height={500}
              defaultExpandAll
              checkable
              checkedKeys={gradeKeys}
              onCheck={handleCheckGradeNode}
              treeData={gradePlace}
            />
          ) : (
            <Empty
              style={{ marginTop: 100 }}
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          )}
        </div>
      )}
      {subTab === 3 && (
        <div>
          {monitor.length > 0 ? (
            <Tree
              height={500}
              defaultExpandAll
              checkable
              checkedKeys={checkedKeys}
              onCheck={handleCheckNode}
              treeData={monitor}
            />
          ) : (
            <Empty
              style={{ marginTop: 100 }}
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          )}
        </div>
      )}

      {subTab === 4 && (
        <div>
          <div className="flex items-center">
            <div className="title flex-1">设备列表</div>
            <Input.Search
              onChange={(e) =>
                setFilterInfo((pre) => ({ ...pre, name: e.target.value }))
              }
              style={{ width: 240 }}
              placeholder="请输入名称或机构查询"
            />
            <div style={{ marginLeft: 24, width: 240 }}>
              <OrgTreeSelector
                onOrgChange={(orgName) => {
                  setFilterInfo((pre) => ({
                    ...pre,
                    orgName,
                  }));
                }}
              />
            </div>
          </div>
          <Table
            style={{ marginTop: 20 }}
            pagination={false}
            rowKey={(c) => c.nodeId}
            rowSelection={{
              selectedRowKeys: checkedBox,
              onChange: setCheckedBox,
            }}
            columns={columns}
            dataSource={filteredEquipList}
          />
        </div>
      )}

      {/* <div
        style={{ width: 480 }}
        className="l py-6 pr-6"
      >
        <div className="title">
          监控
          <span>已选{checkedKeys.length}</span>
        </div>
        {monitor.length > 0 && (
          <Tree
            checkable
            checkedKeys={checkedKeys}
            onCheck={handleCheckNode}
            treeData={monitor}
          />
        )}
      </div>
      <div className="flex-1 p-6">
        <div className="title">
          录播设备 <span>已选 {checkedBox.length}</span>
        </div>
        <div style={{ display: "flex" }}>
          <Input.Search
            onChange={e =>
              setFilterInfo(pre => ({ ...pre, name: e.target.value }))
            }
            style={{ width: 300 }}
            placeholder="请输入名称或机构查询"
          />
          <div style={{ marginLeft: 24, flex: 1 }}>
            <OrgTreeSelector
              onOrgChange={orgName => {
                setFilterInfo(pre => ({
                  ...pre,
                  orgName,
                }))
              }}
            />
          </div>
        </div>
        <Table
          style={{ marginTop: 20 }}
          pagination={false}
          rowKey={c => c.nodeId}
          rowSelection={{
            selectedRowKeys: checkedBox,
            onChange: setCheckedBox,
          }}
          columns={columns}
          dataSource={filteredEquipList}
        />
      </div> */}
    </ChannelRoot>
  );
});

const GroupChannel = forwardRef(function ({ defaultGroup }, ref) {
  const [selectedRowKeys, setRowKeys] = useState([]);
  const [data, setData] = useState({ list: [], total: 0 });
  const [pagination, setPagination] = useState({ pageNo: 1, pageSize: 10 });
  const column = [
    {
      title: "序号",
      width: 60,
      align: "center",
      key: "index",
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: "分组名称",
      dataIndex: "groupingName",
      align: "center",
      key: "groupingName",
    },
    {
      title: "通道数量",
      dataIndex: "number",
      align: "center",
      key: "number",
    },
    {
      title: "备注",
      dataIndex: "remark",
      align: "center",
      key: "remark",
      ellipsis: {
        showTitle: false,
      },
      render: (remark) => (
        <Tooltip placement="topLeft" title={remark}>
          {remark}
        </Tooltip>
      ),
    },
  ];
  useImperativeHandle(ref, () => ({
    getSelectedKey() {
      return selectedRowKeys;
    },
  }));
  useEffect(() => {
    setRowKeys(defaultGroup);
    const params = {
      currPage: pagination.pageNo,
      pageSize: pagination.pageSize,
    };
    getAllGroup(params).then((res) => {
      if (res.code === 0) {
        setData({
          list: res.data.records,
          total: res.data.total * 1,
        });
      }
    });
  }, [pagination, defaultGroup]);
  const onSelectChange = (key) => {
    setRowKeys(key);
  };
  return (
    <div className="mt-6">
      <Table
        rowSelection={{
          type: "checkbox",
          selectedRowKeys,
          onChange: onSelectChange,
        }}
        pagination={false}
        rowKey={(c) => c.channelGroupingId}
        columns={column}
        dataSource={data.list}
      />
      <div className="text-right mt-5 mr-3">
        <Pagination
          className="inline-block"
          current={pagination.pageNo}
          pageSize={pagination.pageSize}
          onChange={(page, pageSize) =>
            setPagination({ pageNo: page, pageSize })
          }
          total={data.total}
        />
      </div>
    </div>
  );
});
export default forwardRef(TaskPlanSetting);
