import React, { useState, useEffect, useRef } from "react";
import {
  selectMemberByPage,
  updateAllTaskMemberRule,
  getTaskById,
  switchRule,
} from "@/api";
import {
  Table,
  Pagination,
  Switch,
  message,
  Input,
  Button,
  Space,
  Select,
  Modal,
  InputNumber,
} from "antd";
import styled from "styled-components";
import { $hidePhone, getTokenFromCookie } from "@/tools";
import axios from "axios";
import { useSearchParams } from "react-router-dom";

const Root = styled.div``;
const StatusLabel = styled.span`
  position: relative;
  &.open::before {
    background: #17be6b;
  }
  &.close::before {
    background: #d9d9d9;
  }
  &::before {
    content: "";
    position: absolute;
    top: 50%;
    left: -10px;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
`;

export default function TaskRule({ taskId }) {
  const [params] = useSearchParams();
  const bureauId = params.get("bureauId");
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [data, setData] = useState({ list: [], total: 0 });
  const [pagination, setPagination] = useState({ pageNo: 1, pageSize: 10 });

  const filterParasm = useRef({
    searchName: "",
    status: 0,
    sort: 0,
  });

  const [checked, setChecked] = useState(false);
  const handleSwitchChange = async (checked) => {
    const result = await switchRule({
      taskId,
    });
    if (result.code === 0) {
      if (result.data.usePatrolRule === 1) {
        setChecked(true);
      } else {
        setChecked(false);
      }
    }
  };
  async function handleExport() {
    let config = {
      method: "get",
      headers: {
        Authorization: getTokenFromCookie(),
        bureauId,
      },
      url: `/sd-api/patrol/patrol/task/downloadRule?taskId=${taskId}`,
      responseType: "blob",
    };
    if (process.env.NODE_ENV === "production") {
      config = window.ysCommonToolLib.encryption(config);
    }
    axios(config).then((res) => {
      let blob = new Blob([res.data], {
        type: "application/vnd.ms-excel",
      });
      let url = window.URL.createObjectURL(blob);
      let a = document.createElement("a");
      a.setAttribute("href", url);
      a.setAttribute("download", "mould.xls");
      a.click();
      window.URL.revokeObjectURL(url);
    });
  }

  function getList(pageNo, pageSize) {
    const { sort, status, searchName } = filterParasm.current;
    selectMemberByPage({
      sort,
      status,
      pageNo,
      pageSize,
      taskId,
      searchName,
    }).then((res) => {
      if (res.code === 0) {
        setData({
          list: res.data,
          total: res.totalDatas * 1,
        });
      }
    });
  }
  useEffect(() => {
    getList(1, 10);
    getTaskById({ taskId }).then((res) => {
      if (res.code === 0) {
        setChecked(res.data.usePatrolRule !== 2);
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [taskId]);
  const columns = [
    {
      title: "序号",
      align: "center",
      key: "index",
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: "姓名",
      align: "center",
      dataIndex: "userName",
      key: "userName",
      ellipsis: {
        showTitle: false,
      },
    },
    {
      title: "机构",
      align: "center",
      dataIndex: "orgName",
      key: "orgName",
      ellipsis: {
        showTitle: false,
      },
    },
    {
      title: "手机号",
      align: "center",
      width: 130,
      dataIndex: "phone",
      key: "phone",
      render: (_) => <span>{$hidePhone(_)}</span>,
    },
    {
      title: "通道数量",
      align: "center",
      dataIndex: "passagewayCount",
      key: "passagewayCount",
      sorter: (a, b) => a.passagewayCount - b.passagewayCount,
    },
    {
      title: "记录总数",
      align: "center",
      dataIndex: "recordCount",
      key: "recordCount",
      sorter: (a, b) => a.recordCount - b.recordCount,
    },
    {
      title: "记录要求",
      align: "center",
      dataIndex: "patrolRecordAsk",
      key: "patrolRecordAsk",
      sorter: (a, b) => a.patrolRecordAsk - b.patrolRecordAsk,
    },
    {
      title: "状态",
      align: "center",
      dataIndex: "status",
      key: "status",
      render: (_, record) => (
        <div>
          <StatusLabel className={_ === 1 ? "open" : "close"}>
            {_ === 1 ? "已完成" : "未完成"}
          </StatusLabel>
        </div>
      ),
    },
    {
      title: "操作",
      width: 140,
      align: "center",
      key: "control",
      render: (_, record) => (
        <div>
          <span
            onClick={() => {
              setModalInfo({
                show: true,
                ids: record.taskMemberId,
              });
            }}
            className="cursor-pointer text-blue-500"
          >
            记录要求
          </span>
        </div>
      ),
    },
  ];
  function handleShowModal() {
    if (selectedRowKeys.length === 0) {
      message.info("请先选择");
      return;
    }
    setModalInfo({
      show: true,
      ids: selectedRowKeys,
    });
  }
  const [modalInfo, setModalInfo] = useState({
    show: false,
    ids: "",
  });
  return (
    <Root>
      <div className="flex items-center">
        <span className="mr-3">巡课考核: </span>
        <Switch checked={checked} onChange={handleSwitchChange} />
      </div>

      <div className="flex justify-between items-center mt-5">
        <div className="font-bold">
          <Input.Search
            placeholder="请输入姓名查询"
            onChange={(e) => {
              filterParasm.current.searchName = e.target.value;
            }}
            onSearch={() => {
              setPagination((pre) => ({
                pageNo: 1,
                pageSize: pre.pageSize,
              }));
              getList(1, pagination.pageSize);
            }}
            style={{ width: 280 }}
          />
        </div>
        <Space>
          <Select
            style={{ width: 120 }}
            onChange={(status) => {
              filterParasm.current.status = status;
              setPagination((pre) => ({
                pageNo: 1,
                pageSize: pre.pageSize,
              }));
              getList(1, pagination.pageSize);
            }}
            defaultValue={0}
            options={[
              {
                value: 0,
                label: "全部",
              },
              {
                value: 1,
                label: "已完成",
              },
              {
                value: 2,
                label: "未完成",
              },
            ]}
          />
          <Button onClick={handleShowModal}>记录要求</Button>
          <Button onClick={handleExport} type="primary">
            <i className="icondaochu1 iconfont align-middle mr-1"></i>
            <span>导出</span>
          </Button>
        </Space>
      </div>
      <Table
        rowSelection={{
          selectedRowKeys,
          onChange: (newSelectedRowKeys) => {
            setSelectedRowKeys(newSelectedRowKeys);
          },
        }}
        className="mt-5"
        rowKey={(columns) => columns.taskMemberId}
        dataSource={data.list}
        columns={columns}
        pagination={false}
      />
      <div className="text-right mt-5">
        <Pagination
          className="inline-block"
          current={pagination.pageNo}
          pageSize={pagination.pageSize}
          onChange={(page, pageSize) => {
            setPagination((pre) => ({
              ...pre,
              pageNo: page,
              pageSize,
            }));
            getList(page, pageSize);
          }}
          total={data.total}
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `总共 ${total} 条`}
        />
      </div>
      <LogModal
        {...modalInfo}
        onCancel={() => {
          setModalInfo({
            show: false,
            ids: "",
          });
        }}
        onOK={() => {
          setModalInfo({
            show: false,
            ids: "",
          });
          getList(pagination.pageNo, pagination.pageSize);
        }}
      />
    </Root>
  );
}

function LogModal({ show, ids, onCancel, onOK }) {
  const [patrolRecordAsk, setPatrolRecordAsk] = useState(1);
  async function handleOk() {
    const result = await updateAllTaskMemberRule({
      patrolRecordAsk,
      taskMemberId: typeof ids === "string" ? [ids] : ids,
    });
    if (result.code === 0) {
      message.success("保存成功");
      setPatrolRecordAsk(1);
      onOK();
    }
  }
  return (
    <Modal
      width={448}
      title=""
      centered
      visible={show}
      onOk={handleOk}
      onCancel={onCancel}
    >
      <div
        style={{
          textAlign: "center",
          fontSize: 16,
          lineHeight: "24px",
        }}
      >
        {typeof ids === "string" ? "记录要求" : "批量设置记录要求"}
      </div>
      <InputNumber
        style={{ width: "100%", marginTop: 24, marginBottom: 12 }}
        min={1}
        max={999}
        value={patrolRecordAsk}
        onChange={setPatrolRecordAsk}
        placeholder="请输入"
      />
    </Modal>
  );
}
