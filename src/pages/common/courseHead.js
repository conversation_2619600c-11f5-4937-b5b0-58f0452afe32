import React, { useEffect, useState } from "react";
import { useRouterGo } from "@/hooks/useRouterGo";
import styled from "styled-components";
import { Dropdown, Menu } from "antd";
import avatar from "@/assets/images/avatar.svg";
import { $isCdn, $message, getUrlQuery } from "@/tools";
import { getSetting } from "@/api";

const Root = styled.div`
  height: 60px;
  background: #4c84ff;
  .icon-setup {
    margin-right: 4px;
    font-size: 20px;
    color: #fff;
    vertical-align: middle;
  }
  .back {
    display: inline-flex;
    align-items: center;
    color: #fff;
    cursor: pointer;
    margin-right: 16px;
    font-weight: bold;
    i {
      font-size: 20px;
    }
  }
`;

function CourseHead() {
  const [userInfo] = useState(() => {
    const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
    return userInfo;
  });
  const navigate = useRouterGo();
  const [sysInfo, setSysInfo] = useState({
    systemName: "",
    systemLogo: "",
  });
  useEffect(() => {
    $message.on("sysChange", (data) => {
      setSysInfo(data);
    });
  }, []);
  useEffect(() => {
    getSetting().then((res) => {
      if (res.code === 0) {
        setSysInfo({
          systemName: res.data.patrolSystemSetting.systemName || "在线巡课",
          systemLogo: res.data.patrolSystemSetting.systemLogo,
        });
      }
    });
  }, []);

  const handle2LoginOut = () => {
    window.location.href = "/zhxy/#/redirect/login";
  };
  const menuBar = (
    <Menu>
      <Menu.Item onClick={handle2LoginOut} key="loginOut">
        退出登录
      </Menu.Item>
    </Menu>
  );
  useEffect(() => {
    window.yskjConfig.createHeadOperation("commonHead", {
      isPureBackground: false,
    });
  }, []);

  return (
    <Root className="flex px-7 items-center justify-between">
      <div onClick={() => navigate("/")} className="cursor-pointer">
        {sysInfo.systemLogo ? (
          <img className="w-8 h-8" src={$isCdn(sysInfo.systemLogo)} alt="" />
        ) : (
          <img
            className="w-8 h-8"
            src="/yskt/ys/ysConfig/images/logo.png"
            alt=""
          />
        )}

        <div className="text-white font-bold text-xl inline-block align-middle ml-1">
          {sysInfo.systemName}
        </div>
      </div>
      <div id="commonHead" style={{ height: "100%" }}></div>
      {/* <div className="flex items-center">
        <a
          href={
            getUrlQuery("jd") === "1"
              ? "/zhxy/#/localSide/home"
              : "/zhxy/#/portal/home"
          }
          className="back"
        >
          <i className="iconfont iconshouyeshouye"></i>
          <span className="text-sm">返回门户</span>
        </a>
        {userInfo ? (
          <>
            <Dropdown overlay={menuBar} placement="bottomCenter">
              <div className="inline-flex items-center">
                <img
                  className="w-8 h-8 rounded-full mr-2"
                  src={userInfo.userFace ? $isCdn(userInfo.userFace) : avatar}
                  alt=""
                />
                <span className="text-white font-bold text-sm">
                  {userInfo.name}
                </span>
              </div>
            </Dropdown>
          </>
        ) : null}
      </div> */}
    </Root>
  );
}

export default CourseHead;
