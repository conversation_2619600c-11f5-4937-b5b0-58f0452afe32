import React, { useEffect, useState, useRef, useContext } from "react";
import styled from "styled-components";
import { $isCdn, $fileType } from "@/tools";
import { EyeOutlined, QuestionCircleFilled } from "@ant-design/icons";
import { Modal } from "antd";
import { PrevContext } from "./../home/<USER>/prevContext";
const { confirm } = Modal;
const Root = styled.div`
  display: inline-block;
  margin: 0 12px 12px 0;
  position: relative;
  width: 100px;
  height: 100px;
  background: #ffffff;
  border-radius: 2px;
  border: 1px solid #d9d9d9;
  padding: 8px;
  &:hover .cover {
    display: block;
  }
  img {
    object-fit: cover;
    width: 100%;
    height: 100%;
  }
  .cover {
    display: none;
    z-index: 1;
    background: rgba(0, 0, 0, 0.4);
  }
  .icons {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  .duraction {
    position: absolute;
    right: 10px;
    bottom: 10px;
    color: #fff;
    background: rgba(0, 0, 0, 0.4);
    border-radius: 9px;
    padding: 0 4px;
  }
`;
function PicBox({ src, realPath, hideDelete, onPreview, onDelete }) {
  const [prevState, prevDispatch] = useContext(PrevContext);

  const videoRef = useRef();
  const [time, setTime] = useState(0);
  const handleDelete = () => {
    if (prevState.oWebControl) {
      prevState.oWebControl.JS_SetHide();
    }
    if (prevState.oWebControl && prevState.oWebControl.JS_RequestInterface) {
      prevState.oWebControl.JS_RequestInterface({
        method: "window.hide",
      });
    }
    confirm({
      title: "确认要删除这个文件吗?",
      icon: <QuestionCircleFilled />,
      onOk() {
        onDelete();
        if (prevState.oWebControl) {
          if (prevState.oWebControl.JS_SetShow) {
            prevState.oWebControl.JS_SetShow();
          }
          if (prevState.oWebControl.JS_RequestInterface) {
            prevState.oWebControl.JS_RequestInterface({
              method: "window.show",
            });
          }
        }
      },
      onCancel() {
        if (prevState.oWebControl) {
          if (prevState.oWebControl.JS_SetShow) {
            prevState.oWebControl.JS_SetShow();
          }
          if (
            prevState.oWebControl &&
            prevState.oWebControl.JS_RequestInterface
          ) {
            prevState.oWebControl.JS_RequestInterface({
              method: "window.show",
            });
          }
        }
      },
    });
  };
  useEffect(() => {
    const getTime = (e) => {
      let time =
        e.target.duration < 10
          ? "0" + parseInt(e.target.duration)
          : parseInt(e.target.duration);
      setTime(time);
    };
    const video = videoRef.current;
    if (
      $fileType(realPath) === "mp4" ||
      $fileType(realPath).toLowerCase() === "mov"
    ) {
      video.src = $isCdn(realPath);
      video.addEventListener("canplay", getTime);
    }
    return () => {
      if ($fileType(realPath) === "mp4" && video) {
        video.removeEventListener("canplay", getTime);
      }
    };
  }, [realPath]);
  return (
    <Root>
      <div className="cover absolute left-0 right-0 top-0 bottom-0">
        <div className="icons w-full text-center">
          <EyeOutlined
            onClick={onPreview}
            className="cursor-pointer"
            style={{ fontSize: 22, color: "#fff" }}
          />
          {!hideDelete && (
            <i
              onClick={handleDelete}
              className="ml-2 cursor-pointer iconfont icon-del"
              style={{ fontSize: 22, color: "#fff" }}
            ></i>
          )}
        </div>
      </div>
      <img src={$isCdn(src)} alt="" />
      <video ref={videoRef} className="hidden" />
      {time !== 0 && <div className="duraction">{"00:" + time}</div>}
    </Root>
  );
}
export default PicBox;
