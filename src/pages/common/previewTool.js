import React, { useEffect, useState } from "react";
import { $isCdn, $fileType } from "@/tools";
import {
  LeftCircleFilled,
  RightCircleFilled,
  CloseOutlined,
} from "@ant-design/icons";
import styled from "styled-components";
import { message } from "antd";
const Root = styled.div`
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 998;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  .main {
    position: relative;
    flex: 1;
    text-align: center;
    line-height: 100vh;
    video {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      background: #000;
      display: inline-block;
      width: 800px;
      height: 450px;
    }
    video::-webkit-media-controls-fullscreen-button {
      display: none;
    }
    img {
      max-height: 100%;
      max-width: 100%;
    }
  }
  .bar {
    position: relative;
    width: 120px;
    .icon {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      cursor: pointer;
      font-size: 40px;
      color: rgba(0, 0, 0, 0.4);
    }
  }

  .closeBtn {
    cursor: pointer;
    border-radius: 2px;
    position: absolute;
    text-align: center;
    right: 16px;
    top: 6px;
    width: 36px;
    height: 36px;
    background: rgba(0, 0, 0, 0.4);
    .closeIcon {
      padding-top: 8px;
      font-size: 20px;
    }
  }
`;
function PreviewTool({ list, index, onClose }) {
  const [preview, setPreview] = useState({ list, index });
  useEffect(() => {
    document.body.style.overflow = "hidden";
    return () => {
      document.body.removeAttribute("style");
    };
  }, []);
  const leftPreview = () => {
    if (preview.index === 0) {
      return message.error("已经是第一张了");
    }
    setPreview({ ...preview, index: preview.index - 1 });
  };
  const rightPreview = () => {
    if (preview.index === preview.list.length - 1) {
      return message.error("已经是最后一张了");
    }
    setPreview({ ...preview, index: preview.index + 1 });
  };

  return (
    <Root>
      <div className="bar left-bar">
        <LeftCircleFilled onClick={leftPreview} className="icon" />
      </div>
      <div className="main">
        {$fileType(preview.list[preview.index]) === "mp4" ||
        $fileType(preview.list[preview.index]).toLowerCase() === "mov" ? (
          <video autoPlay controls src={$isCdn(preview.list[preview.index])} />
        ) : (
          <img src={$isCdn(preview.list[preview.index])} alt="" />
        )}
      </div>
      <div className="bar right-bar">
        <RightCircleFilled onClick={rightPreview} className="icon" />
      </div>
      <div onClick={onClose} className="closeBtn">
        <CloseOutlined className="closeIcon inline-block text-white" />
      </div>
    </Root>
  );
}

export default PreviewTool;
