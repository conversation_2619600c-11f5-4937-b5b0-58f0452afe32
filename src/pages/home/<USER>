import React, { useState, useEffect, useRef, useContext } from "react";
import { useRouterGo } from "@/hooks/useRouterGo";
import { useIs<PERSON>reeU<PERSON>, useIsClassteacher } from "@/hooks";
import styled from "styled-components";
import moment from "moment";
import {
  Table,
  Divider,
  Pagination,
  Tooltip,
  Button,
  DatePicker,
  Space,
  Dropdown,
  Menu,
  Input,
} from "antd";
import {
  getMyTasksList,
  selectRecordedMeByPage,
  getSetting,
  myTaskAndRecordNum,
  selectMyRecordedNewByPage,
} from "@/api";
import DetailDrawer from "./task/detailDrawer";
import { MenuContext } from "@/pages/layout";
import { PrevContextProvider } from "./../home/<USER>/prevContext";
// import { PlusOutlined, QuestionCircleFilled } from "@ant-design/icons";
function checkIsShowTourBtn({ passwayCount, startTime, endTime }) {
  if (passwayCount > 0) {
    const now = +new Date();
    if (startTime < now && now < endTime + 86400000) {
      return true;
    }
    return false;
  }
  return false;
}
const Root = styled.div`
  background: #f0f2f5;
  height: calc(100vh - 60px);
  overflow: auto;
  padding: 24px 0 32px 0;
  .main {
    position: relative;
    padding: 24px;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 4px;
    width: 80%;
  }
  .top-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 20px;
    .nav span {
      font-size: 14px;
      cursor: pointer;
      color: #262626;
      margin-right: 32px;
      padding-bottom: 24px;
      border-bottom: 3px solid transparent;
      &:hover {
        color: #007aff;
      }
    }
    .nav span.active {
      color: #007aff;
      border-bottom-color: #007aff;
    }
  }
  .admin {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    position: absolute;
    width: 56px;
    height: 56px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #e5e5e5;
    right: -72px;
    top: 216px;
    i {
      color: #007aff;
      font-size: 18px;
    }
    &:hover {
      background: #007aff;
      color: #fff;
      i {
        color: #fff;
      }
    }
  }
`;
function Tasks() {
  const navigate = useRouterGo();
  const [staticNum, setStaticNum] = useState({
    myTaskNum: 0,
    evaluateNum: 0,
    toMeTaskNum: 0,
  });
  const [active, setActive] = useState(1);
  const [showNav, setShowNav] = useState(false);
  const menu = useContext(MenuContext);
  const isFree = useIsFreeUser();
  useEffect(() => {
    getSetting().then((res) => {
      if (res.code === 0) {
        const data = res.data;
        // 如已开启巡课自动跳转
        if (data.patrolSystemSetting.freeAutoJump === 1 && isFree) {
          const hasJumped = sessionStorage.getItem('hasJumpedToFree');

          if (!hasJumped) {
            sessionStorage.setItem("hasJumpedToFree", 1);
            navigate("/prevFree");
          }
        }
        setShowNav(
          data.patrolSystemSetting.allowViewOwnRecords === 1 ? true : false
        );
      }
    });
    myTaskAndRecordNum().then((res) => {
      if (res.code === 0) {
        setStaticNum(res.data);
      }
    });
  }, [isFree]);
  const handle2Admin = () => {
    if (menu.includes(1)) {
      return navigate("/admin/tasks");
    }
    if (menu.includes(2)) {
      return navigate("/admin/log");
    }
    if (menu.includes(3)) {
      return navigate("/admin/setting");
    }
  };
  return (
    <Root>
      <div className="main">
        <div className="top-nav">
          <div className="nav">
            <span
              onClick={() => setActive(1)}
              className={active === 1 ? "active" : ""}
            >
              我的巡课任务 {staticNum.myTaskNum}
            </span>
            {showNav && (
              <span
                onClick={() => setActive(2)}
                className={active === 2 ? "active" : ""}
              >
                我的被评记录 {staticNum.evaluateNum}
              </span>
            )}
            {showNav && (
              <span
                onClick={() => setActive(3)}
                className={active === 3 ? "active" : ""}
              >
                我的评价记录 {staticNum.toMeTaskNum}
              </span>
            )}
          </div>
        </div>
        {menu.length > 1 && (
          <div onClick={() => handle2Admin()} className="admin">
            <i className="iconfont iconshezhi1"></i>
            <span>管理</span>
          </div>
        )}
        {active === 1 && <TaskList />}
        {active === 2 && <EvaluateList />}
        {active === 3 && <MyEvaluateList />}
      </div>
    </Root>
  );
}
const TaskListRoot = styled.div`
  .head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 24px 0 16px;
  }
`;
function TaskList() {
  const isFree = useIsFreeUser();
  const isClassTeacher = useIsClassteacher();
  const navigate = useRouterGo();
  const columns = [
    {
      title: "序号",
      width: 60,
      align: "center",
      key: "index",
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: "任务名称",
      dataIndex: "taskName",
      align: "center",
      key: "taskName",
      ellipsis: true,
    },
    {
      title: "开始时间",
      dataIndex: "startTime",
      align: "center",
      key: "startTime",
      render: (_) => moment(_).format("YYYY-MM-DD"),
    },
    {
      title: "结束时间",
      dataIndex: "endTime",
      align: "center",
      key: "endTime",
      render: (_) => moment(_).format("YYYY-MM-DD"),
    },
    {
      title: "巡课通道数量",
      dataIndex: "passwayCount",
      align: "center",
      key: "passwayCount",
    },
    {
      title: "巡课记录",
      width: 120,
      dataIndex: "patrolRecordCount",
      align: "center",
      key: "patrolRecordCount",
      render: (_, record) => (
        <div>
          <span>{_}</span>
          {record.usePatrolRule === 1 && (
            <Dropdown
              overlay={
                <Menu>
                  <Menu.Item key="setting">
                    <span
                      style={{
                        color:
                          record.patrolRecordCount < record.patrolRecordAsk
                            ? "#FFAA00"
                            : "#17BE6B",
                      }}
                    >
                      考核
                      {record.patrolRecordCount < record.patrolRecordAsk
                        ? "未"
                        : ""}
                      完成{record.patrolRecordCount}/{record.patrolRecordAsk}
                    </span>
                  </Menu.Item>
                </Menu>
              }
              placement="bottomLeft"
              arrow={{ pointAtCenter: true }}
            >
              <i
                className="iconfont iconbaogao"
                style={{
                  position: "relative",
                  top: 1,
                  marginLeft: 4,
                  color:
                    record.patrolRecordCount < record.patrolRecordAsk
                      ? "#FFAA00"
                      : "#17BE6B",
                }}
              ></i>
            </Dropdown>
          )}
        </div>
      ),
    },
    {
      title: "备注",
      dataIndex: "remarks",
      align: "center",
      key: "remarks",
      ellipsis: {
        showTitle: false,
      },
      render: (remarks) => (
        <Tooltip placement="topLeft" title={remarks}>
          {remarks}
        </Tooltip>
      ),
    },
    {
      title: "操作",
      width: 180,
      key: "control",
      align: "center",
      render: (_, record) => (
        <div>
          {checkIsShowTourBtn(record) && (
            <>
              <span
                onClick={() => {
                  navigate(`/prev/${record.taskId}`);
                }}
                className="cursor-pointer text-blue-500"
              >
                在线巡课
              </span>
              <Divider type="vertical" />
            </>
          )}
          <span
            onClick={() => {
              navigate(`/task/${record.taskId}`);
            }}
            className="cursor-pointer text-blue-500"
          >
            详情
          </span>
        </div>
      ),
    },
  ];
  const [data, setData] = useState({ list: [], total: 0 });
  const [pagination, setPagination] = useState({
    pageNo: 1,
    pageSize: 10,
  });
  async function getList(pageNo, pageSize) {
    const result = await getMyTasksList({ pageNo, pageSize });
    if (result.code === 0) {
      setData({
        list: result.data,
        total: result.totalDatas * 1,
      });
    }
  }
  useEffect(() => {
    getList(1, 10);
  }, []);
  return (
    <TaskListRoot>
      <div className="head h-8">
        <div className="text-sm">任务列表</div>
        <Space size={8}>
          {isClassTeacher && (
            <Button
              onClick={() => {
                navigate("/prevTeacher");
              }}
              type="primary"
            >
              <i className="iconjiaoshi11 iconfont align-middle mr-2"></i>
              <span>班主任</span>
            </Button>
          )}
          {isFree && (
            <Button
              onClick={() => {
                navigate("/prevFree");
              }}
              type="primary"
            >
              <i className="iconxunke1 iconfont align-middle mr-2"></i>
              <span>自由巡课</span>
            </Button>
          )}
        </Space>
      </div>

      <Table
        pagination={false}
        columns={columns}
        rowKey={(record) => record.taskId}
        dataSource={data.list}
      />
      <div className="text-right mt-5">
        <Pagination
          className="inline-block"
          current={pagination.pageNo}
          pageSize={pagination.pageSize}
          onChange={(page, pageSize) => {
            setPagination((pre) => ({
              ...pre,
              pageNo: page,
              pageSize,
            }));
            getList(page, pageSize);
          }}
          total={data.total}
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `总共 ${total} 条`}
        />
      </div>
    </TaskListRoot>
  );
}
const EvaluateListRoot = styled.div`
  .head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 24px 0 16px;
  }
`;
const MyEvaluateListRoot = styled.div`
  .head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 24px 0 16px;
  }
`;
function MyEvaluateList() {
  const { Search } = Input;
  const detailDrawerRef = useRef();
  const isFree = useIsFreeUser();
  const isClassTeacher = useIsClassteacher();
  const navigate = useRouterGo();
  const [myData, setMyData] = useState({ list: [], total: 0 });
  const [myPagination, setMyPagination] = useState({
    pageNo: 1,
    pageSize: 10,
    month: null,
    targetName: null,
  });
  const columns = [
    {
      title: "序号",
      width: 60,
      align: "center",
      key: "index",
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: "巡课时间",
      width: 160,
      dataIndex: "patrolTime",
      align: "center",
      key: "patrolTime",
      render: (_) => moment(_).format("YYYY-MM-DD HH:mm"),
      sorter: (a, b) => a.patrolTime - b.patrolTime,
    },
    {
      title: "被记录人",
      dataIndex: "recordedUserName",
      align: "center",
      key: "recordedUserName",
    },
    {
      title: "被记录班级",
      dataIndex: "className",
      align: "center",
      key: "className",
    },
    {
      title: "被记录年级",
      dataIndex: "gradeName",
      align: "center",
      key: "gradeName",
    },
    {
      title: "图片",
      dataIndex: "picCount",
      align: "center",
      key: "picCount",
    },
    {
      title: "视频",
      dataIndex: "videoCount",
      align: "center",
      key: "videoCount",
    },
    {
      title: "量表",
      dataIndex: "useTable",
      align: "center",
      key: "useTable",
      render: (_, record) => `${record.useTable === 2 ? "无" : "有"}`,
    },
    {
      title: "评语",
      dataIndex: "patrolComment",
      align: "center",
      key: "patrolComment",
      ellipsis: {
        showTitle: false,
      },
      render: (patrolComment) => (
        <Tooltip placement="topLeft" title={patrolComment}>
          {patrolComment}
        </Tooltip>
      ),
    },
    {
      title: "操作",
      width: 60,
      key: "control",
      align: "center",
      render: (_, record) => (
        <div>
          <span
            onClick={() => {
              detailDrawerRef.current.showDrawer(record.patrolRecordId, 2);
            }}
            className="cursor-pointer text-blue-500"
          >
            详情
          </span>
        </div>
      ),
    },
  ];
  async function getMyList(pageNo, pageSize, month, targetName) {
    const result = await selectMyRecordedNewByPage({
      pageNo,
      pageSize,
      month,
      targetName,
    });
    if (result.code === 0) {
      setMyData({
        list: result.data,
        total: result.totalDatas * 1,
      });
    }
  }
  const onSearch = (value) => {
    setMyPagination((pre) => ({
      ...pre,
      pageNo: 1,
      month: myPagination.month,
      targetName: value,
    }));
    getMyList(1, myPagination.pageSize, myPagination.month, value);
  };
  useEffect(() => {
    getMyList(1, 10, null, null);
  }, []);
  return (
    <MyEvaluateListRoot>
      <div className="head">
        <div className="text-sm">评价列表</div>
        <Space size={24}>
          <Search
            placeholder="记录对象、班级、年级"
            onSearch={onSearch}
            style={{ width: 200 }}
          />
          <DatePicker
            onChange={(_, dateString) => {
              setMyPagination((pre) => ({
                ...pre,
                pageNo: 1,
                month: dateString ? dateString : null,
                targetName: myPagination.targetName,
              }));
              getMyList(
                1,
                myPagination.pageSize,
                dateString ? dateString : null,
                myPagination.targetName
              );
            }}
            picker="month"
          />
          {isClassTeacher && (
            <Button
              onClick={() => {
                navigate("/prevTeacher");
              }}
              type="primary"
            >
              <i className="iconjiaoshi11 iconfont align-middle mr-2"></i>
              <span>班主任</span>
            </Button>
          )}
          {isFree && (
            <Button
              onClick={() => {
                navigate("/prevFree");
              }}
              type="primary"
            >
              <i className="iconxunke1 iconfont align-middle mr-2"></i>
              <span>自由巡课</span>
            </Button>
          )}
        </Space>
      </div>

      <Table
        pagination={false}
        columns={columns}
        rowKey={(record) => record.patrolRecordId}
        dataSource={myData.list}
      />
      <div className="text-right mt-5">
        <Pagination
          className="inline-block"
          current={myPagination.pageNo}
          pageSize={myPagination.pageSize}
          onChange={(page, pageSize) => {
            setMyPagination((pre) => ({
              ...pre,
              pageNo: page,
              pageSize,
            }));
            getMyList(
              page,
              pageSize,
              myPagination.month,
              myPagination.targetName
            );
          }}
          total={myData.total}
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `总共 ${total} 条`}
        />
      </div>
      <PrevContextProvider>
        <DetailDrawer ref={(c) => (detailDrawerRef.current = c)} />
      </PrevContextProvider>
    </MyEvaluateListRoot>
  );
}
function EvaluateList() {
  const detailDrawerRef = useRef();
  const isFree = useIsFreeUser();
  const isClassTeacher = useIsClassteacher();
  const navigate = useRouterGo();
  const [data, setData] = useState({ list: [], total: 0 });
  const [pagination, setPagination] = useState({
    pageNo: 1,
    pageSize: 10,
    month: "",
  });
  const columns = [
    {
      title: "序号",
      width: 60,
      align: "center",
      key: "index",
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: "巡课时间",
      width: 160,
      dataIndex: "patrolTime",
      align: "center",
      key: "patrolTime",
      render: (_) => moment(_).format("YYYY-MM-DD HH:mm"),
      sorter: (a, b) => a.patrolTime - b.patrolTime,
    },
    {
      title: "图片",
      dataIndex: "picCount",
      align: "center",
      key: "picCount",
    },
    {
      title: "视频",
      dataIndex: "videoCount",
      align: "center",
      key: "videoCount",
    },
    {
      title: "量表",
      dataIndex: "useTable",
      align: "center",
      key: "useTable",
      render: (_, record) => `${record.useTable === 2 ? "无" : "有"}`,
    },
    {
      title: "评语",
      dataIndex: "patrolComment",
      align: "center",
      key: "patrolComment",
      ellipsis: {
        showTitle: false,
      },
      render: (patrolComment) => (
        <Tooltip placement="topLeft" title={patrolComment}>
          {patrolComment}
        </Tooltip>
      ),
    },
    {
      title: "操作",
      width: 60,
      key: "control",
      align: "center",
      render: (_, record) => (
        <div>
          <span
            onClick={() => {
              detailDrawerRef.current.showDrawer(record.patrolRecordId, 1);
            }}
            className="cursor-pointer text-blue-500"
          >
            详情
          </span>
        </div>
      ),
    },
  ];
  async function getList(pageNo, pageSize, month) {
    const result = await selectRecordedMeByPage({ pageNo, pageSize, month });
    if (result.code === 0) {
      setData({
        list: result.data,
        total: result.totalDatas * 1,
      });
    }
  }
  useEffect(() => {
    getList(1, 10, "");
  }, []);
  return (
    <EvaluateListRoot>
      <div className="head">
        <div className="text-sm">评价列表</div>
        <Space size={8}>
          <DatePicker
            onChange={(_, dateString) => {
              setPagination((pre) => ({
                ...pre,
                pageNo: 1,
                month: dateString,
              }));
              getList(1, pagination.pageSize, dateString);
            }}
            picker="month"
          />
          {isClassTeacher && (
            <Button
              onClick={() => {
                navigate("/prevTeacher");
              }}
              type="primary"
            >
              <i className="iconjiaoshi11 iconfont align-middle mr-2"></i>
              <span>班主任</span>
            </Button>
          )}
          {isFree && (
            <Button
              onClick={() => {
                navigate("/prevFree");
              }}
              type="primary"
            >
              <i className="iconxunke1 iconfont align-middle mr-2"></i>
              <span>自由巡课</span>
            </Button>
          )}
        </Space>
      </div>

      <Table
        pagination={false}
        columns={columns}
        rowKey={(record) => record.patrolRecordId}
        dataSource={data.list}
      />
      <div className="text-right mt-5">
        <Pagination
          className="inline-block"
          current={pagination.pageNo}
          pageSize={pagination.pageSize}
          onChange={(page, pageSize) => {
            setPagination((pre) => ({
              ...pre,
              pageNo: page,
              pageSize,
            }));
            getList(page, pageSize, pagination.month);
          }}
          total={data.total}
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `总共 ${total} 条`}
        />
      </div>
      <PrevContextProvider>
        <DetailDrawer ref={(c) => (detailDrawerRef.current = c)} />
      </PrevContextProvider>
    </EvaluateListRoot>
  );
}
export default Tasks;
