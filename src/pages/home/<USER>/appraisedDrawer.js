import React, {
  useEffect,
  forwardRef,
  useImperative<PERSON>and<PERSON>,
  useState,
} from "react";
import moment from "moment";
import styled from "styled-components";
import { CloseOutlined } from "@ant-design/icons";
import { Drawer, Table, Space, Tooltip, Pagination } from "antd";
import { selectRecordByPage } from "@/api";
import { useParams, useSearchParams } from "react-router-dom";
const AppraisedDrawerDrawer = (props, ref) => {
  const { id } = useParams();
  const [columns, setColumns] = useState([
    {
      title: "序号",
      width: 60,
      align: "center",
      key: "index",
      render: (_, record, index) => `${index + 1}`,
      fixed: "left",
    },
    {
      title: "被记录人",
      dataIndex: "recorded_user_name",
      align: "center",
      key: "recorded_user_name",
      ellipsis: true,
      fixed: "left",
    },
    {
      title: "巡课时间",
      width: 160,
      dataIndex: "patrol_time",
      align: "center",
      key: "patrol_time",
      render: (_) => moment(_).format("YYYY-MM-DD HH:mm"),
    },
    {
      title: "记录提交时间",
      width: 160,
      dataIndex: "record_time",
      align: "center",
      key: "record_time",
      render: (_) => moment(_).format("YYYY-MM-DD HH:mm"),
    },
    {
      title: "记录人",
      dataIndex: "record_user_name",
      align: "center",
      key: "record_user_name",
      ellipsis: true,
    },
    {
      title: "图片",
      dataIndex: "pic_count",
      align: "center",
      key: "pic_count",
    },
    {
      title: "视频",
      dataIndex: "video_count",
      align: "center",
      key: "video_count",
    },
    {
      title: "量表",
      dataIndex: "use_table",
      align: "center",
      key: "use_table",
      render: (_, record) => `${record.use_table === 2 ? "无" : "有"}`,
    },
    {
      title: "评语",
      width: 300,
      dataIndex: "patrol_comment",
      align: "center",
      key: "patrol_comment",
      ellipsis: {
        showTitle: false,
      },
      render: (patrol_comment) => (
        <Tooltip placement="topLeft" title={patrol_comment}>
          {patrol_comment}
        </Tooltip>
      ),
    },
  ]);
  const [show, setShow] = useState(false);
  const [data, setData] = useState({
    list: [],
    total: 0,
  });
  const [pagination, setPagination] = useState({
    pageNo: 1,
    pageSize: 10,
  });
  const [rowId, setRowId] = useState(null); // 新增状态来保存 rowId
  const [dropdownTitle1, setDropdownTitle1] = useState(null); // 新增状态来保存 dropdownTitle1
  useImperativeHandle(ref, () => ({
    showDrawer(rowId, dropdownTitle1) {
      console.log("id", rowId);
      let newColumns = [...columns];
      if (dropdownTitle1 == 2) {
        newColumns[1] = {
          title: "被记录班级",
          dataIndex: "class_name",
          align: "center",
          key: "class_name",
          ellipsis: true,
          fixed: "left",
        };
      }
      if (dropdownTitle1 == 3) {
        newColumns[1] = {
          title: "被记录年级",
          dataIndex: "grade_name",
          align: "center",
          key: "grade_name",
          ellipsis: true,
          fixed: "left",
        };
      }
      setColumns(newColumns);
      // initDetail(rowId, dropdownTitle1);
      setShow(true);
      setRowId(rowId); // 保存 rowId
      setDropdownTitle1(dropdownTitle1); // 保存 dropdownTitle1
    },
  }));
  const initDetail = (rowId, dropdownTitle1) => {
    console.log("dropdownTitle1", dropdownTitle1);
    let params = {
      objectId: rowId,
      pageNo: pagination.pageNo,
      pageSize: pagination.pageSize,
      taskId: id,
      type: dropdownTitle1 == 1 ? 2 : dropdownTitle1 == 3 ? 4 : 3,
    };
    selectRecordByPage(params).then((res) => {
      setData({ list: res.data.records, total: res.data.total * 1 });
    });
  };

  const handleCloseDrader = () => {
    setShow(false);
  };
  const handlePaginationChange = (page, pageSize) => {
    console.log("page", page);
    console.log("pageSize", pageSize);
    setPagination({ pageNo: page, pageSize });
    // initDetail(rowId, dropdownTitle1); // 调用 initDetail 方法重新获取数据
  };
  useEffect(() => {
    if (rowId !== null && dropdownTitle1 !== null) {
      initDetail(rowId, dropdownTitle1);
    }
  }, [pagination.pageNo, pagination.pageSize, rowId, dropdownTitle1]);
  return (
    <Drawer
      destroyOnClose
      title="查看详情"
      width={1200}
      closable={false}
      visible={show}
      extra={
        <CloseOutlined onClick={handleCloseDrader} className="cursor-pointer" />
      }
    >
      <div style={{ marginBottom: "20px" }}>巡课记录</div>
      <Table
        className="personList"
        columns={columns}
        pagination={false}
        rowKey={(column) => column.patrolRecordId}
        dataSource={data.list}
        scroll={{ x: "max-content" }}
      />
      <div className="text-right mt-5">
        <Pagination
          className="inline-block"
          current={pagination.pageNo}
          pageSize={pagination.pageSize}
          onChange={handlePaginationChange}
          total={data.total}
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `总共 ${total} 条`}
        />
      </div>
    </Drawer>
  );
};
export default forwardRef(AppraisedDrawerDrawer);
