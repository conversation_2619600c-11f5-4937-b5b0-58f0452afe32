import React, { useState } from "react";
import ReactDOM from "react-dom";
import styled from "styled-components";
import { Slider } from "antd";
import { CloseOutlined } from "@ant-design/icons";

function ControlBarTip() {
  const [step, setStep] = useState(4);
  const [position, setPosition] = useState(() => {
    const initL = document.body.clientWidth - 320;
    const initT = document.body.clientHeight / 2 - 200;
    return { l: initL, t: initT };
  });
  const mouseDown = (e) => {
    if (
      e.target.className.indexOf &&
      e.target.className.indexOf("barTip") !== -1
    ) {
      const elWidth = e.target.clientWidth;
      const elHeight = e.target.clientHeight;
      document.onmousemove = (ev) => {
        let left = ev.clientX - e.nativeEvent.offsetX;
        let top = ev.clientY - e.nativeEvent.offsetY;
        left = Math.max(0, left);
        left = Math.min(left, document.body.clientWidth - elWidth);
        top = Math.max(0, top);
        top = Math.min(top, document.body.clientHeight - elHeight);
        setPosition({
          l: left,
          t: top,
        });
      };
      document.onmouseup = () => {
        document.onmousemove = null;
        document.onmouseup = null;
      };
    }
  };
  return (
    <Root
      style={{ left: position.l + "px", top: position.t + "px" }}
      onMouseDown={(e) => mouseDown(e)}
      className="container barTip cursor-move"
    >
      <div
        data-role="close"
        data-step={step}
        className="absolute right-4 top-4 cursor-pointer"
      >
        <CloseOutlined
          className="pointer-events-none"
          style={{ color: "#8C8C8C", fontSize: 18 }}
        />
      </div>

      <div className="top">
        <div data-role="1" data-step={step} className="lt item">
          {/* <span>▲</span> */}
        </div>
        <div data-role="4" data-step={step} className="rt item">
          <i className="iconfont icon-solid-arrow"></i>
        </div>
        <div data-role="3" data-step={step} className="lb item">
          <i className="iconfont icon-solid-arrow"></i>
        </div>
        <div data-role="2" data-step={step} className="rb item">
          <i className="iconfont icon-solid-arrow"></i>
        </div>
        <div data-role="zz" data-step={step} className="zz">
          <div className="mask">
            <i className="iconfont iconyuan"></i>
          </div>
        </div>
      </div>
      <div className="bottom">
        <div data-role="-" data-step={step}>
          <i className="iconfont iconsuoxiao1"></i>
        </div>
        <div data-role="+" data-step={step}>
          <i className="iconfont iconfangda"></i>
        </div>
      </div>
      <div className="slider">
        <div style={{ color: "#000" }}>移动速度 {step}</div>
        <Slider
          value={step}
          onChange={(value) => setStep(value)}
          max={8}
          min={1}
        />
      </div>
    </Root>
  );
}

export function ControlBarTipPortal(props) {
  return ReactDOM.createPortal(
    props.children,
    document.getElementById("modal-test")
  );
}

const Root = styled.div`
  z-index: 100;
  position: fixed;
  width: 230px;
  border-radius: 6px;
  padding: 32px 0 24px 0;
  background: #fff;
  box-shadow: 0px 2px 16px 1px rgba(0, 0, 0, 0.1);
  .top {
    margin: 0 auto;
    transform: rotate(45deg);
    width: 180px;
    height: 180px;
    border-radius: 50%;
    background: #e4e7ed;
    overflow: hidden;
    position: relative;
    clip-path: circle(50% at 50% 50%);
    .item {
      position: absolute;
      width: 90px;
      height: 90px;
      cursor: pointer;
      &:hover {
        background: rgba(76, 132, 255, 0.2);
        i {
          color: #4c84ff;
        }
      }
      i {
        color: #909399;
        position: absolute;
        left: 50%;
        top: 50%;
        font-size: 30px;
        pointer-events: none;
      }
    }
  }

  .lt {
    left: 0;
    top: 0;
    border-right: 2px solid #fff;
    border-bottom: 2px solid #fff;
    i {
      transform: translate(-50%, -50%) rotate(135deg);
    }
  }
  .rt {
    right: 0;
    top: 0;
    border-left: 2px solid #fff;
    border-bottom: 2px solid #fff;
    i {
      transform: translate(-50%, -50%) rotate(-135deg);
    }
  }
  .lb {
    left: 0;
    bottom: 0;
    border-right: 2px solid #fff;
    border-top: 2px solid #fff;
    i {
      transform: translate(-50%, -50%) rotate(45deg);
    }
  }
  .rb {
    right: 0;
    bottom: 0;
    border-left: 2px solid #fff;
    border-top: 2px solid #fff;
    i {
      transform: translate(-50%, -50%) rotate(-45deg);
    }
  }
  .zz {
    position: absolute;
    left: 50%;
    top: 50%;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    width: 76px;
    height: 76px;
    border: 4px solid #fff;
    background: #e4e7ed;
    z-index: 9;
    overflow: hidden;
    clip-path: circle(50% at 50% 50%);
    .mask {
      cursor: pointer;
      position: absolute;
      background: #e4e7ed;
      left: 0;
      top: 0;
      bottom: 0;
      right: 0;
      z-index: 10;
      &:hover {
        background: rgba(76, 132, 255, 0.2);
        i {
          color: #4c84ff;
        }
      }
    }
    i {
      position: absolute;
      left: 50%;
      top: 50%;
      font-size: 20px;
      z-index: 10;
      transform: translate(-50%, -50%);
      color: #a9acb1;
    }
  }

  .bottom {
    margin: 0 auto;
    width: 180px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20px;
    div {
      cursor: pointer;
      text-align: center;
      line-height: 36px;
      width: 88px;
      height: 36px;
      background: #e4e7ed;
      &:hover {
        background: rgba(76, 132, 255, 0.2);
        i {
          color: #4c84ff;
        }
      }
      i {
        pointer-events: none;
        font-size: 20px;
        color: #595959;
      }
      &:first-child {
        border-radius: 18px 0px 0px 18px;
      }
      &:last-child {
        border-radius: 0px 18px 18px 0px;
      }
    }
  }
  .slider {
    padding: 20px 25px 0;
    .ant-slider-track {
      background-color: #007aff;
    }
    .ant-slider-handle {
      border-color: #007aff;
    }
  }
`;
export default ControlBarTip;
// export default ControlBarTip
