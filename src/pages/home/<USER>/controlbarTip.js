import React, { useState } from "react";
import ReactDOM from "react-dom";
import styled from "styled-components";
import { Slider } from "antd";
import { CloseOutlined } from "@ant-design/icons";

function ControlBarTip() {
  const [step, setStep] = useState(4);
  const [position, setPosition] = useState(() => {
    const initL = document.body.clientWidth - 320;
    const initT = document.body.clientHeight / 2 - 200;
    return { l: initL, t: initT };
  });
  const mouseDown = (e) => {
    if (
      e.target.className.indexOf &&
      e.target.className.indexOf("barTip") !== -1
    ) {
      const elWidth = e.target.clientWidth;
      const elHeight = e.target.clientHeight;
      document.onmousemove = (ev) => {
        let left = ev.clientX - e.nativeEvent.offsetX;
        let top = ev.clientY - e.nativeEvent.offsetY;
        left = Math.max(0, left);
        left = Math.min(left, document.body.clientWidth - elWidth);
        top = Math.max(0, top);
        top = Math.min(top, document.body.clientHeight - elHeight);
        setPosition({
          l: left,
          t: top,
        });
      };
      document.onmouseup = () => {
        document.onmousemove = null;
        document.onmouseup = null;
      };
    }
  };
  return (
    <Root
      style={{ left: position.l + "px", top: position.t + "px" }}
      onMouseDown={(e) => mouseDown(e)}
      className="container barTip cursor-move"
    >
      <div
        data-role="close"
        data-step={step}
        className="absolute right-4 top-4 cursor-pointer"
      >
        <CloseOutlined
          className="pointer-events-none"
          style={{ color: "#8C8C8C", fontSize: 18 }}
        />
      </div>

      <div className="top">
        {/* 上方向 */}
        <div data-role="up" data-step={step} className="direction-up item">
          <div className="arrow arrow-up"></div>
        </div>
        {/* 右上方向 */}
        <div data-role="up-right" data-step={step} className="direction-up-right item">
          <div className="arrow arrow-up-right"></div>
        </div>
        {/* 右方向 */}
        <div data-role="right" data-step={step} className="direction-right item">
          <div className="arrow arrow-right"></div>
        </div>
        {/* 右下方向 */}
        <div data-role="down-right" data-step={step} className="direction-down-right item">
          <div className="arrow arrow-down-right"></div>
        </div>
        {/* 下方向 */}
        <div data-role="down" data-step={step} className="direction-down item">
          <div className="arrow arrow-down"></div>
        </div>
        {/* 左下方向 */}
        <div data-role="down-left" data-step={step} className="direction-down-left item">
          <div className="arrow arrow-down-left"></div>
        </div>
        {/* 左方向 */}
        <div data-role="left" data-step={step} className="direction-left item">
          <div className="arrow arrow-left"></div>
        </div>
        {/* 左上方向 */}
        <div data-role="up-left" data-step={step} className="direction-up-left item">
          <div className="arrow arrow-up-left"></div>
        </div>
        {/* 中心停止按钮 */}
        <div data-role="zz" data-step={step} className="zz">
          <div className="mask">
            <i className="iconfont iconyuan"></i>
          </div>
        </div>
      </div>
      <div className="bottom">
        <div data-role="-" data-step={step}>
          <i className="iconfont iconsuoxiao1"></i>
        </div>
        <div data-role="+" data-step={step}>
          <i className="iconfont iconfangda"></i>
        </div>
      </div>
      <div className="slider">
        <div style={{ color: "#000" }}>移动速度 {step}</div>
        <Slider
          value={step}
          onChange={(value) => setStep(value)}
          max={8}
          min={1}
        />
      </div>
    </Root>
  );
}

export function ControlBarTipPortal(props) {
  return ReactDOM.createPortal(
    props.children,
    document.getElementById("modal-test")
  );
}

const Root = styled.div`
  z-index: 100;
  position: fixed;
  width: 230px;
  border-radius: 6px;
  padding: 32px 0 24px 0;
  background: #fff;
  box-shadow: 0px 2px 16px 1px rgba(0, 0, 0, 0.1);
  .top {
    margin: 0 auto;
    width: 180px;
    height: 180px;
    border-radius: 50%;
    background: #e4e7ed;
    overflow: hidden;
    position: relative;
    clip-path: circle(50% at 50% 50%);
    .item {
      position: absolute;
      cursor: pointer;
      &:hover {
        background: #007aff;
        .arrow {
          border-color: #fff;
          &::before {
            border-color: #fff;
          }
          &::after {
            border-color: #fff;
          }
        }
      }
      .arrow {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        pointer-events: none;
      }
    }
  }

  /* 8个方向按钮的样式 - 每个占据45度扇形区域 */
  .direction-up {
    width: 90px;
    height: 45px;
    top: 0;
    left: 45px;
    border-bottom: 2px solid #fff;
  }

  .direction-up-right {
    width: 63.64px;
    height: 63.64px;
    top: 0;
    right: 0;
    border-left: 2px solid #fff;
    border-bottom: 2px solid #fff;
    transform: rotate(45deg);
    transform-origin: 0 100%;
  }

  .direction-right {
    width: 45px;
    height: 90px;
    top: 45px;
    right: 0;
    border-left: 2px solid #fff;
  }

  .direction-down-right {
    width: 63.64px;
    height: 63.64px;
    bottom: 0;
    right: 0;
    border-left: 2px solid #fff;
    border-top: 2px solid #fff;
    transform: rotate(-45deg);
    transform-origin: 0 0;
  }

  .direction-down {
    width: 90px;
    height: 45px;
    bottom: 0;
    left: 45px;
    border-top: 2px solid #fff;
  }

  .direction-down-left {
    width: 63.64px;
    height: 63.64px;
    bottom: 0;
    left: 0;
    border-right: 2px solid #fff;
    border-top: 2px solid #fff;
    transform: rotate(45deg);
    transform-origin: 100% 0;
  }

  .direction-left {
    width: 45px;
    height: 90px;
    top: 45px;
    left: 0;
    border-right: 2px solid #fff;
  }

  .direction-up-left {
    width: 63.64px;
    height: 63.64px;
    top: 0;
    left: 0;
    border-right: 2px solid #fff;
    border-bottom: 2px solid #fff;
    transform: rotate(-45deg);
    transform-origin: 100% 100%;
  }
  /* 箭头样式 */
  .arrow {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0;
    border-color: #909399;
  }

  .arrow-up {
    border-width: 0 8px 12px 8px;
    border-color: transparent transparent #909399 transparent;
  }

  .arrow-up-right {
    width: 12px;
    height: 12px;
    border-width: 0;
    position: relative;
    &::before {
      content: '';
      position: absolute;
      width: 12px;
      height: 2px;
      background: #909399;
      transform: rotate(-45deg);
      top: 5px;
      left: 0;
    }
    &::after {
      content: '';
      position: absolute;
      border-style: solid;
      border-width: 6px 6px 0 0;
      border-color: #909399 transparent transparent transparent;
      transform: rotate(0deg);
      top: -2px;
      right: -2px;
    }
  }

  .arrow-right {
    border-width: 8px 0 8px 12px;
    border-color: transparent transparent transparent #909399;
  }

  .arrow-down-right {
    width: 12px;
    height: 12px;
    border-width: 0;
    position: relative;
    &::before {
      content: '';
      position: absolute;
      width: 12px;
      height: 2px;
      background: #909399;
      transform: rotate(45deg);
      top: 5px;
      left: 0;
    }
    &::after {
      content: '';
      position: absolute;
      border-style: solid;
      border-width: 0 6px 6px 0;
      border-color: transparent #909399 transparent transparent;
      transform: rotate(0deg);
      bottom: -2px;
      right: -2px;
    }
  }

  .arrow-down {
    border-width: 12px 8px 0 8px;
    border-color: #909399 transparent transparent transparent;
  }

  .arrow-down-left {
    width: 12px;
    height: 12px;
    border-width: 0;
    position: relative;
    &::before {
      content: '';
      position: absolute;
      width: 12px;
      height: 2px;
      background: #909399;
      transform: rotate(-45deg);
      top: 5px;
      left: 0;
    }
    &::after {
      content: '';
      position: absolute;
      border-style: solid;
      border-width: 0 0 6px 6px;
      border-color: transparent transparent #909399 transparent;
      transform: rotate(0deg);
      bottom: -2px;
      left: -2px;
    }
  }

  .arrow-left {
    border-width: 8px 12px 8px 0;
    border-color: transparent #909399 transparent transparent;
  }

  .arrow-up-left {
    width: 12px;
    height: 12px;
    border-width: 0;
    position: relative;
    &::before {
      content: '';
      position: absolute;
      width: 12px;
      height: 2px;
      background: #909399;
      transform: rotate(45deg);
      top: 5px;
      left: 0;
    }
    &::after {
      content: '';
      position: absolute;
      border-style: solid;
      border-width: 6px 0 0 6px;
      border-color: #909399 transparent transparent transparent;
      transform: rotate(0deg);
      top: -2px;
      left: -2px;
    }
  }

  .zz {
    position: absolute;
    left: 50%;
    top: 50%;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    width: 76px;
    height: 76px;
    border: 4px solid #fff;
    background: #e4e7ed;
    z-index: 9;
    overflow: hidden;
    clip-path: circle(50% at 50% 50%);
    .mask {
      cursor: pointer;
      position: absolute;
      background: #e4e7ed;
      left: 0;
      top: 0;
      bottom: 0;
      right: 0;
      z-index: 10;
      &:hover {
        background: rgba(76, 132, 255, 0.2);
        i {
          color: #4c84ff;
        }
      }
    }
    i {
      position: absolute;
      left: 50%;
      top: 50%;
      font-size: 20px;
      z-index: 10;
      transform: translate(-50%, -50%);
      color: #a9acb1;
    }
  }

  .bottom {
    margin: 0 auto;
    width: 180px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20px;
    div {
      cursor: pointer;
      text-align: center;
      line-height: 36px;
      width: 88px;
      height: 36px;
      background: #e4e7ed;
      &:hover {
        background: rgba(76, 132, 255, 0.2);
        i {
          color: #4c84ff;
        }
      }
      i {
        pointer-events: none;
        font-size: 20px;
        color: #595959;
      }
      &:first-child {
        border-radius: 18px 0px 0px 18px;
      }
      &:last-child {
        border-radius: 0px 18px 18px 0px;
      }
    }
  }
  .slider {
    padding: 20px 25px 0;
    .ant-slider-track {
      background-color: #007aff;
    }
    .ant-slider-handle {
      border-color: #007aff;
    }
  }
`;
export default ControlBarTip;
// export default ControlBarTip
