import React, { forwardRef, useImperativeHandle, useState } from "react";
import { Drawer, Timeline } from "antd";
import moment from "moment";
import { CloseOutlined } from "@ant-design/icons";
import { selectPatrolRecordDetail, getPatrolRecordEditDetail } from "@/api";
import styled from "styled-components";
import PicBox from "@/pages/common/picBox";
import { FormRes } from "@/components";
import PreviewTool from "../../common/previewTool";
const Tab = styled.div`
  margin-bottom: 20px;
`;
const TabItem = styled.span`
  margin-right: 32px;
  cursor: pointer;
  padding-bottom: 6px;
  border-bottom: 2px solid transparent;
  &.active {
    color: #007aff;
    border-block-color: #007aff;
  }
  &:hover {
    color: #007aff;
  }
`;
function logLable(type) {
  switch (type) {
    case 1:
      return "创建";
    case 2:
      return "编辑";
    case 3:
      return "编辑量表";
    default:
      return "编辑";
  }
}
const DetailDrawer = (props, ref) => {
  const [active, setActive] = useState(1); //1基础信息 2编辑记录
  const [show, setShow] = useState(false);
  const [status, setStatus] = useState(1);
  const [info, setInfo] = useState(null);
  const [list, setList] = useState([]);
  useImperativeHandle(ref, () => ({
    showDrawer(patrolRecordId, status) {
      initDetail(patrolRecordId);
      initEditDetail(patrolRecordId);
      setActive(1);
      setShow(true);
      setStatus(status);
    },
  }));
  const initDetail = (patrolRecordId) => {
    selectPatrolRecordDetail({ patrolRecordId }).then((res) => {
      setInfo(res.data);
    });
  };

  const initEditDetail = (patrolRecordId) => {
    getPatrolRecordEditDetail({ patrolRecordId }).then((res) => {
      setList(res.data);
    });
  };
  const handleCloseDrader = () => {
    setShow(false);
  };
  const handleChangeTab = (tab) => {
    setActive(tab);
  };
  return (
    <Drawer
      destroyOnClose
      title="详情"
      width={480}
      closable={false}
      visible={show}
      extra={
        <CloseOutlined onClick={handleCloseDrader} className="cursor-pointer" />
      }
    >
      <Tab>
        <TabItem
          onClick={() => handleChangeTab(1)}
          className={active === 1 ? "active" : ""}
        >
          基础信息
        </TabItem>
        {info && info.useTable === 1 && (
          <TabItem
            onClick={() => handleChangeTab(3)}
            className={active === 3 ? "active" : ""}
          >
            量表
          </TabItem>
        )}
        <TabItem
          onClick={() => handleChangeTab(2)}
          className={active === 2 ? "active" : ""}
        >
          编辑记录
        </TabItem>
      </Tab>
      {active === 1 && <BasicInfo info={info} status={status} />}
      {active === 2 && (
        <Timeline>
          {list.map((item, index) => (
            <Timeline.Item key={index}>
              <span>
                {moment(item.operationTime).format("YYYY-MM-DD HH:mm")}
              </span>
              <span className="ml-4">{item.operatorUserName}</span>
              <span className="ml-4">{logLable(item.operationType)}</span>
            </Timeline.Item>
          ))}
        </Timeline>
      )}
      {active === 3 && (
        <FormRes
          formTemplateAnswerId={info.formTemplateAnswerId}
          formInfoId={info.formInfoId}
          formTemplateInfoId={info.formTemplateInfoId}
        />
      )}
    </Drawer>
  );
};

const BasicRoot = styled.div`
  .item {
    margin-bottom: 28px;
    display: flex;
    .content {
      margin-left: 10px;
      flex: 1;
    }
  }
`;
function BasicInfo({ info, status }) {
  const [prevInfo, setPreview] = useState({
    show: false,
    list: [],
    index: -1,
  });

  if (!info) return null;

  const handle2Preview = (index) => {
    const list = info.patrolRecordAttrList.map((item) => item.attrPath);
    setPreview({
      list,
      show: true,
      index,
    });
  };
  const handle2ClosePreview = () => {
    setPreview({
      show: false,
      list: [],
      index: -1,
    });
  };
  return (
    <BasicRoot>
      {/* <div className="item">
        <div className="label">人员:</div>
        <div className="content">{info.recordedUserName}</div>
      </div> */}
      {status == 2 && (
        <div className="item">
          <div className="label">被记录人:</div>
          <div className="content break-all">{info.recordUserName}</div>
        </div>
      )}
      {status == 2 && (
        <div className="item">
          <div className="label">被记录年级:</div>
          <div className="content break-all">{info.gradeName}</div>
        </div>
      )}
      {status == 2 && (
        <div className="item">
          <div className="label">被记录班级:</div>
          <div className="content break-all">{info.className}</div>
        </div>
      )}

      <div className="item">
        <div className="label">时间:</div>
        <div className="content">
          {moment(info.patrolTime).format("YYYY-MM-DD HH:mm")}
        </div>
      </div>
      <div className="item">
        <div className="label">评价:</div>
        <div className="content break-all">{info.patrolComment}</div>
      </div>
      <div className="item">
        <div className="label">截图:</div>
        <div className="content">
          {info.patrolRecordAttrList.map((item, index) => (
            <PicBox
              key={index}
              onPreview={() => handle2Preview(index)}
              src={item.attrPic ? item.attrPic : item.attrPath}
              realPath={item.attrPath}
              hideDelete
            />
          ))}
        </div>
      </div>
      {prevInfo.show && (
        <PreviewTool onClose={handle2ClosePreview} {...prevInfo} />
      )}
    </BasicRoot>
  );
}

export default forwardRef(DetailDrawer);
