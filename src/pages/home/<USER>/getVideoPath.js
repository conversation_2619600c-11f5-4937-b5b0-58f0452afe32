import {
  rulesLive
} from "@/api";

export async function getVideoPath(list, onChoose) {
  console.log('list', list);
  if (!Array.isArray(list) || list.length === 0) {
    onChoose([]);
    return;
  }

  const videoPromises = list.map((item) =>
    getVideoPathData(item)
      .then((res) => {
        if (res?.code === 0 && res.data?.[0]) {
          const { webrtc, flvUrl, pcUrl } = res.data[0];
          const url = flvUrl || pcUrl || webrtc;
          if (!url) return null;

          const obj = {
            url,
            belongs: belongs(item),
            urlList: [],
            monitoringNodeId: item.objectId,
            classRoomId: item.objectId,
            type: item.objectType,
            nodeId: item.objectId,
            isPatrol: 1
          };

          if (item.objectType === 1) {
            obj.recordUrl = webrtc ? flvUrl : url;
          }

          return obj;
        }
        return null;
      })
      .catch((err) => {
        console.error("获取视频路径失败:", err);
        return null;
      })
  );

  const videoDataList = await Promise.all(videoPromises);

  onChoose(videoDataList);
}

function getVideoPathData(item) {
  if(!item) return Promise.resolve(null);
  return rulesLive({
    id: item.objectId,
    type: item.objectType, // 1.监控, 2.录播, 3.魔盒, 4.第三方录播
  });
}

function belongs(item) {
  switch (item.objectType) {
    case 1:
      return 20000; // 监控
    case 2:
      return 10000; // 录播
    case 3:
      return 40000; // 魔盒
    case 4:
      return 20000; // 第三方录播
    default:
      return 20000;
  }
}
