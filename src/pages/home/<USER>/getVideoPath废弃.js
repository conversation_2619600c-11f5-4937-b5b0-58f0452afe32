import {
  getBaseCourseUser,
  getPatrolLiveByFree,
  getMagicLive,
  getTimetable,
} from "@/api";
import { message } from "antd";
import dayjs from "dayjs";

function getSelectType(selectType){
  //selectType: 1. 按班级选择 2. 按教室选择 3. 按监控选择
  return selectType === 1 ? 4 : selectType === 2 ? 3 : 2 
}
const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));

export async function getVideoPath(list, node, selectType, prevState, prevDispatch, videoList, onChoose) {
  console.log('list', list)
  // 获取教室对应的老师\班级\年级信息
  const info = await getBaseCourseUser({
    time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
    nodeId: node.objectId,
    type: getSelectType(selectType), // 节点类型 1.录播 2.监控 3.教室 4.班级
  });
  if(info?.code === 0) {
    prevDispatch({
      type: "change_state",
      payload: {
        classRoomInfo: info.data[0],
      },
    });
  }
  // selectType === 3 ：按监控分组
  if(selectType === 3) {
    const result = await getPatrolLiveByFree({
      belong: 20000,
      nodeId: node.objectId,
      bureauId: userInfo.orgId,
    });
    let monitorBindClassInfo = null;
    const monitorBindClassInfoRes = await getTimetable({
      nodeId: node.objectId,
    });
    if (monitorBindClassInfoRes.code === 0) {
      monitorBindClassInfo = monitorBindClassInfoRes.data;
    }

    if (result && result.code === 0 && result.data[0]) {
      const { webrtc, flvUrl, pcUrl } = result.data[0];
      let url = webrtc ? webrtc : flvUrl ? flvUrl : pcUrl;
      if (!url) {
        message.error("暂无播放地址");
        return;
      }
      const recordUrl = webrtc ? flvUrl : url;
      onChoose({
        url,
        recordUrl,
        monitoringNodeId: node.objectId,
        belongs: 20000,
        urlList: [],
        monitorBindClassInfo,
        classRoomId: node.objectId,
        type: 2,
      });
    }
  } else {
    // objectType: 1.监控, 2.录播, 3.魔盒 4.第三方录播-长度:-1
    // belong: 20000-监控, 10000-录播
    /* 普通模式 */
    if (prevState.patrolFreeSetting && prevState.patrolFreeSetting.freeMode === 101) {
      console.log('prevState.patrolFreeSetting', prevState.patrolFreeSetting)
      // debugger
      if (list && list.length > 0) {
        let deviceList = list.filter((item) => item.objectType !== 3) || [];
        const magicDeviceList = list.filter((item) => item.objectType === 3) || [];
        if (deviceList.length > 0) {
          const { objectType, objectId } = deviceList[0];
          const patrolResult = await getPatrolLiveByFree({
            belong: [1, 4].includes(objectType) ? 20000 : 10000,
            nodeId: objectId,
            bureauId: userInfo?.orgId,
          });
          if (patrolResult && patrolResult.code === 0 && patrolResult.data[0]) {
            onChoose({
              url: patrolResult.data[0].pcUrl,
              belongs: [1, 4].includes(objectType) ? 20000 : 10000,
              urlList: [],
              monitoringNodeId: objectId,
              classRoomId: node.objectId,
              type: 3,
              nodeId: patrolResult.data[0].nodeId,
            });
          }
          return;
        }
        if (magicDeviceList.length > 0) {
          const magicResult = await getMagicLive({ roomId: node.objectId });
          if (magicResult && magicResult.code === 0 && magicResult.data) {
            let urlList = magicResult.data.liveUrls || [];
            if (magicResult.data.success === 500) {
              urlList = urlList.filter((item) => item.status === 1);
            }
            if (urlList.length === 0) {
              message.error("暂无播放地址");
              return;
            }
            const data = {
              url: urlList[0].flvUrl,
              urlList: urlList.map((item) => item.flvUrl),
              monitoringNodeId: node.objectId,
              belongs: 40000,
              classRoomId: node.objectId,
              type: 3,
              nodeId: urlList[0].nodeId,
            };
            onChoose(data);
          }
          return;
        }
        message.info("暂无录播设备或魔盒");
      }
      return;
    }

    if (list && list.length > 0) {
      const request = [];
      /* 监控和录播设置 */
      const deviceList = list.filter((item) => item.objectType !== 3) || [];
      const magicDeviceList = list.filter((item) => item.objectType === 3) || [];
  
      // console.log('deviceList', deviceList)
      // console.log('magicDeviceList', magicDeviceList)
      // return
      [...deviceList, ...magicDeviceList].forEach((item) => {
        request.push(getPromiseByBelong(item, node.id));
      });
      let data = await Promise.all(request);
      data = data.filter(Boolean);
      if(data.length === 0) {
        message.error("暂无播放地址");
        return;
      }
      const moheUrls = [];
      data.forEach((item) => {
        if (item.belongs === "10006") {
          if (item.urlList.length > 1) {
            item.urlList.forEach((urlItem, index) => {
              moheUrls.push({
                belongs: 40000,
                monitoringNodeId: item.objectId + index,
                url: urlItem.flvUrl,
                urlList: [],
                classRoomId: node.objectId,
                type: 3,
                nodeId: urlItem.nodeId,
              });
            });
          }
        } else {
          moheUrls.push({ ...item, classRoomId: node.objectId, type: 3 });
        }
      });
      data = [...moheUrls];
      // 处理窗口数量相关问题
      const currentWindNum = videoList.length;
      const num = data.length - currentWindNum;
      if (num > 0) {
        const isUseWindList = [];
        const {
          oneFrame,
          twoFrame,
          threeFrame,
          fourFrame,
          sixFrame,
          nineFrame,
          sixteenFrame,
        } = prevState.patrolSystemSetting;
        if (oneFrame === 1) isUseWindList.push(1);
        if (fourFrame === 1) isUseWindList.push(4);
        if (twoFrame === 1) isUseWindList.push(2);
        if (threeFrame === 1) isUseWindList.push(3);
        if (sixFrame === 1) isUseWindList.push(6);
        if (nineFrame === 1) isUseWindList.push(9);
        if (sixteenFrame === 1) isUseWindList.push(16);
        
        const nearWind = isUseWindList.find((wind) => wind - data.length >= 0);
        const list = [...data];
        new Array(Number(nearWind - list.length)).fill("").forEach((_) => {
          list.push(null);
        });
        onChoose(list);
      } else {
        const list = [...data];
        new Array(Number(currentWindNum - list.length)).fill("").forEach((_) => {
          list.push(null);
        });
        onChoose(list);
      }
    }
  }
}


function getPromiseByBelong(item, roomId) {
  if (item.objectType !== 3 ) {
    return new Promise((resolve, reject) => {
      getPatrolLiveByFree({
        belong: [1, 4].includes(item.objectType) ? 20000 : 10000,
        nodeId: item.objectId,
        bureauId: userInfo?.orgId,
      }).then((result) => {
        if (result && result.code === 0 && result.data[0]) {
          resolve({
            url: result.data[0].pcUrl,
            belongs: [1, 4].includes(item.objectType) ? 20000 : 10000,
            urlList: [],
            monitoringNodeId: item.objectId,
          });
        } else {
          resolve();
        }
      });
    });
  }
  if (item.objectType === 3 ) {
    return new Promise((resolve, reject) => {
      getMagicLive({ roomId }).then((result) => {
        if (result && result.code === 0 && result.data) {
          let urlList = result.data.liveUrls || [];
          if (result.data.success === 500) {
            urlList = urlList.filter((item) => item.status === 1);
          }
          // urlList = urlList.map((item) => item.flvUrl);
          if (urlList.length === 0) {
            message.error("暂无播放地址");
            return;
          }
          const data = {
            url: urlList[0].flvUrl,
            urlList,
            monitoringNodeId: item.objectId,
            belongs: "10006",
          };
          resolve(data);
        } else {
          resolve();
        }
      });
    });
  }
}