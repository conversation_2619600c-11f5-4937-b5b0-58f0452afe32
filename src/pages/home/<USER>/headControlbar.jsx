import styled from "styled-components";
import { useState, useEffect, useMemo, useContext } from "react";
import {
  Space,
  Divider,
  Popover,
  message,
  Tooltip,
  Drawer,
  Select,
  Switch,
} from "antd";
import { getSetting, getVendorrList, getSilent } from "@/api";
import { useRouterGo } from "@/hooks/useRouterGo";
import { PrevContext } from "./prevContext";
import { CloseOutlined } from "@ant-design/icons";

const Root = styled.div`
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .left {
    i,
    span {
      vertical-align: middle;
    }
    i {
      color: #8c8c8c;
      margin-right: 8px;
    }
    span {
      font-size: 14px;
    }
  }
  .right {
    i {
      cursor: pointer;
      font-size: 28px;
      color: #d9d9d9;
    }
    i:hover {
      color: #3395ff;
    }
    i.active {
      color: #3395ff;
    }
  }
`;
const ContentBox = styled.div`
  i {
    cursor: pointer;
    font-size: 28px;
    color: #d9d9d9;
  }
  i:hover {
    color: #3395ff;
  }
  i.active {
    color: #3395ff;
  }
`;

export default function HeadControlBar(props) {
  const [prevState, prevDispatch] = useContext(PrevContext);
  const {
    windNum,
    setShowBar,
    setShowPatrol,
    handleClosePatrol,
    onChangeWind,
    enterFullscreen,
    taskInfo,
    isFree,
    setShowSpeak,
    activeIndex,
    videoList,
    activeRoomId
  } = props;

  const navigate = useRouterGo();
  // const [patrolFrameNum, setPatrolFrameNum] = useState([]);
  const handleShowControlBar = () => {
    if (taskInfo) {
      if (taskInfo.allowControl === 1) {
        setShowBar(true);
      } else {
        message.error("不允许控制云平台摄像头");
      }
    } else {
      setShowBar(true);
    }
  };
  const handleShowPatrol = () => {
    setShowPatrol(true);
  }

  useEffect(() => {
    getSetting().then((res) => {
      if (res.code === 0) {
        const { patrolFreeSetting, patrolSystemSetting } = res.data;
        const mode = patrolSystemSetting.prevType === 2 ? "rtsp" : "live";
        const vendor =
          patrolSystemSetting.prevType === 2
            ? patrolSystemSetting.vendor
            : null;

        // 初始化默认画面数量
        let frameMap = {
          adaptFrameDefault: 0, //自适应
          oneFrameDefault: 1,
          twoFrameDefault: 2,
          threeFrameDefault: 3,
          fourFrameDefault: 4,
          sixFrameDefault: 6,
          nineFrameDefault: 9,
          sixteenFrameDefault: 16,
        };
        for (const k in patrolSystemSetting) {
          if (
            k.indexOf("FrameDefault") !== -1 &&
            patrolSystemSetting[k] === 1
          ) {
            handleChangeWind(frameMap[k]);
            break;
          }
        }

        if (vendor == 1002 && patrolSystemSetting.vendorId) {
          getVendorrList({
            currPage: 1,
            pageSize: 999,
          }).then((result) => {
            if (result.code === 0) {
              const target = result.data.records.find(
                (item) =>
                  item.monitoringCenterId === patrolSystemSetting.vendorId
              );
              if (!target) return;
              const ipRegex =
                /((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)/g;
              const ipMatches = target.path.match(ipRegex);
              window.yskjConfig = {
                ...window.yskjConfig,
                ip: ipMatches[0],
                port: 9060,
                password: target.password,
                username: target.userName,
              };
              prevDispatch({
                type: "change_mode",
                payload: {
                  patrolFreeSetting,
                  patrolSystemSetting,
                  mode,
                  vendor,
                  isClassRoom: patrolFreeSetting.roomSwitch === 1 && isFree,
                },
              });
            }
          });
        } else {
          prevDispatch({
            type: "change_mode",
            payload: {
              patrolFreeSetting,
              patrolSystemSetting,
              mode,
              vendor,
              isClassRoom: patrolFreeSetting.roomSwitch === 1 && isFree,
            },
          });
        }
      }
    });
    getSilent().then((res) => {
      if (res.code === 0) {
        const videoContentWrap = document.getElementById("videoContentWrap");
        if (videoContentWrap) {
          videoContentWrap.dataset.iactive = res.data;
        }
      }
    });
  }, []);

  const activeClass = useMemo(() => {
    if(prevState.isAdaptiveMode) return "iconzishiying";

    if (windNum === 0) return "iconzishiying";
    if (windNum === 1) return "iconyifen";
    if (windNum === 4) return "iconsifen";
    if (windNum === 2) return "iconerfenping";
    if (windNum === 3) return "iconsanfenping";
    if (windNum === 6) return "iconliufen";
    if (windNum === 9) return "iconjiufen";
    if (windNum === 16) return "iconshiliufen";
  }, [windNum, prevState.isAdaptiveMode]);

   const handleChangeWind = (num) =>{
      prevDispatch({
        type: "change_state", 
        payload: { isAdaptiveMode: num === 0 }
      });
      onChangeWind(num)
   }
  const patrolContent = (
    <ContentBox>
      <Space>
        {prevState.patrolSystemSetting?.oneFrame === 1 && (
          <i
            onClick={() => handleChangeWind(1)}
            className={`iconfont iconyifen ${windNum === 1 && !prevState.isAdaptiveMode ? "active" : ""}`}
          ></i>
        )}

        {prevState.patrolSystemSetting?.twoFrame === 1 && (
          <i
            onClick={() => handleChangeWind(2)}
            className={`iconfont iconerfenping ${windNum === 2 && !prevState.isAdaptiveMode ? "active" : ""
              }`}
          ></i>
        )}
        {prevState.patrolSystemSetting?.threeFrame === 1 && (
          <i
            onClick={() => handleChangeWind(3)}
            className={`iconfont iconsanfenping ${windNum === 3 && !prevState.isAdaptiveMode ? "active" : ""
              }`}
          ></i>
        )}

        {prevState.patrolSystemSetting?.fourFrame === 1 && (
          <i
            onClick={() => handleChangeWind(4)}
            className={`iconfont iconsifen ${windNum === 4 && !prevState.isAdaptiveMode ? "active" : ""}`}
          ></i>
        )}

        {prevState.patrolSystemSetting?.sixFrame === 1 && (
          <i
            onClick={() => handleChangeWind(6)}
            className={`iconfont iconliufen ${windNum === 6 && !prevState.isAdaptiveMode ? "active" : ""}`}
          ></i>
        )}
        {prevState.patrolSystemSetting?.nineFrame === 1 && (
          <i
            onClick={() => handleChangeWind(9)}
            className={`iconfont iconjiufen ${windNum === 9 && !prevState.isAdaptiveMode ? "active" : ""}`}
          ></i>
        )}
        {prevState.patrolSystemSetting?.sixteenFrame === 1 && (
          <i
            onClick={() => handleChangeWind(16)}
            className={`iconfont iconshiliufen ${windNum === 16 && !prevState.isAdaptiveMode ? "active" : ""
              }`}
          ></i>
        )}
        {prevState.patrolSystemSetting?.adaptFrame === 1 && (
          <i
            onClick={() => handleChangeWind(0)}  
            className={`iconfont iconzishiying ${windNum === 0 ? "active" : ""}`} 
          ></i>
        )}
      </Space>
    </ContentBox>
  );
  const showLiveMode = prevState.mode === "live" || !prevState.isMonitor;

  const isHSPluginMode =
    prevState.mode === "rtsp" &&
    prevState.isMonitor &&
    prevState.vendor == 1002;
  const [hsInfo, setHsInfo] = useState({
    showWinSelector: false,
    winValue: "1x1",
  });

  // 喊话
  const handleShowSpeakModal = () => {
    console.log(123)
    if (!activeRoomId) {
      message.warning("请选择教室");
      return;
    }
    if (videoList.length > 1 && activeIndex === -1) {
      message.warning("当前教室存在多个设备，请先选择要喊话的画面");
      return;
    }
    setShowSpeak(true);
  }

  return (
    <Root className="h-12 bg-white">
      <div className="left" size={20}>
        {/* 监控TAB 显示切换 */}
        {prevState.isMonitor && (
          <Space>
            <span>显示通道详细信息</span>
            <Switch
              checked={prevState.isMonitorSwitch}
              onChange={(checked) => {
                prevDispatch({
                  type: "change_state",
                  payload: {
                    isMonitorSwitch: checked,
                  },
                });
              }}
            />
          </Space>
        )}
        {/* 监控TAB 显示切换 */}
        {prevState.classRoomInfo && prevState.isClassRoom && (
          <Space>
            {prevState.classRoomInfo.subjectTeacherName && (
              <div>
                <i className="iconfont iconyonghu11"></i>
                <span>{prevState.classRoomInfo.subjectTeacherName}</span>
              </div>
            )}
            {prevState.classRoomInfo.subjectName && (
              <div>
                <i className="iconfont iconwenjian4"></i>
                <span>{prevState.classRoomInfo.subjectName}</span>
              </div>
            )}
          </Space>
        )}
      </div>
      <Space className="right" size={20}>
        <i
          onClick={handleShowSpeakModal}
          className="iconfont iconyinpin"
        ></i>

        {showLiveMode && (
          <Popover
            placement="left"
            content={patrolContent}
            arrowPointAtCenter
            overlayClassName="headControlPatrolOverlay"
          >
            <i className={`iconfont ${
              prevState.isAdaptiveMode ? "iconzishiying" : activeClass
            } active`}></i>
          </Popover>
        )}

        {isHSPluginMode && (
          <>
            <i
              onClick={() => {
                setHsInfo((pre) => ({
                  ...pre,
                  showWinSelector: true,
                }));
              }}
              className={`iconfont iconyifen`}
            ></i>
            <i
              onClick={() => {
                if (prevState.oWebControl) {
                  prevState.oWebControl.JS_RequestInterface({
                    method: "window.full_screen",
                  });
                }
              }}
              className="iconfont iconquanping1"
            ></i>
          </>
        )}

        <i
          onClick={handleShowControlBar}
          className="iconfont iconjingtoukongzhi"
        ></i>

        {(showLiveMode && !prevState.isPatrol) && (
          <i onClick={handleShowPatrol} className="iconfont iconlunbo"></i>
        )}
        {(showLiveMode && prevState.isPatrol) && (
          <Tooltip placement="bottom" title={`停止轮巡`}>
            <i
              onClick={() => handleClosePatrol(prevDispatch)}
              className="iconfont iconlunbo"
              style={{ color: '#007aff' }}></i>
          </Tooltip>
        )}

        {showLiveMode && (
          <i onClick={enterFullscreen} className="iconfont iconquanping1"></i>
        )}

        {showLiveMode && <Divider type="vertical" />}
        <Tooltip placement="bottom" title="返回首页">
          <i
            onClick={() => {
              navigate("/");
            }}
            className="iconfont iconfangzi1"
          ></i>
        </Tooltip>
      </Space>

      <Drawer
        title="窗口选择"
        mask={false}
        width={300}
        closable={false}
        visible={hsInfo.showWinSelector}
        extra={
          <CloseOutlined
            onClick={() => {
              setHsInfo((pre) => ({
                ...pre,
                showWinSelector: false,
              }));
            }}
            className="cursor-pointer"
          />
        }
      >
        <Select
          style={{ width: "100%" }}
          value={hsInfo.winValue}
          onChange={(winValue) => {
            if (prevState.oWebControl) {
              prevState.oWebControl.JS_RequestInterface({
                method: "window.set_layout",
                data: {
                  layout: winValue,
                },
              });
            }
            setHsInfo((pre) => ({
              ...pre,
              winValue,
            }));
          }}
          options={[
            {
              value: "1x1",
              label: "1x1",
            },
            {
              value: "1x2",
              label: "1x2",
            },
            {
              value: "1+2",
              label: "1+2",
            },
            {
              value: "1x4",
              label: "1x4",
            },
            {
              value: "2x2",
              label: "2x2",
            },
            {
              value: "1+5",
              label: "1+5",
            },
            {
              value: "3+4",
              label: "3+4",
            },
            {
              value: "1+7",
              label: "1+7",
            },
            {
              value: "1+8",
              label: "1+8",
            },
            {
              value: "3x3",
              label: "3x3",
            },
            {
              value: "1+9",
              label: "1+9",
            },
            {
              value: "1+12",
              label: "1+12",
            },
            {
              value: "4+9",
              label: "4+9",
            },
            {
              value: "1+1+12",
              label: "1+1+12",
            },
            {
              value: "4x4",
              label: "4x4",
            },
            {
              value: "1+16",
              label: "1+16",
            },
            {
              value: "4x6",
              label: "4x6",
            },
            {
              value: "5x5",
              label: "5x5",
            },
            {
              value: "6x6",
              label: "6x6",
            },
          ]}
        ></Select>
      </Drawer>
    </Root>
  );
}
