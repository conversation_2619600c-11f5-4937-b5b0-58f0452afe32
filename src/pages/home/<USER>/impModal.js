import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useRef,
} from "react";
import { message, Modal } from "antd";
import { importMyRecord, importTaskRecord } from "@/api";
import { useSearchParams } from "react-router-dom";
// getRecordTemp
import styled from "styled-components";
import axios from "axios";
import { $fileType, getTokenFromCookie } from "@/tools";
import { PlusOutlined } from "@ant-design/icons";
const UploadContainer = styled.div`
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8c8c8c;
  height: 100px;
  background: #f9fafb;
  border-radius: 2px 2px 2px 2px;
  border: 1px dashed #d9d9d9;
  transition: all 0.2s linear;
  &:hover {
    border-color: #1890ff;
  }
`;
const UpfileList = styled.div`
  margin-bottom: 24px;
  .file-item {
    height: 36px;
    background: #f5f5f5;
    padding: 0 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 12px;
  }
  .icon-del:hover {
    color: #4c84ff !important;
  }
`;
const ImpModal = ({ taskId, onSuccess, isFromAdmin }, ref) => {
  const [params] = useSearchParams();
  const bureauId = params.get("bureauId");
  const inputRef = useRef();
  const [attr, setAttr] = useState(null);
  const [show, setShow] = useState(false);
  useImperativeHandle(ref, () => ({
    showModal() {
      setShow(true);
    },
  }));
  const submit = async () => {
    if (!attr) return;
    const form = new FormData();
    form.append("taskId", taskId);
    form.append("file", attr);
    try {
      let result = await importMyRecord(form);
      if (result.code === 0) {
        message.success("导入成功");
        onSuccess();
      }
    } catch (error) {}
  };
  const fileChange = async (e) => {
    const fileType = $fileType(e.target.files[0].name);
    if (fileType && (fileType === "xls" || fileType === "xlsx")) {
      setAttr(e.target.files[0]);
    } else {
      message.error("请上传excel格式的文件");
    }
    e.target.value = "";
  };
  const cancelImpInfo = () => {
    setAttr(null);
    setShow(false);
  };
  const handle2downTem = async () => {
    let url = "/sd-api/patrol/patrol/myTask/getRecordMould?taskId=" + taskId;
    // if (isFromAdmin) {
    //   url = "/sd-api/patrol/patrol/task/getImportRecordMould?taskId=" + taskId
    // }
    let config = {
      method: "get",
      headers: {
        Authorization: getTokenFromCookie(),
        bureauId,
      },
      url,
      responseType: "blob",
    };
    if (process.env.NODE_ENV === "production") {
      config = window.ysCommonToolLib.encryption(config);
    }
    axios(config).then((res) => {
      let blob = new Blob([res.data], {
        type: "application/vnd.ms-excel",
      });
      let url = window.URL.createObjectURL(blob);
      let a = document.createElement("a");
      a.setAttribute("href", url);
      a.setAttribute("download", "模板.xls");
      a.click();
      window.URL.revokeObjectURL(url);
    });
  };
  return (
    <Modal
      title="导入"
      width="460px"
      visible={show}
      onOk={submit}
      onCancel={cancelImpInfo}
    >
      <UploadContainer onClick={() => inputRef.current.click()}>
        <input
          ref={(c) => (inputRef.current = c)}
          onChange={fileChange}
          type="file"
          className="hidden"
        />
        <PlusOutlined />
        <span>点击上传文件</span>
      </UploadContainer>
      <UpfileList>
        {attr ? (
          <div className="file-item hover:text-blue-500">
            <i
              style={{ fontSize: 18, color: "#595959" }}
              className="iconfont iconfujian1"
            ></i>
            <div className="flex-1 truncate px-2 cursor-default lable">
              {attr.name}
            </div>
            <i
              onClick={() => setAttr(null)}
              style={{ fontSize: 18, color: "#595959" }}
              className="iconfont icon-del cursor-pointer"
            ></i>
          </div>
        ) : null}
      </UpfileList>
      <div>
        <span>下载模板: </span>
        <span onClick={handle2downTem} className="cursor-pointer text-blue-500">
          下载导入模板
        </span>
      </div>
    </Modal>
  );
};

export default forwardRef(ImpModal);
