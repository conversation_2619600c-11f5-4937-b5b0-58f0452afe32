import React, { useState, useRef, useEffect } from "react";
import { useFullscreen } from "ahooks";
import { FormContextProvider } from "@/components";
import { getptcmd, stopptcmd, zoomcmd, stopzoomcmd, getSetting } from "@/api";
import _ from "lodash";
import styled from "styled-components";
import Tabar from "./tabar";
import TourLog from "./tourLog";
import VideoItem from "./videoItem";
import ControlBarTip, {
  ControlBarTipPortal,
} from "../components/controlbarTip";
import PatrolDrawer from "../components/patrolRules/patrolDrawer";
import HeadControlBar from "../components/headControlbar";
import SidePreForm from "../components/sidePreForm";
import { PrevContextProvider } from "./../components/prevContext";
import RtspPlayer from "../components/rtspPlayer";
import SpeakModal from "../components/speakModal";
import { message } from "antd";
import { useDomObserver } from "@/hooks";
import { getVideoPath } from "./../components/getVideoPath";

const Root = styled.div`
  min-height: calc(100vh - 60px);
  background: #f0f2f5;
  overflow-y: auto;
  padding: 24px;
  display: flex;
  justify-content: center;
  .videoContainer {
    flex: 1;
    max-width: 1192px;
  }

  .videoListContainer {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    font-size: 0;
  }
`;
const videoTem = {
  monitoringNodeId: 0,
  belongs: 0,
  url: "",
};
function CoursePreview() {
  const domInfo = useDomObserver("videoContentWrap");
  const videoContainerRef = useRef();
  const [isFullscreen, { enterFullscreen }] = useFullscreen(videoContainerRef);
  const [videoList, setVideoList] = useState([videoTem]);
  const [showBar, setShowBar] = useState(false);
  /* 选中的窗口索引 */
  const [activeIndex, setActiveIndex] = useState(0);

  /* 选取通道 */
  const onChooseChannel = async (data) => {
    if (typeof data === "string" && data === "clear") {
      const list = [];
      new Array(videoList.length).fill("").forEach((_) => {
        list.push(videoTem);
      });
      setVideoList(list);
      return;
    }
    /* 教室模式 data --> array */
    if (data && Array.isArray(data)) {
      data = data.map((item) => {
        if (!item) {
          return { ...videoTem };
        }
        return item;
      });
      setVideoList(data);

      return;
    }
    /* 教室模式 data --> array */

    const hasChannel = videoList.findIndex(
      (item) => item.monitoringNodeId === data.monitoringNodeId
    );
    if (hasChannel !== -1) {
      return message.error("通道已存在");
    }

    /* 有选中的窗口 */
    if (activeIndex !== -1) {
      const list = _.cloneDeep(videoList);
      list.splice(activeIndex, 1, data);
      setVideoList(list);
    } else {
      /* 没有选中的窗口 */
      const list = _.cloneDeep(videoList);
      const findIndex = videoList.findIndex(
        (item) => item.monitoringNodeId === 0
      );
      list.splice(findIndex !== -1 ? findIndex : 0, 1, data);
      setVideoList(list);
    }
  };
  /* 改变窗口数量 */
  const onChangeWind = (windNum) => {
    setActiveIndex(-1);
    // if (windNum === 1) {
    //   setActiveIndex(0);
    // } else {
    //   setActiveIndex(-1);
    // }
     // 自适应模式
     if (windNum === 0) {
      const validStreams = videoList.filter(
        item => item.url && item.monitoringNodeId !== 0
      )
      const targetCount = Math.max(validStreams.length, 1);
      const emptySlots = Math.max(targetCount - validStreams.length, 0);
      const newList = [
        ...validStreams,
        ...Array(emptySlots).fill(videoTem)
      ];
      setVideoList(newList.slice(0, targetCount));
      return;
    }
    const currentWindNum = videoList.length;
    if (windNum > currentWindNum) {
      const list = _.cloneDeep(videoList);
      new Array(Number(windNum - currentWindNum)).fill("").forEach((_) => {
        list.push(videoTem);
      });
      setVideoList(list);
    }
    if (windNum < currentWindNum) {
      let list = _.cloneDeep(videoList);
      list = list.slice(0, windNum);
      setVideoList(list);
    }
  };
  /* 控制 */
  const onBarControl = (e) => {
    console.log("onBarControl", e);
    let publishOrgId = "";
    const orgSelector = document.getElementById("orgSelector");
    if (orgSelector && orgSelector.dataset.id) {
      publishOrgId = orgSelector.dataset.id;
    }
    const type = e.target.getAttribute("data-role");
    const step = e.target.getAttribute("data-step");
    if (type === "close") {
      return setShowBar(false);
    }
    if (!type) return;
    const screenShot = document.querySelector(".screenShot");
    const cameraid = screenShot.getAttribute("cameraid");
    let nodeId = "";
    let belongs = 0;
    if (cameraid) {
      nodeId = cameraid;
    } else {
      if (activeIndex === -1) {
        return message.error("请选取通道");
      }
      belongs = videoList[activeIndex].belongs;
      // if (belongs !== 20000 || belongs !== 40000) {
      //   return message.error("请选取监控或者魔盒通道");
      // }
      nodeId =
        videoList[activeIndex].nodeId ||
        videoList[activeIndex].monitoringNodeId;
    }
    // 聚焦
    let timer;
    if (type === "-" || type === "+") {
      const operation = type === "-" ? 4 : 1;
      zoomcmd({
        operation,
        nodeId,
        step,
        publishOrgId,
        belongs,
      }).then((res) => {
        if (res.code === 0) {
          timer && clearTimeout(timer);
          timer = setTimeout(() => {
            stopzoomcmd({
              operation,
              nodeId,
              step,
              publishOrgId,
              belongs,
            });
          }, 1000);
        }
      });
    } else {
      getptcmd({ direction: type, nodeId, step, publishOrgId, belongs }).then(
        () => {
          timer && clearTimeout(timer);
          timer = setTimeout(() => {
            stopptcmd({
              direction: type,
              nodeId,
              step,
              publishOrgId,
              belongs,
            });
          }, 1000);
        }
      );
    }
    // 转向
  };

  /* 轮巡 */
  const [showPatrol, setShowPatrol] = useState(false);
  const [patrolTimer, setPatrolTimer] = useState(null);
  const [patrolGroups, setPatrolGroups] = useState([]);
  const [currentGroupIndex, setCurrentGroupIndex] = useState(0);

  const onPatrolChannel = async (data, prevState, prevDispatch) => {
    if (patrolTimer) {
      clearInterval(patrolTimer);
      setPatrolTimer(null);
      setPatrolGroups([]);
      setCurrentGroupIndex(0);
    }
    console.log(currentGroupIndex, patrolGroups);
    prevDispatch({
      type: "change_is_patrol",
      payload: { isPatrol: true }
    });

    const totalChannels = data.list;
    const windowCount = videoList.length;

    // 小于等于窗口数：一次性播放，无需轮巡
    if (totalChannels.length <= windowCount) {
      getVideoPath(
        totalChannels,
        (result) => {
          const filled = totalChannels.map((_, i) => result[i] || { ...videoTem });
          const padded = [...filled];
          while (padded.length < windowCount) {
            padded.push({ ...videoTem });
          }
          onChooseChannel(padded);
        }
      );
      return;
    }

    // 通道数大于窗口数：进行分组轮巡
    const groups = groupByWindowCount(totalChannels, windowCount);
    setPatrolGroups(groups);
    setCurrentGroupIndex(0);

    const loadGroup = async (group) => {
      return new Promise((resolve) => {
        getVideoPath(group, (result) => {
          const filled = group.map((_, i) => result[i] || { ...videoTem });
          onChooseChannel(filled);
          resolve();
        });
      });
    };

    await loadGroup(groups[0]);

    const timer = setInterval(async () => {
      setCurrentGroupIndex((prev) => {
        const next = (prev + 1) % groups.length;
        loadGroup(groups[next]);
        return next;
      });
    }, (data.time || 10) * 1000);

    setPatrolTimer(timer);
  };
  const groupByWindowCount = (list, windowCount) => {
    const result = [];
    for (let i = 0; i < list.length; i += windowCount) {
      const group = list.slice(i, i + windowCount);
      while (group.length < windowCount) {
        group.push(null); // 补 null
      }
      result.push(group);
    }
    return result;
  };
  // 组件卸载时清除轮巡定时器
  useEffect(() => {
    return () => {
      if (patrolTimer) {
        clearInterval(patrolTimer);
        setPatrolTimer(null);
        setPatrolGroups([]);
        setCurrentGroupIndex(0);
      }
    };
  }, [patrolTimer]);
  // 关闭轮巡
  const handleClosePatrol = (prevDispatch) => {
    if (patrolTimer) {
      clearInterval(patrolTimer);
      setPatrolTimer(null);
    }
    setPatrolGroups([]);
    setCurrentGroupIndex(0);
    prevDispatch({
      type: "change_is_patrol",
      payload: { isPatrol: false }
    });
  };

  const handleCloseVideo = (data) => {
    const findIndex = videoList.findIndex(
      (item) => item.monitoringNodeId === data.monitoringNodeId
    );
    if (findIndex !== -1) {
      let list = _.cloneDeep(videoList);
      list.splice(findIndex, 1, videoTem);
      setVideoList(list);
    }
  };
  /* 双画面切换画面 */
  const onToggleUrl = (data) => {
    const findIndex = videoList.findIndex(
      (item) => item.monitoringNodeId === data.monitoringNodeId
    );
    if (findIndex !== -1) {
      let list = _.cloneDeep(videoList);

      const urlIndex = data.urlList.findIndex((item) => item === data.url);
      if (data.urlList[urlIndex + 1]) {
      }
      let url = data.urlList[urlIndex + 1] || data.urlList[0];

      list.splice(findIndex, 1, {
        ...data,
        url,
      });
      setVideoList(list);
    }
  };

  function clearVideo() {
    const newList = [];
    videoList.forEach((_) => {
      newList.push(videoTem);
    });
    setVideoList(newList);
    setActiveIndex(-1);
  }
  function onChangeIndex(index) {
    const list = _.cloneDeep(videoList);
    const target = list[index];
    const main = list[0];
    list[0] = target;
    list[index] = main;
    setVideoList(list);
  }

  const [showSpeak, setShowSpeak] = useState(false);
  const [activeRoomId, setActiveRoomId] = useState("");

  // 水印
  const [openWaterMark, setOpenWaterMark] = useState(0)
  useEffect(() => {
    getSetting().then((res) => {
      if (res.code === 0) {
        setOpenWaterMark(res.data.patrolSystemSetting.openWaterMark);
      }
    });
  },[])

  return (
    <FormContextProvider>
      <PrevContextProvider>
        <Root>
          <div onClick={onBarControl}>
            {showBar && (
              <ControlBarTipPortal>
                <ControlBarTip />
              </ControlBarTipPortal>
            )}
          </div>
          <div>
            {showPatrol && (
              <PatrolDrawer
              showPatrol={showPatrol}
              setShowPatrol={setShowPatrol}
              businessType={2}
              onPatrolChannel={onPatrolChannel}
              />
            )}
          </div>

          <Tabar
            onChoose={onChooseChannel}
            videoList={videoList}
            currentHeight={domInfo?.height}
            setActiveRoomId={setActiveRoomId}
          />

          <div id="videoContainer" className="videoContainer px-6">
            <div id="videoContentWrap">
              <HeadControlBar
                enterFullscreen={enterFullscreen}
                onChangeWind={onChangeWind}
                setShowBar={setShowBar}
                setShowPatrol={setShowPatrol}
                setShowSpeak={setShowSpeak}
                handleClosePatrol={handleClosePatrol}
                windNum={videoList.length}
                activeIndex={activeIndex}
                videoList={videoList}
                activeRoomId={activeRoomId}
                isFree
              />
              <div
                className="videoListContainer bg-white relative"
                id="videoListContainer"
                ref={videoContainerRef}
                style={{ fontSize: 0 }}
              >
                <RtspPlayer clearVideo={clearVideo} />
                {videoList.map((video, index) => (
                  <VideoItem
                    onToggleUrl={onToggleUrl}
                    onClose={handleCloseVideo}
                    onChoose={() => setActiveIndex(index)}
                    info={video}
                    windNum={videoList.length}
                    openWaterMark={openWaterMark}
                    active={activeIndex === index}
                    key={index}
                    isFullscreen={isFullscreen}
                    index={index}
                    onChangeIndex={onChangeIndex}
                  />
                ))}
              </div>
              <TourLog activeIndex={activeIndex} videoList={videoList} />
            </div>
          </div>

          <SidePreForm currentHeight={domInfo?.height} isFree />

          <div>
            {showSpeak && (
              <SpeakModal
                showSpeak={showSpeak}
                setShowSpeak={setShowSpeak}
                activeRoomId={activeRoomId}
              />
            )}
          </div>
        </Root>
      </PrevContextProvider>
    </FormContextProvider>
  );
}
export default CoursePreview;
