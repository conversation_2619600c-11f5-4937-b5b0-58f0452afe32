import React, { useState, useRef } from "react";
import { useFullscreen } from "ahooks";
import { FormContextProvider } from "@/components";
import { getPlayUrl, getptcmd, stopptcmd, zoomcmd, stopzoomcmd } from "@/api";
import _ from "lodash";
import styled from "styled-components";
import Tabar from "./tabar";

import TourLog from "./tourLog";
import VideoItem from "./videoItem";
import ControlBarTip, {
  ControlBarTipPortal,
} from "../components/controlbarTip";
import { PrevContext, PrevContextProvider } from "./../components/prevContext";
import { message } from "antd";
import { useDomObserver } from "@/hooks";
import HeadControlBar from "../components/headControlbar";
import RtspPlayer from "../components/rtspPlayer";

const Root = styled.div`
  min-height: calc(100vh - 60px);
  background: #f0f2f5;
  overflow-y: auto;
  padding: 24px;
  display: flex;
  justify-content: center;

  .videoContainer {
    flex: 1;
    max-width: 1192px;
  }

  .videoListContainer {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    font-size: 0;
  }
`;
const videoTem = {
  monitoringNodeId: 0,
  belongs: 0,
  url: "",
};
function CoursePreview() {
  const domInfo = useDomObserver("videoContentWrap");
  const videoContainerRef = useRef();
  const [isFullscreen, { enterFullscreen }] = useFullscreen(videoContainerRef);
  const [videoList, setVideoList] = useState([videoTem]);
  const [showBar, setShowBar] = useState(false);
  /* 选中的窗口索引 */
  const [activeIndex, setActiveIndex] = useState(0);

  const generateUrl = async (data) => {
    if (data.url) return data.url;
    const result = await getPlayUrl({
      belong: data.belongs,
      nodeId: data.monitoringNodeId,
    });
    /* 第三方录播 */
    if (result && result.code === 0 && typeof result.data === "string") {
      return result.data;
    }
    /* 录播设备和监控字段不一样 */
    if (result && result.code === 0 && result.data[0]) {
      if (data.belongs === 20000) {
        return result.data[0].pcUrl;
      }
      return result.data[0].flvUrl;
    }
  };
  /* 选取通道 */
  const onChooseChannel = async (data) => {
    if (data && Array.isArray(data)) {
      data = data.map((item) => {
        if (!item) {
          return { ...videoTem };
        }
        return item;
      });
      setVideoList(data);

      return;
    }
    // ====
    const hasChannel = videoList.findIndex(
      (item) => item.monitoringNodeId === data.monitoringNodeId
    );
    if (hasChannel !== -1) {
      return message.error("通道已存在");
    }

    // const url = await generateUrl(data);
    // if (!url) return;
    // const videoItem = {
    //   url,
    //   monitoringNodeId: data.monitoringNodeId,
    //   belongs: data.belongs,
    //   urlList: data.urlList || [],
    //   monitorBindClassInfo: data.monitorBindClassInfo,
    // };
    /* 有选中的窗口 */
    if (activeIndex !== -1) {
      const list = _.cloneDeep(videoList);
      list.splice(activeIndex, 1, data);
      setVideoList(list);
    } else {
      /* 没有选中的窗口 */
      const list = _.cloneDeep(videoList);
      const findIndex = videoList.findIndex(
        (item) => item.monitoringNodeId === 0
      );
      list.splice(findIndex !== -1 ? findIndex : 0, 1, data);
      setVideoList(list);
    }
  };
  /* 改变窗口数量 */
  const onChangeWind = (windNum) => {
    if (windNum === 1) {
      setActiveIndex(0);
    } else {
      setActiveIndex(-1);
    }
    const currentWindNum = videoList.length;
    if (windNum > currentWindNum) {
      const list = _.cloneDeep(videoList);
      new Array(Number(windNum - currentWindNum)).fill("").forEach((_) => {
        list.push(videoTem);
      });
      setVideoList(list);
    }
    if (windNum < currentWindNum) {
      let list = _.cloneDeep(videoList);
      list = list.slice(0, windNum);
      setVideoList(list);
    }
  };
  /* 控制 */
  const onBarControl = (e) => {
    const type = e.target.getAttribute("data-role");
    const step = e.target.getAttribute("data-step");
    if (type === "close") {
      return setShowBar(false);
    }
    if (!type) return;
    const screenShot = document.querySelector(".screenShot");
    const cameraid = screenShot.getAttribute("cameraid");
    let nodeId = "";
    let belongs = 0;
    if (cameraid) {
      nodeId = cameraid;
    } else {
      if (activeIndex === -1) {
        return message.error("请选取通道");
      }
      belongs = videoList[activeIndex].belongs;
      nodeId =
        videoList[activeIndex].nodeId ||
        videoList[activeIndex].monitoringNodeId;
    }
    // 聚焦
    let timer;
    if (type === "-" || type === "+") {
      const operation = type === "-" ? 4 : 1;
      zoomcmd({
        operation,
        nodeId,
        step,
        belongs,
      }).then((res) => {
        if (res.code === 0) {
          timer && clearTimeout(timer);
          timer = setTimeout(() => {
            stopzoomcmd({
              operation,
              nodeId,
              step,
              belongs,
            });
          }, 1000);
        }
      });
    } else {
      getptcmd({ direction: type, nodeId, step, belongs }).then(() => {
        timer && clearTimeout(timer);
        timer = setTimeout(() => {
          stopptcmd({
            direction: type,
            nodeId,
            step,
            belongs,
          });
        }, 1000);
      });
    }
    // 转向
  };

  const handleCloseVideo = (data) => {
    const findIndex = videoList.findIndex(
      (item) => item.monitoringNodeId === data.monitoringNodeId
    );
    if (findIndex !== -1) {
      let list = _.cloneDeep(videoList);
      list.splice(findIndex, 1, videoTem);
      setVideoList(list);
    }
  };
  /* 双画面切换画面 */
  const onToggleUrl = (data) => {
    const findIndex = videoList.findIndex(
      (item) => item.monitoringNodeId === data.monitoringNodeId
    );
    if (findIndex !== -1) {
      let list = _.cloneDeep(videoList);
      const urlIndex = data.urlList.findIndex((item) => item === data.url);
      if (data.urlList[urlIndex + 1]) {
      }
      let url = data.urlList[urlIndex + 1] || data.urlList[0];
      list.splice(findIndex, 1, {
        ...data,
        url,
      });
      setVideoList(list);
    }
  };
  function clearVideo() {
    const newList = [];
    videoList.forEach((_) => {
      newList.push(videoTem);
    });
    setVideoList(newList);
    setActiveIndex(-1);
  }
  function onChangeIndex(index) {
    const list = _.cloneDeep(videoList);
    const target = list[index];
    const main = list[0];
    list[0] = target;
    list[index] = main;
    setVideoList(list);
  }
  return (
    <FormContextProvider>
      <PrevContextProvider>
        <Root>
          <div onClick={onBarControl}>
            {showBar && (
              <ControlBarTipPortal>
                <ControlBarTip />
              </ControlBarTipPortal>
            )}
          </div>

          <Tabar
            onChoose={onChooseChannel}
            videoList={videoList}
            currentHeight={domInfo?.height}
          />

          <div id="videoContainer" className="videoContainer px-6">
            <div id="videoContentWrap">
              <HeadControlBar
                enterFullscreen={enterFullscreen}
                onChangeWind={onChangeWind}
                setShowBar={setShowBar}
                windNum={videoList.length}
              />
              <div
                className="videoListContainer bg-white relative"
                id="videoListContainer"
                ref={videoContainerRef}
                style={{ fontSize: 0 }}
              >
                <RtspPlayer clearVideo={clearVideo} />
                {videoList.map((video, index) => (
                  <VideoItem
                    onToggleUrl={onToggleUrl}
                    onClose={handleCloseVideo}
                    onChoose={() => setActiveIndex(index)}
                    info={video}
                    windNum={videoList.length}
                    active={activeIndex === index}
                    key={index}
                    isFullscreen={isFullscreen}
                    index={index}
                    onChangeIndex={onChangeIndex}
                  />
                ))}
              </div>
              <TourLog activeIndex={activeIndex} videoList={videoList} />
            </div>
          </div>
        </Root>
      </PrevContextProvider>
    </FormContextProvider>
  );
}
export default CoursePreview;
