import React, { useEffect, useState, useRef, useMemo } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import { useRouterGo } from "@/hooks/useRouterGo";
import styled from "styled-components";
import { FormContextProvider } from "@/components";
import ReactECharts from "echarts-for-react";
import moment from "moment";
import {
  selectPatrolRecordByTaskId,
  selectTasksRecordByTaskId,
  deletePatrolRecord,
  getMyTasksDetail,
  getTasksDetail,
  evaluate,
  patrolmove,
  selectRecordByPage,
  patrolTotal,
  patrolUser,
} from "@/api";
import {
  Table,
  Space,
  Button,
  Pagination,
  Tooltip,
  Modal,
  message,
  Input,
  Select,
  Dropdown,
  Menu,
} from "antd";
import {
  PlusOutlined,
  QuestionCircleFilled,
  CloseOutlined,
  DownOutlined,
} from "@ant-design/icons";
import { PrevContextProvider } from "./../components/prevContext";
import TourLog from "./tourLogDrawer";
import DetailDrawer from "./detailDrawer";
import PersonalDrawer from "./personalDrawer";
import AppraisedDrawer from "./appraisedDrawer";
import ImpModal from "./impModal";
const Root = styled.div`
  position: relative;
  background: #f0f2f5;
  padding: 72px 0 32px;
  height: calc(100vh - 60px);
  overflow: auto;
  .top-head {
    z-index: 9;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    background: #fff;
    height: 56px;
    .head-container {
      width: 80%;
      margin: 0 auto;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #262626;
      font-size: 16px;
    }
  }
  .chooseTab {
    margin: 0 auto;
    width: 80%;
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    div {
      width: 80px;
      height: 32px;
      background: #fff;
      border-radius: 9999px 9999px 9999px 9999px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 32px;
      cursor: pointer;
    }
    .activeItem {
      background: #007aff;
      color: #ffffff;
    }
  }
  .main {
    min-height: 100%;
    padding: 24px;
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    width: 80%;
    .title {
      display: flex;
      justify-content: space-between;
      color: #262626;
      margin-bottom: 20px;
    }
  }
  .main1 {
    min-height: 100%;
    margin: 0 auto;
    border-radius: 4px;
    width: 80%;
    .tabNum {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .tabItem {
        width: calc(33% - 12px);
        height: 120px;
        background: #ffffff;
        border-radius: 4px 4px 4px 4px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 24px;
        .leftIcon {
          width: 72px;
          height: 72px;
          background: #e6fffb;
          border-radius: 36px 36px 36px 36px;
          display: flex;
          justify-content: center;
          align-items: center;
          .iconzhexiantu {
            font-size: 32px;
            color: #13c2c2;
          }
        }
        .centerIcon {
          width: 72px;
          height: 72px;
          background: rgba(250, 84, 28, 0.12);
          border-radius: 36px 36px 36px 36px;
          display: flex;
          justify-content: center;
          align-items: center;
          .iconyonghu11 {
            font-size: 32px;
            color: #fa541c;
          }
        }
        .rightIcon {
          width: 72px;
          height: 72px;
          background: rgba(39, 163, 250, 0.12);
          border-radius: 36px 36px 36px 36px;
          display: flex;
          justify-content: center;
          align-items: center;
          .iconpingfen {
            font-size: 32px;
            color: #27a3fa;
          }
        }
        .rightInfo {
          width: calc(100% - 72px);
          display: flex;
          flex-direction: column;
          flex-wrap: wrap;
          justify-content: center;
          align-items: center;
          .num {
            font-weight: bold;
            font-size: 28px;
            color: #262626;
          }
          .title {
            color: #8c8c8c;
            font-size: 14px;
            // margin-top: 8px;
          }
        }
      }
    }
    .potrolEcharts {
      margin-top: 16px;
      height: 360px;
      background: #ffffff;
      padding: 20px 24px;
      .headTitle {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .echarts {
        width: 100%; /* 确保 echarts 容器宽度为 100% */
        height: 100%; /* 可选，根据需要调整高度 */
      }
      .empty {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 20px;
        font-weight: bold;
      }
    }
    .potrolPersonList {
      margin-top: 16px;
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
      padding: 20px 24px;
      .personList {
        margin-top: 16px;
      }
    }
    .appraisedObjectList {
      margin-top: 16px;
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
      padding: 20px 24px;
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .personList {
        margin-top: 16px;
      }
    }
  }
  .status {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: center;
    .greenDiv {
      width: 6px;
      height: 6px;
      background: #17be6b;
      border-radius: 50%;
    }
    .darkDiv {
      width: 6px;
      height: 6px;
      background: #d9d9d9;
      border-radius: 50%;
    }
    .titleStatus {
      margin-left: 8px;
    }
  }
`;

function Task() {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const isFromAdmin = searchParams.get("from");
  const navigate = useRouterGo();
  const impRef = useRef();
  const tourLogRef = useRef();
  const detailDrawerRef = useRef();
  const personDrawerRef = useRef();
  const appraisedDrawerRef = useRef();
  const dropdownTitle1Ref = useRef(1);
  const [dropdownTitle, setDropdownTitle] = useState(2);
  const [dropdownTitle1, setDropdownTitle1] = useState(1);
  const filterRef = useRef({
    userType: 0,
    userName: "",
  });
  const [taskInfo, setTaskInfo] = useState(null);
  const [data, setData] = useState({
    list: [],
    total: 0,
  });
  const [patrolData, setPatrolData] = useState({
    list: [],
    total: 0,
  });
  const [appraisedData, setAppraisedData] = useState({
    list: [],
    total: 0,
  });
  const [appraisedpagination, setappraisedPagination] = useState({
    pageNo: 1,
    pageSize: 10,
  });
  const [patrolpagination, setPatrolPagination] = useState({
    pageNo: 1,
    pageSize: 10,
  });
  const [appraisedSort, setAppraisedSort] = useState(null);
  const [patrolSort, setpatrolSort] = useState(null);
  const [pagination, setPagination] = useState({
    pageNo: 1,
    pageSize: 10,
  });
  const [xData, setXData] = useState([]);
  const [yData, setYData] = useState([]);
  const [showEacharts, setShowEacharts] = useState(true);
  const [active, setActive] = useState(1); //1统计 2详情
  const [numberInfo, setNumberInfo] = useState({
    avgScore: null,
    complete: null,
    total: null,
  });
  // 被评价对象列表
  useEffect(() => {
    let params = {
      ...appraisedpagination,
      taskId: id,
      sort: appraisedSort,
      type: dropdownTitle1,
    };
    evaluate(params).then((res) => {
      if (res.code == 0) {
        setAppraisedData({ list: res.data.records, total: res.data.total * 1 });
      }
    });
  }, [id, appraisedpagination, appraisedSort, dropdownTitle1]);
  // 巡课人员列表
  useEffect(() => {
    let params = {
      ...patrolpagination,
      taskId: id,
      sort: patrolSort,
    };
    patrolUser(params).then((res) => {
      if (res.code == 0) {
        setPatrolData({ list: res.data.records, total: res.data.total * 1 });
      }
    });
  }, [id, patrolpagination, patrolSort]);
  // tab数据统计
  useEffect(() => {
    patrolTotal({ taskId: id }).then((res) => {
      if (res.code == 0) {
        let complete =
          res.data && res.data.complete && res.data.complete.split("%")[0];
        let obj = {
          avgScore: res.data.avgScore,
          complete: complete,
          total: res.data.total,
        };
        setNumberInfo(obj);
      }
    });
  }, [id]);
  // 树状图统计
  useEffect(() => {
    patrolmove({ taskId: id, type: dropdownTitle }).then((res) => {
      if (res.code == 0) {
        console.log("res.data", res.data);
        const XList = [];
        const YList = [];
        if (res.data && res.data.length > 0) {
          setShowEacharts(true);
          for (let i of res.data) {
            if (dropdownTitle < 3) {
              XList.push(i.name);
              YList.push(i.num);
            } else {
              XList.push(i.option_answer);
              YList.push(i.num);
            }
          }
          setXData(XList);
          setYData(YList);
        } else {
          setXData([]);
          setYData([]);
          setShowEacharts(false);
        }
      }
    });
  }, [id, dropdownTitle]);
  // echarts柱状图数据源
  const option1 = {
    // width: "90%",
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
      },
    },
    xAxis: {
      type: "category",
      data: xData,
      axisTick: {
        alignWithLabel: true,
      },
      axisLabel: {
        margin: 15, // 调整标签与轴线的距离
        alignWithLabel: true, // 标签与柱状图对齐
      },
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        name: "次数",
        type: "bar",
        barWidth: "40px",
        data: yData,
        itemStyle: {
          color: "#5B8FF9", // 设置柱体的背景颜色为绿色
        },
      },
    ],
    grid: {
      bottom: "30%", // 留出底部空间
    },
    graphic: {
      elements: [
        {
          type: "text",
          left: "8.5%",
          top: "73%",
          style: {
            text: "(次)",
            fontSize: 14,
            fill: "#8C8C8C",
          },
        },
      ],
    },
  };
  const [option, setOption] = useState(null);
  const handleChangeTab = (tab) => {
    console.log('tab', tab);
    setActive(tab);
    if (tab == 1) {
      patrolTotal({ taskId: id }).then((res) => {
        if (res.code == 0) {
          let complete =
            res.data && res.data.complete && res.data.complete.split("%")[0];
          let obj = {
            avgScore: res.data.avgScore,
            complete: complete,
            total: res.data.total,
          };
          setNumberInfo(obj);
        }
      });
    } else {
      // getPatrolClassRoom({ taskId: id }).then((res) => {
      //   setOption(res.data);
      // });
    }
  };
  useEffect(() => {
    if (isFromAdmin) {
      getTasksDetail({ taskId: id }).then((res) => {
        if (res.code === 0) {
          setTaskInfo({
            ...res.data.patrolTask,
            patrolObject: res.data.patrolObject,
          });
        }
      });
    } else {
      getMyTasksDetail({ taskId: id }).then((res) => {
        if (res.code === 0) {
          setTaskInfo(res.data);
        }
      });
    }
  }, [id, isFromAdmin]);

  useEffect(() => {
    const params = {
      taskId: id,
      ...pagination,
      ...filterRef.current,
    };
    if (isFromAdmin) {
      selectTasksRecordByTaskId(params).then((res) => {
        if (res.code === 0) {
          setData({ list: res.data, total: res.totalDatas * 1 });
        }
      });
    } else {
      selectPatrolRecordByTaskId(params).then((res) => {
        if (res.code === 0) {
          setData({ list: res.data, total: res.totalDatas * 1 });
        }
      });
    }
  }, [id, pagination, isFromAdmin]);

  const taskInfoIsOver = useMemo(() => {
    if (!taskInfo) return true;
    const endTime = taskInfo.endTime;
    if (+new Date() > endTime) {
      return true;
    }
    return false;
  }, [taskInfo]);

  const columns = [
    {
      title: "序号",
      width: 60,
      align: "center",
      key: "index",
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: "被记录人",
      dataIndex: "recordedUserName",
      align: "center",
      key: "recordedUserName",
      ellipsis: true,
    },
    {
      title: "被记录班级",
      width: 160,
      dataIndex: "className",
      align: "center",
      key: "className",
      render: (_) => <span>{_ || "--"}</span>,
    },
    {
      title: "被记录年级",
      width: 160,
      dataIndex: "gradeName",
      align: "center",
      key: "gradeName",
      render: (_) => <span>{_ || "--"}</span>,
    },
    {
      title: "巡课时间",
      width: 160,
      dataIndex: "patrolTime",
      align: "center",
      key: "patrolTime",
      render: (_) => moment(_).format("YYYY-MM-DD HH:mm"),
      sorter: (a, b) => a.patrolTime - b.patrolTime,
    },
    {
      title: "记录提交时间",
      width: 160,
      dataIndex: "recordTime",
      align: "center",
      key: "recordTime",
      render: (_) => moment(_).format("YYYY-MM-DD HH:mm"),
      sorter: (a, b) => a.recordTime - b.recordTime,
    },
    {
      title: "记录人",
      dataIndex: "recordUserName",
      align: "center",
      key: "recordUserName",
      ellipsis: true,
    },
    {
      title: "图片",
      dataIndex: "picCount",
      align: "center",
      key: "picCount",
    },
    {
      title: "视频",
      dataIndex: "videoCount",
      align: "center",
      key: "videoCount",
    },
    {
      title: "量表",
      dataIndex: "useTable",
      align: "center",
      key: "useTable",
      render: (_, record) => `${record.useTable === 2 ? "无" : "有"}`,
    },
    {
      title: "评语",
      dataIndex: "patrolComment",
      align: "center",
      key: "patrolComment",
      ellipsis: {
        showTitle: false,
      },
      render: (patrolComment) => (
        <Tooltip placement="topLeft" title={patrolComment}>
          {patrolComment}
        </Tooltip>
      ),
    },
    {
      title: "操作",
      width: 160,
      key: "control",
      align: "center",
      render: (_, record) => (
        <div>
          {!taskInfoIsOver && (
            <span
              onClick={() => handle2Edit(record)}
              className="cursor-pointer text-blue-500"
            >
              编辑
            </span>
          )}

          {!taskInfoIsOver && (
            <span
              onClick={() => handle2Detail(record)}
              className="cursor-pointer text-blue-500 mx-2"
            >
              详情
            </span>
          )}

          <span
            onClick={() => handle2Delete(record)}
            className="cursor-pointer text-blue-500"
          >
            删除
          </span>
        </div>
      ),
    },
  ];
  const patrolColumns = [
    {
      title: "序号",
      width: 60,
      align: "center",
      key: "index",
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: "姓名",
      dataIndex: "userName",
      align: "center",
      key: "userName",
      ellipsis: true,
    },
    {
      title: "机构",
      dataIndex: "orgName",
      align: "center",
      key: "orgName",
      ellipsis: true,
    },
    {
      title: "手机号",
      dataIndex: "phone",
      align: "center",
      key: "phone",
      render: (_, record) => (
        <div>
          <span>{record.phone ? record.phone : "--"}</span>
        </div>
      ),
    },
    {
      title: "通道数",
      dataIndex: "chanCount",
      align: "center",
      key: "chanCount",
      sorter: (a, b) => a.chanCount - b.chanCount,
    },
    {
      title: "记录总数",
      dataIndex: "recordCount",
      align: "center",
      key: "recordCount",
      sorter: (a, b) => a.recordCount - b.recordCount,
    },
    {
      title: "记录要求",
      dataIndex: "mustCount",
      align: "center",
      key: "mustCount",
      sorter: (a, b) => a.mustCount - b.mustCount,
    },
    {
      title: "状态",
      dataIndex: "complete",
      align: "center",
      key: "complete",
      render: (_, record) => (
        <div className="status">
          <div
            className={record.complete == "1" ? "greenDiv" : "darkDiv"}
          ></div>
          <div className="titleStatus">
            {record.complete == "1" ? "已完成" : "未完成"}
          </div>
        </div>
      ),
    },
    {
      title: "操作",
      dataIndex: "action",
      align: "center",
      render: (_, record) => (
        <div>
          <span
            onClick={() => handlepersonDetail(record)}
            className="cursor-pointer text-blue-500 mx-2"
          >
            查看详情
          </span>
        </div>
      ),
    },
  ];

  const [appraisedColumns, setAppraisedColumns] = useState([
    {
      title: "序号",
      width: 60,
      align: "center",
      key: "index",
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: "姓名",
      dataIndex: "userName",
      align: "center",
      key: "userName",
      ellipsis: true,
    },
    {
      title: "机构",
      dataIndex: "orgName",
      align: "center",
      key: "orgName",
      ellipsis: true,
    },
    {
      title: "手机号",
      dataIndex: "phone",
      align: "center",
      key: "phone",
    },
    {
      title: "被评次数",
      dataIndex: "evaluateCount",
      align: "center",
      key: "evaluateCount",
      sorter: true,
    },
    {
      title: "平均分",
      dataIndex: "avgScore",
      align: "center",
      key: "avgScore",
      sorter: true,
      render: (_, record, index) => `${(record.avgScore || 0).toFixed(2)}`,
    },
    {
      title: "最高分",
      dataIndex: "maxScore",
      align: "center",
      key: "maxScore",
      sorter: (a, b) => a.maxScore - b.maxScore,
    },
    {
      title: "最低分",
      dataIndex: "minScore",
      align: "center",
      key: "minScore",
      sorter: (a, b) => a.minScore - b.minScore,
    },
    {
      title: "操作",
      dataIndex: "action",
      align: "center",
      render: (_, record) => (
        <div>
          <span
            onClick={() => handleappraisedDetail(record, dropdownTitle1)}
            className="cursor-pointer text-blue-500 mx-2"
          >
            查看详情
          </span>
        </div>
      ),
    },
  ]);
  // 被评价人对象列表-教师
  const appraisedTeacherColumns = [
    {
      title: "序号",
      width: 60,
      align: "center",
      key: "index",
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: "姓名",
      dataIndex: "userName",
      align: "center",
      key: "userName",
      ellipsis: true,
    },
    {
      title: "机构",
      dataIndex: "orgName",
      align: "center",
      key: "orgName",
      ellipsis: true,
    },
    {
      title: "手机号",
      dataIndex: "phone",
      align: "center",
      key: "phone",
    },
    {
      title: "被评次数",
      dataIndex: "evaluateCount",
      align: "center",
      key: "evaluateCount",
      sorter: true,
    },
    {
      title: "平均分",
      dataIndex: "avgScore",
      align: "center",
      key: "avgScore",
      sorter: true,
    },
    {
      title: "最高分",
      dataIndex: "maxScore",
      align: "center",
      key: "maxScore",
      sorter: true,
    },
    {
      title: "最低分",
      dataIndex: "minScore",
      align: "center",
      key: "minScore",
      sorter: true,
    },
    {
      title: "操作",
      dataIndex: "action",
      align: "center",
      render: (_, record) => (
        <div>
          <span
            onClick={() => handleappraisedDetail(record, dropdownTitle1)}
            className="cursor-pointer text-blue-500 mx-2"
          >
            查看详情
          </span>
        </div>
      ),
    },
  ];
  // 被评价人对象列表-年级
  const appraisedGradeColumns = [
    {
      title: "序号",
      width: 60,
      align: "center",
      key: "index",
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: "年级",
      dataIndex: "gradeName",
      align: "center",
      key: "gradeName",
      ellipsis: true,
    },
    {
      title: "被评次数",
      dataIndex: "evaluateCount",
      align: "center",
      key: "evaluateCount",
      sorter: true,
    },
    {
      title: "平均分",
      dataIndex: "avgScore",
      align: "center",
      key: "avgScore",
      sorter: true,
    },
    {
      title: "最高分",
      dataIndex: "maxScore",
      align: "center",
      key: "maxScore",
      sorter: true,
    },
    {
      title: "最低分",
      dataIndex: "minScore",
      align: "center",
      key: "minScore",
      sorter: true,
    },
    {
      title: "操作",
      dataIndex: "action",
      align: "center",
      render: (_, record) => (
        <div>
          <span
            onClick={() => handleappraisedDetail(record, dropdownTitle1)}
            className="cursor-pointer text-blue-500 mx-2"
          >
            查看详情
          </span>
        </div>
      ),
    },
  ];
  // 被评价人对象列表-班级
  const appraisedClassColumns = [
    {
      title: "序号",
      width: 60,
      align: "center",
      key: "index",
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: "班级",
      dataIndex: "className",
      align: "center",
      key: "gradeName",
      ellipsis: true,
    },
    {
      title: "被评次数",
      dataIndex: "evaluateCount",
      align: "center",
      key: "evaluateCount",
      sorter: true,
    },
    {
      title: "平均分",
      dataIndex: "avgScore",
      align: "center",
      key: "avgScore",
      sorter: true,
    },
    {
      title: "最高分",
      dataIndex: "maxScore",
      align: "center",
      key: "maxScore",
      sorter: true,
    },
    {
      title: "最低分",
      dataIndex: "minScore",
      align: "center",
      key: "minScore",
      sorter: true,
    },
    {
      title: "操作",
      dataIndex: "action",
      align: "center",
      render: (_, record) => (
        <div>
          <span
            onClick={() => handleappraisedDetail(record, dropdownTitle1)}
            className="cursor-pointer text-blue-500 mx-2"
          >
            查看详情
          </span>
        </div>
      ),
    },
  ];

  const handle2Delete = ({ patrolRecordId }) => {
    Modal.confirm({
      title: "确定删除这条记录吗？",
      icon: <QuestionCircleFilled />,
      onOk() {
        deletePatrolRecord({ patrolRecordId }).then(({ code }) => {
          if (code === 0) {
            message.success("删除成功");
            setPagination({ pageNo: 1, pageSize: 10 });
          }
        });
      },
    });
  };

  const handle2Edit = ({ patrolRecordId }) => {
    handle2Add(patrolRecordId);
  };

  const handle2Detail = ({ patrolRecordId }) => {
    detailDrawerRef.current.showDrawer(patrolRecordId);
  };
  const handlepersonDetail = ({ userId }) => {
    personDrawerRef.current.showDrawer(userId);
  };

  const handleappraisedDetail = (record) => {
    let id = null;
    if (record.userId) {
      id = record.userId;
    }
    if (record.gradeId) {
      id = record.gradeId;
    }
    if (record.classId) {
      id = record.classId;
    }
    appraisedDrawerRef.current.showDrawer(id, dropdownTitle1Ref.current);
  };
  const handle2Add = (patrolRecordId) => {
    tourLogRef.current.showDrawer(patrolRecordId, id);
  };

  const handleChangeDropdown = (val) => {
    if (val.key == "teacher") {
      setDropdownTitle(2);
    } else if (val.key == "grade") {
      setDropdownTitle(0);
    } else if (val.key == "class") {
      setDropdownTitle(1);
    } else if (val.key == "score") {
      setDropdownTitle(3);
    }
  };
  const handleChangeDropdown1 = (val) => {
    setAppraisedSort(null);
    setAppraisedData({ list: [], total: 0 });
    setappraisedPagination((prev) => ({
      ...prev,
      pageNo: 1,
      pageSize: 10,
    }));
    if (val.key == "teacher") {
      setDropdownTitle1(1);
      dropdownTitle1Ref.current = 1;
      setAppraisedColumns(appraisedTeacherColumns);
    } else if (val.key == "grade") {
      setDropdownTitle1(3);
      dropdownTitle1Ref.current = 3;
      setAppraisedColumns(appraisedGradeColumns);
    } else if (val.key == "class") {
      setDropdownTitle1(2);
      dropdownTitle1Ref.current = 2;
      setAppraisedColumns(appraisedClassColumns);
    }
  };
  const onChangeSort = (pagation, filters, sorter) => {
    if (sorter.field == "chanCount") {
      if (sorter.order == "ascend") {
        setpatrolSort(1);
      } else {
        setpatrolSort(2);
      }
    } else if (sorter.field == "recordCount") {
      if (sorter.order == "ascend") {
        setpatrolSort(3);
      } else {
        setpatrolSort(4);
      }
    } else if (sorter.field == "mustCount") {
      if (sorter.order == "ascend") {
        setpatrolSort(5);
      } else {
        setpatrolSort(6);
      }
    }
  };
  const onChangeSort1 = (pagination, filters, sorter) => {
    console.log("sorter", sorter);
    if (sorter.field == "evaluateCount") {
      if (sorter.order == "ascend") {
        setAppraisedSort(1);
      } else {
        setAppraisedSort(2);
      }
    } else if (sorter.field == "avgScore") {
      if (sorter.order == "ascend") {
        setAppraisedSort(3);
      } else {
        setAppraisedSort(4);
      }
    } else if (sorter.field == "maxScore") {
      if (sorter.order == "ascend") {
        setAppraisedSort(5);
      } else {
        setAppraisedSort(6);
      }
    } else if (sorter.field == "minScore") {
      if (sorter.order == "ascend") {
        setAppraisedSort(7);
      } else {
        setAppraisedSort(8);
      }
    }
  };
  useEffect(() => {
    setDropdownTitle1(dropdownTitle1);
  }, [dropdownTitle1]);
  return (
    <Root>
      <div className="top-head">
        <div className="head-container">
          <div>详情 {taskInfo ? "- " + taskInfo.taskName : ""}</div>
          <CloseOutlined
            onClick={() => navigate(-1)}
            style={{ fontSize: 14, cursor: "pointer" }}
          />
        </div>
      </div>
      <div className="chooseTab">
        <div
          onClick={() => handleChangeTab(1)}
          className={active === 1 ? "activeItem" : "tabItem"}
        >
          统计
        </div>
        <div
          onClick={() => handleChangeTab(2)}
          className={active === 2 ? "activeItem" : "tabItem"}
        >
          详情
        </div>
      </div>
      {active == 2 && (
        <div className="main">
          <div className="title">
            <div>详情列表</div>
            <Space>
              <Input.Group compact>
                <Select
                  defaultValue="0"
                  onChange={(e) => {
                    filterRef.current.userType = e;
                  }}
                >
                  <Select.Option value="0">全部</Select.Option>
                  <Select.Option value="1">记录人</Select.Option>
                  <Select.Option value="2">被记录人</Select.Option>
                </Select>
                <Input.Search
                  onChange={(e) => {
                    filterRef.current.userName = e.target.value;
                  }}
                  onSearch={() => {
                    setPagination((pre) => ({
                      ...pre,
                      pageNo: 1,
                    }));
                  }}
                  style={{ width: 200 }}
                  placeholder="记录人/被记录人姓名"
                />
              </Input.Group>
              <Button
                onClick={() => {
                  impRef.current.showModal();
                }}
              >
                导入
              </Button>
              <Button
                onClick={() => handle2Add(0)}
                icon={<PlusOutlined />}
                type="primary"
              >
                添加
              </Button>
            </Space>
          </div>

          <Table
            columns={columns}
            pagination={false}
            rowKey={(column) => column.patrolRecordId}
            dataSource={data.list}
          />
          <div className="text-right mt-5">
            <Pagination
              className="inline-block"
              current={pagination.pageNo}
              pageSize={pagination.pageSize}
              onChange={(page, pageSize) =>
                setPagination({ pageNo: page, pageSize })
              }
              total={data.total}
              showSizeChanger
              showQuickJumper
              showTotal={(total) => `总共 ${total} 条`}
            />
          </div>
        </div>
      )}
      {active == 1 && (
        <div className="main1">
          <div className="tabNum">
            <div className="tabItem">
              <div className="leftIcon">
                <i className="iconfont iconzhexiantu"></i>
              </div>
              <div className="rightInfo">
                <div className="num">{numberInfo.total}</div>
                <div className="title">
                  <span>累计巡课次数</span>
                  <Tooltip placement="bottom" title="提交的巡课记录数量">
                    <i
                      style={{ marginLeft: "4px" }}
                      className="iconfont iconyuanxingwenhao"
                    ></i>
                  </Tooltip>
                </div>
              </div>
            </div>
            <div className="tabItem">
              <div className="centerIcon">
                <i className="iconfont iconyonghu11"></i>
              </div>
              <div className="rightInfo">
                <div className="num">{numberInfo.complete}</div>
                <div className="title">
                  <span>任务完成人数占比(%)</span>
                  <Tooltip placement="bottom" title="完成任务人数/设置任务数的人数">
                    <i
                      style={{ marginLeft: "4px" }}
                      className="iconfont iconyuanxingwenhao"
                    ></i>
                  </Tooltip>
                </div>
              </div>
            </div>
            <div className="tabItem">
              <div className="rightIcon">
                <i className="iconfont iconpingfen"></i>
              </div>
              <div className="rightInfo">
                <div className="num">{numberInfo.avgScore}</div>
                <div className="title">
                  <span>平均得分</span>
                  <Tooltip placement="bottom" title="各巡课记录得分之和/巡课数量">
                    <i
                      style={{ marginLeft: "4px" }}
                      className="iconfont iconyuanxingwenhao"
                    ></i>
                  </Tooltip>
                </div>
              </div>
            </div>
          </div>
          <div className="potrolEcharts">
            <div className="headTitle">
              <span>巡课次数分布</span>
              <Dropdown
                overlay={
                  <Menu onClick={(_) => handleChangeDropdown(_)}>
                    <Menu.Item key="teacher">
                      <span>按教师统计</span>
                    </Menu.Item>
                    <Menu.Item key="grade">
                      <span>按年级统计</span>
                    </Menu.Item>
                    <Menu.Item key="class">
                      <span>按班级统计</span>
                    </Menu.Item>
                    <Menu.Item key="score">
                      <span>按分数统计</span>
                    </Menu.Item>
                  </Menu>
                }
                placement="bottomRight"
              >
                <span style={{ color: "#007aff", cursor: "pointer" }}>
                  {dropdownTitle == 2
                    ? "按教师统计"
                    : dropdownTitle == 0
                      ? "按年级统计"
                      : dropdownTitle == 1
                        ? "按班级统计"
                        : "按分数统计"}
                  <DownOutlined style={{ marginLeft: "4px" }} />
                </span>
              </Dropdown>
            </div>
            {showEacharts && (
              <div className="echarts" id="echarts">
                <ReactECharts
                  className="reactECharts"
                  option={option1}
                  style={{
                    marginLeft: "-130px",
                    width: "120%",
                    height: "400px",
                  }}
                />
              </div>
            )}
            {!showEacharts && <div className="empty">暂无数据</div>}
          </div>
          <div className="potrolPersonList">
            <span>巡课人员列表</span>
            <Table
              className="personList"
              columns={patrolColumns}
              pagination={false}
              dataSource={patrolData.list}
              onChange={onChangeSort}
            />
            <div className="text-right mt-5">
              <Pagination
                className="inline-block"
                current={patrolpagination.pageNo}
                pageSize={patrolpagination.pageSize}
                onChange={(page, pageSize) =>
                  setPatrolPagination({ pageNo: page, pageSize })
                }
                total={patrolData.total}
                showSizeChanger
                showQuickJumper
                showTotal={(total) => `总共 ${total} 条`}
              />
            </div>
          </div>
          <div className="appraisedObjectList">
            <div className="title">
              <span>被评价对象列表</span>
              <Dropdown
                overlay={
                  <Menu onClick={(_) => handleChangeDropdown1(_)}>
                    <Menu.Item key="teacher">
                      <span>教师维度</span>
                    </Menu.Item>
                    <Menu.Item key="grade">
                      <span>年级维度</span>
                    </Menu.Item>
                    <Menu.Item key="class">
                      <span>班级维度</span>
                    </Menu.Item>
                  </Menu>
                }
                placement="bottomRight"
              >
                <span style={{ color: "#007aff", cursor: "pointer" }}>
                  {dropdownTitle1 == 1
                    ? "教师维度"
                    : dropdownTitle1 == 2
                      ? "班级维度"
                      : "年级维度"}
                  <DownOutlined style={{ marginLeft: "4px" }} />
                </span>
              </Dropdown>
            </div>
            <Table
              onChange={onChangeSort1}
              className="personList"
              columns={appraisedColumns}
              pagination={false}
              rowKey={(column) => column.formTemplateInfoId}
              dataSource={appraisedData.list}
            />
            <div className="text-right mt-5">
              <Pagination
                className="inline-block"
                current={appraisedpagination.pageNo}
                pageSize={appraisedpagination.pageSize}
                onChange={(page, pageSize) =>
                  setappraisedPagination({ pageNo: page, pageSize })
                }
                total={appraisedData.total}
                showSizeChanger
                showQuickJumper
                showTotal={(total) => `总共 ${total} 条`}
              />
            </div>
          </div>
        </div>
      )}
      <ImpModal
        isFromAdmin={isFromAdmin}
        onSuccess={() => {
          setPagination({ ...pagination });
        }}
        taskId={id}
        ref={(c) => (impRef.current = c)}
      />
      <FormContextProvider>
        <TourLog
          taskInfo={taskInfo}
          onSuccess={() => {
            setPagination({ ...pagination });
          }}
          ref={(c) => (tourLogRef.current = c)}
        />
      </FormContextProvider>

      <PrevContextProvider>
        <DetailDrawer ref={(c) => (detailDrawerRef.current = c)} />
      </PrevContextProvider>
      <PersonalDrawer ref={(c) => (personDrawerRef.current = c)} />
      <AppraisedDrawer ref={(c) => (appraisedDrawerRef.current = c)} />
    </Root>
  );
}

export default Task;
