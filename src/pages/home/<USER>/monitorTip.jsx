import styled from "styled-components";
const MonitorTipsRoot = styled.div`
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  color: #fff;
  z-index: 19;
  padding: 6px 10px;
  font-size: 14px;
  i,
  span {
    font-size: 14px;
    color: #fff;
  }
  > div {
    width: 0;
  }
  div + div {
    margin-left: 10px;
  }
`;

export function MonitorTips({
  roomName,
  subjectName,
  subjectTeacherName,
  className: classRoomName,
}) {
  return (
    <MonitorTipsRoot>
      {roomName && (
        <div className="text-overflow flex-1">
          <i className="iconfont icon-school"></i>
          {roomName}
        </div>
      )}
      {classRoomName && (
        <div className="text-overflow flex-1">
          <i className="iconfont iconbanji1"></i>
          {classRoomName}
        </div>
      )}
      {subjectTeacherName && (
        <div className="text-overflow flex-1">
          <i className="iconfont iconyonghu11"></i>
          {subjectTeacherName}
        </div>
      )}
      {subjectName && (
        <div className="text-overflow flex-1">
          <i className="iconfont iconwenjian4"></i>
          {subjectName}
        </div>
      )}
    </MonitorTipsRoot>
  );
}
