import React, { useState, useEffect, useCallback, useMemo, useContext } from "react";
import {
  Drawer,
  Button,
  Table,
  Pagination,
  Form,
  Select,
  Input,
  Dropdown,
  Menu,
  Tree,
  Empty,
  Space,
  Spin,
  message,
  Tooltip
} from "antd";
import { CloseOutlined, DownOutlined } from "@ant-design/icons";
import RulesDetail from "./rulesDetail";
import { PrevContext } from "./../prevContext";
import styled from "styled-components";
import { $getTree, getUrlQuery } from "@/tools";
import {
  rulesClassTree,
  rulesRoomTree,
  rulesMonitoringTree,
  rulesPage,
  editRules,
  deleteRules,
  getRules,
} from "@/api";

// 样式组件
const Root = styled.div`
  padding: 16px 24px 24px;
`;

const FormTitle = styled.div`
  position: relative;
  font-weight: 500;
  color: #262626;
  margin-bottom: 24px;
  padding-left: 14px;
  font-size: 16px;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background: #007aff;
  }
`;

// 主组件
const PatrolDrawer = ({ showPatrol, setShowPatrol, businessType, onPatrolChannel }) => {
  const [prevState, prevDispatch] = useContext(PrevContext);
  const [data, setData] = useState({ list: [], total: 0 });
  const [pagination, setPagination] = useState({ pageNo: 1, pageSize: 10 });
  const [loading, setLoading] = useState(false);
  const [addVisible, setAddVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState(null);
  const [lookVisible, setLookVisible] = useState(false);

  const columns = [
    {
      title: "序号",
      width: 60,
      dataIndex: "index",
      key: "index",
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: "名称",
      dataIndex: "name",
      key: "name",
      width: 180,
      ellipsis: {
        showTitle: false,
      },
      render: (_, record) => (
        <Tooltip title={record.name} placement="topLeft">
          <span
            onClick={() => handleLook(record)}
            style={{ color: "#007aff", cursor: "pointer" }}
          >
            {record.name}
          </span>
        </Tooltip>
      ),
    },
    {
      title: "通道数",
      dataIndex: "number",
      key: "number",
    },
    {
      title: "时间间隔",
      key: "time",
      dataIndex: "time",
      render: (_, record) => <span>{record.time + "s"} </span>,
    },
    {
      title: "备注",
      dataIndex: "remark",
      key: "remark",
      width: 305,
      ellipsis: true,
    },
    {
      title: "操作",
      width: 160,
      dataIndex: "control",
      key: "control",
      render: (_, record) => (
        <div>
          <span
            onClick={() => handleOperate("delete", record)}
            className="cursor-pointer text-blue-500"
          >
            删除
          </span>
          <span
            onClick={() => handleOperate("edit", record)}
            className="cursor-pointer text-blue-500 mx-4"
          >
            编辑
          </span>
          <span
            onClick={() => handleOperate("apply", record)}
            className="cursor-pointer text-blue-500"
          >
            应用
          </span>
        </div>
      ),
    },
  ];
  // 新增规则
  const handleAdd = () => {
    setAddVisible(true);
    setCurrentRecord(null);
  };
  // 查看规则
  const handleLook = (record) => {
    setLookVisible(true);
    setCurrentRecord(record);
  };
  // 操作
  const handleOperate = async (type, record) => {
    if (type === "delete") {
      const res = await deleteRules({ id: record.pollingRulesId });
      if (res.code === 0 && res.success) {
        message.success("删除成功");
        getRulesList();
      }
    } else if (type === "edit") {
      setAddVisible(true);
      setCurrentRecord(record);
    } else if (type === "apply") {
      if (!record.pollingRulesId) return;
      const res = await getRules({ pollingRulesId: record.pollingRulesId });
      if (res.code === 0 && res.success) {
        console.log("getRulesInfo", res);
        const patrolChannel = {
          list: res.data?.objects || [],
          selectType: res.data?.selectType,
          time: res.data?.time || 10,
        }
        onPatrolChannel(patrolChannel, prevState, prevDispatch)
        setShowPatrol(false)
      }
    }
  };

  // 获取规则列表
  const getRulesList = async () => {
    setLoading(true);
    const params = {
      currPage: pagination.pageNo,
      pageSize: pagination.pageSize,
      businessType,
    };
    const res = await rulesPage(params);
    if (res.code === 0 && res.success) {
      const { records = [], total = 0 } = res.data;
      setData({ list: records, total: Number(total) });
    }
    setLoading(false);
  };

  useEffect(() => {
    if (showPatrol) {
      getRulesList();
    }
  }, [showPatrol, pagination]);

  // 关闭
  const handleClose = () => {
    setShowPatrol(false);
    setPagination({ pageNo: 1, pageSize: 10 });
  };

  // 新增关闭
  const onAddClose = () => {
    setAddVisible(false);
  };
  // 新增成功
  const onSucess = async (params) => {
    const res = await editRules(params);
    if (res.code === 0 && res.success) {
      message.success("操作成功");
      onAddClose();
      getRulesList();
      setCurrentRecord(null);
    }
  };

  return (
    <Root>
      <Drawer
        title="选择轮巡规则"
        width={1000}
        closable={false}
        visible={showPatrol}
        extra={<CloseButton onClose={handleClose} />}
      >
        <div className="drawer-content">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-medium">规则列表</h3>
            <Button type="primary" onClick={handleAdd}>
              新增规则
            </Button>
          </div>

          <Table
            loading={loading}
            className="mt-5"
            rowKey={(columns) => columns.pollingRulesId}
            dataSource={data.list}
            columns={columns}
            pagination={false}
          />
          <div className="text-right mt-5">
            <Pagination
              className="inline-block"
              current={pagination.pageNo}
              pageSize={pagination.pageSize}
              onChange={(page, pageSize) =>
                setPagination({ ...pagination, pageNo: page, pageSize })
              }
              total={data.total}
              showSizeChanger
              showQuickJumper
              showTotal={(total) => `总共 ${total} 条`}
            />
          </div>
        </div>
      </Drawer>

      <AddRulesDrawer
        visible={addVisible}
        onClose={onAddClose}
        currentRecord={currentRecord}
        setCurrentRecord={setCurrentRecord}
        businessType={businessType}
        onSucess={onSucess}
      />
      <RulesDetail
        lookVisible={lookVisible}
        setLookVisible={setLookVisible}
        id={currentRecord?.pollingRulesId}
      />
    </Root>
  );
};

// 新增规则抽屉组件
const AddRulesDrawer = ({
  visible,
  currentRecord,
  setCurrentRecord,
  businessType,
  onSucess,
  onClose,
}) => {
  const [form] = Form.useForm();
  const [dropdownType, setDropdownType] = useState("1");
  const [selectedKeys, setSelectedKeys] = useState({ 1: [], 2: [], 3: [] });
  const [channels, setChannels] = useState({ 1: [], 2: [], 3: [] });
  const { treeData, loading, fetchTreeData } = useTreeData();
  const [searchValue, setSearchValue] = useState("");

  const bureauId = getUrlQuery("bureauId");
  useEffect(() => {
    if (visible) {
      fetchTreeData(bureauId);
      if (currentRecord) {
        getRulesInfo(currentRecord.pollingRulesId);
      } else {
        form.resetFields();
        setSelectedKeys({ 1: [], 2: [], 3: [] });
        setChannels({ 1: [], 2: [], 3: [] });
        setDropdownType("1");
        setCurrentRecord(null);
      }
    }
  }, [visible, currentRecord]);

  const getRulesInfo = async (id) => {
    if (!id) return;
    const res = await getRules({ pollingRulesId: id });
    if (res.code === 0 && res.success) {
      console.log("getRulesInfo", res);
      const { name, time, remark, selectType, objects } = res.data;
      form.setFieldsValue({
        name,
        time,
        remark,
      });
      // 初始化选中通道
      const type = selectType + "";
      setDropdownType(type);
      const keys = objects.map((i) => i.objectId);
      setSelectedKeys((prev) => ({ ...prev, [type]: keys }));
      setChannels((prev) => ({ ...prev, [type]: objects }));
    }
  };

  const handleCheck = useCallback(
    (type) => (keys, e) => {
      const validData = e.checkedNodes.filter((node) =>
        (["record"].includes(node.type) && node.nodeType === "1") ||
        (["magic", "monitor", "monitoring"].includes(node.type) && node.nodeType === "2")
      );

      const validObj = validData.map((node) => ({
        objectId: node.id,
        objectParent: node.parentId,
        objectType:
          (node.type === "monitoring" || node.type === "monitor")
            ? 1
            : node.type === "record"
              ? 2
              : node.type === "magic"
                ? 3
                : 4,
      }));

      setChannels({ ...channels, [type]: validObj });
      const validKeys = validData.map((node) => node.id);
      setSelectedKeys((prev) => ({ ...prev, [type]: validKeys }));
    },
    [channels]
  );

  const onAddClose = () => {
    form.resetFields();
    setCurrentRecord(null);
    setSelectedKeys({ 1: [], 2: [], 3: [] });
    setChannels({ 1: [], 2: [], 3: [] });
    setDropdownType("1");
    setSearchValue(""); // 重置搜索框
    onClose();
  };

  const handleSubmit = async (values) => {
    try {
      const params = {
        businessType,
        pollingRulesId: currentRecord?.pollingRulesId,
        ...values,
        objects: channels[dropdownType],
        selectType: dropdownType,
      };
      onSucess(params);
      setSearchValue(""); // 重置搜索框
    } catch (error) {
      console.error("提交失败:", error);
    }
  };

  return (
    <Drawer
      title={`${currentRecord ? "编辑" : "新增"}轮巡规则`}
      width={600}
      closable={false}
      visible={visible}
      onClose={onAddClose}
      extra={<CloseButton onClose={onAddClose} />}
      footer={
        <Footer
          count={selectedKeys[dropdownType].length}
          onCancel={onAddClose}
        />
      }
    >
      <Form
        id="patrolRulesForm"
        form={form}
        layout="horizontal"
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        initialValues={{
          time: 10,
        }}
        onFinish={handleSubmit}
      >
        <FormTitle>基本信息</FormTitle>

        <Form.Item
          label="规则名称"
          name="name"
          rules={[{ required: true, message: "请输入规则名称" }]}
        >
          <Input placeholder="请输入" maxLength={50} />
        </Form.Item>

        <Form.Item
          label="轮巡时间间隔"
          name="time"
          rules={[{ required: true, message: "请选择" }]}
        >
          <Select
            options={[
              { value: 10, label: "10s" },
              { value: 15, label: "15s" },
              { value: 30, label: "30s" },
              { value: 60, label: "60s" },
              { value: 120, label: "120s" },
            ]}
          />
        </Form.Item>

        <Form.Item label="备注" name="remark">
          <Input.TextArea rows={5} maxLength={200} placeholder="请输入" />
        </Form.Item>
      </Form>
      <ChannelSelector
        searchValue={searchValue}
        setSearchValue={setSearchValue}
        dropdownType={dropdownType}
        onChangeType={setDropdownType}
        treeData={treeData[dropdownType]}
        checkedKeys={selectedKeys[dropdownType]}
        onCheck={handleCheck(dropdownType)}
        loading={loading}
      />
    </Drawer>
  );
};

// 通道选择器组件
const ChannelSelector = ({
  dropdownType,
  onChangeType,
  treeData,
  checkedKeys,
  onCheck,
  loading,
  searchValue,
  setSearchValue,
}) => {
  const onChangeSearch = (e) => {
    setSearchValue(e.target.value);
  };
  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <FormTitle style={{ margin: 0 }}>轮巡通道</FormTitle>
        <Dropdown
          overlay={
            <Menu onClick={({ key }) => onChangeType(key)}>
              <Menu.Item key="1">按班级选择</Menu.Item>
              <Menu.Item key="2">按教室选择</Menu.Item>
              <Menu.Item key="3">按监控分组选择</Menu.Item>
            </Menu>
          }
        >
          <Button type="link">
            {["按班级选择", "按教室选择", "按监控分组选择"][dropdownType - 1]}
            <DownOutlined />
          </Button>
        </Dropdown>
      </div>
      <div className="mb-5">
        <Input.Search
          value={searchValue}
          placeholder="节点名称"
          style={{ width: 328 }}
          onChange={onChangeSearch}
        />
      </div>
      <TreeSelector
        searchValue={searchValue}
        data={treeData}
        checkedKeys={checkedKeys}
        onCheck={onCheck}
        loading={loading}
      />
    </>
  );
};

// 树选择器组件
const TreeSelector = React.memo(
  ({ data, checkedKeys, onCheck, loading, searchValue }) => {
    const [expandedKeys, setExpandedKeys] = useState([]);

    // 生成高亮标题
    const generateHighlightTitle = (title, searchValue) => {
      const lowerTitle = title.toLowerCase();
      const lowerSearch = searchValue.toLowerCase();
      const index = lowerTitle.indexOf(lowerSearch);

      if (index === -1) return title;

      return (
        <span>
          {title.substr(0, index)}
          <span style={{ color: "#007aff" }}>
            {title.substr(index, searchValue.length)}
          </span>
          {title.substr(index + searchValue.length)}
        </span>
      );
    };

    // 过滤树数据
    const filteredData = useMemo(() => {
      if (!searchValue) return data;
      const filterNode = (nodes) => {
        return nodes
          .map((node) => {
            const isMatched = node.title
              .toLowerCase()
              .includes(searchValue.toLowerCase());

            let children = [];
            if (isMatched) {
              children = node.children || [];
            } else if (node.children) {
              children = filterNode(node.children);
            }
            const newNode = {
              ...node,
              title: generateHighlightTitle(node.title, searchValue),
              children,
            };
            return isMatched || children.length ? newNode : null;
          })
          .filter(Boolean);
      };

      return filterNode(data);
    }, [data, searchValue]);


    // 计算需要展开的节点
    useEffect(() => {
      if (!searchValue) return setExpandedKeys([]);

      const getParentKeys = (nodes) => {
        let keys = new Set();

        const traverse = (nodes, parentKeys = []) => {
          nodes.forEach((node) => {
            if (node.title.toLowerCase().includes(searchValue.toLowerCase())) {
              parentKeys.forEach((k) => keys.add(k));
            }
            if (node.children) {
              traverse(node.children, [...parentKeys, node.key]);
            }
          });
        };

        traverse(nodes);
        return Array.from(keys);
      };

      setExpandedKeys(getParentKeys(data));
    }, [searchValue, data]);

    if (loading) return <Spin tip="加载中..." />;

    return filteredData?.length > 0 ? (
      <Tree
        checkable
        checkedKeys={checkedKeys}
        onCheck={onCheck}
        treeData={filteredData}
        expandedKeys={expandedKeys}
        onExpand={(keys) => setExpandedKeys(keys)}
        autoExpandParent={false}
      />
    ) : (
      <Empty
        style={{ marginTop: 100 }}
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description="暂无数据"
      />
    );
  }
);

// 公用组件
const CloseButton = ({ onClose }) => (
  <CloseOutlined
    onClick={onClose}
    style={{ fontSize: 16, color: "#666", cursor: "pointer" }}
  />
);

const Footer = ({ count, onCancel }) => (
  <div className="flex justify-between items-center px-4">
    <span>
      已选 <span style={{ color: "#1890ff" }}>{count}</span> 个通道
    </span>
    <Space>
      <Button onClick={onCancel}>取消</Button>
      <Button type="primary" htmlType="submit" form="patrolRulesForm">
        提交
      </Button>
    </Space>
  </div>
);

export const useTreeData = () => {
  const [treeData, setTreeData] = useState({ 1: [], 2: [], 3: [] });
  const [loading, setLoading] = useState(false);

  const formatTreeData = (data) => {
    if (!data) return [];
    return $getTree(
      data.map((item) => ({
        ...item,
        title: item.name,
        key: item.id,
      })),
      "0"
    );
  };

  const fetchTreeData = async (bureauId) => {
    try {
      setLoading(true);
      const [classRes, roomRes, monitorRes] = await Promise.all([
        rulesClassTree({ orgId: bureauId }),
        rulesRoomTree({ orgId: bureauId }),
        rulesMonitoringTree({ orgId: bureauId }),
      ]);

      setTreeData({
        1: formatTreeData(classRes?.data),
        2: formatTreeData(roomRes?.data),
        3: formatTreeData(monitorRes?.data),
      });
    } finally {
      setLoading(false);
    }
  };
  return { treeData, loading, fetchTreeData };
};

export default PatrolDrawer;
