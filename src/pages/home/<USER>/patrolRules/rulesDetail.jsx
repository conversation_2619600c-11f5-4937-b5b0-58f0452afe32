import React, { useState, useEffect } from "react";
import { Drawer, Form, Tree, Empty } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import styled from "styled-components";
import { getUrlQuery } from "@/tools";
import { getRules } from "@/api";
import { useTreeData } from './patrolDrawer'


const FormTitle = styled.div`
  position: relative;
  font-weight: 500;
  color: #262626;
  margin-bottom: 24px;
  padding-left: 14px;
  font-size: 16px;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background: #007aff;
  }
`;

export default function RulesDetail({ lookVisible, setLookVisible, id }) {
  const [form] = Form.useForm();
  const [selectedKeys, setSelectedKeys] = useState([]);
  const {treeData, loading, fetchTreeData } = useTreeData()
  const [currentType, setCurrentType] = useState(1)
  const bureauId = getUrlQuery("bureauId");

  const getRulesInfo = async (id) => {
    if (!id) return;
    const res = await getRules({ pollingRulesId: id });
    if (res.code === 0 && res.success) {
      const { name, time, remark, selectType, objects } = res.data;
      form.setFieldsValue({
        name,
        time,
        remark,
      });
      // 初始化选中通道
      const type = selectType;
      setCurrentType(type)
      const keys = objects.map((i) => i.objectId);
      setSelectedKeys((prev) => ({ ...prev, [type]: keys }));
    }
  };
  useEffect(() => {
    if (lookVisible) {
      fetchTreeData(bureauId);
      if (id) {
        getRulesInfo(id);
      }
    }
  }, [id]);

  return (
    <>
      <Drawer
        title="查看轮巡规则"
        width={600}
        closable={false}
        visible={lookVisible}
        onClose={() => setLookVisible(false)}
        extra={
          <CloseOutlined
            onClick={() => setLookVisible(false)}
            style={{ fontSize: 16, color: "#666", cursor: "pointer" }}
          />
        }
      >
        <Form
          id="patrolRulesForm"
          form={form}
          layout="horizontal"
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          initialValues={{
            time: 10,
          }}
        >
          <FormTitle>基本信息</FormTitle>
          <Form.Item label="规则名称">
            {form.getFieldValue("name") || ""}
          </Form.Item>

          <Form.Item label="轮巡时间间隔">
            {form.getFieldValue("time") + "s" || ""}
          </Form.Item>

          <Form.Item label="备注">
            {form.getFieldValue("remark") || ""}
          </Form.Item>

          <FormTitle style={{ margin: 0 }}>轮巡通道</FormTitle>
          <div className="mt-5">
            {treeData[currentType].length > 0 ? (
              <Tree
               checkable 
               loading={loading}
               checkedKeys={selectedKeys[currentType]} 
               treeData={treeData[currentType]}
               defaultExpandAll={true}
               disabled={true}
               />
            ) : (
              <Empty
                style={{ marginTop: 100 }}
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="暂无数据"
              />
            )}
          </div>
        </Form>
      </Drawer>
    </>
  );
}
