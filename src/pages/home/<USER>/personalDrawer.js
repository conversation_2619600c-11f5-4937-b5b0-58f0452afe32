import React, {
  useEffect,
  forwardRef,
  useImperative<PERSON>andle,
  useState,
} from "react";
import moment from "moment";
import styled from "styled-components";
import { CloseOutlined } from "@ant-design/icons";
import { Drawer, Table, Space, Tooltip, Pagination } from "antd";
import { selectRecordByPage } from "@/api";
import { useParams, useSearchParams } from "react-router-dom";
const PersonalDetailDrawer = (props, ref) => {
  const { id } = useParams();
  const columns = [
    {
      title: "序号",
      width: 60,
      align: "center",
      key: "index",
      render: (_, record, index) => `${index + 1}`,
      fixed: "left",
    },
    {
      title: "被记录人",
      dataIndex: "recorded_user_name",
      align: "center",
      key: "recorded_user_name",
      ellipsis: true,
      fixed: "left",
    },
    {
      title: "被记录班级",
      width: 160,
      dataIndex: "class_name",
      align: "center",
      key: "class_name",
      render: (_) => <span>{_ || "--"}</span>,
    },
    {
      title: "被记录年级",
      width: 160,
      dataIndex: "grade_name",
      align: "center",
      key: "grade_name",
      render: (_) => <span>{_ || "--"}</span>,
    },
    {
      title: "巡课时间",
      width: 160,
      dataIndex: "patrol_time",
      align: "center",
      key: "patrol_time",
      render: (_) => moment(_).format("YYYY-MM-DD HH:mm"),
    },
    {
      title: "记录提交时间",
      width: 160,
      dataIndex: "record_time",
      align: "center",
      key: "record_time",
      render: (_) => moment(_).format("YYYY-MM-DD HH:mm"),
    },
    {
      title: "记录人",
      dataIndex: "record_user_name",
      align: "center",
      key: "record_user_name",
      ellipsis: true,
    },
    {
      title: "图片",
      dataIndex: "pic_count",
      align: "center",
      key: "pic_count",
    },
    {
      title: "视频",
      dataIndex: "video_count",
      align: "center",
      key: "video_count",
    },
    {
      title: "量表",
      dataIndex: "use_table",
      align: "center",
      key: "use_table",
      render: (_, record) => `${record.use_table === 2 ? "无" : "有"}`,
    },
    {
      title: "评语",
      width: 300,
      dataIndex: "patrol_comment",
      align: "center",
      key: "patrol_comment",
      ellipsis: {
        showTitle: false,
      },
      render: (patrol_comment) => (
        <Tooltip placement="topLeft" title={patrol_comment}>
          {patrol_comment}
        </Tooltip>
      ),
    },
  ];
  const [show, setShow] = useState(false);
  const [data, setData] = useState({
    list: [],
    total: 0,
  });
  const [rowId, setRowId] = useState(null); // 新增状态来保存 rowId
  const [pagination, setPagination] = useState({
    pageNo: 1,
    pageSize: 10,
  });
  useImperativeHandle(ref, () => ({
    showDrawer(rowId) {
      initDetail(rowId);
      setShow(true);
      setRowId(rowId); // 保存 rowId
    },
  }));
  const initDetail = (rowId) => {
    let params = {
      objectId: rowId,
      pageNo: pagination.pageNo,
      pageSize: pagination.pageSize,
      taskId: id,
      type: 1,
    };
    selectRecordByPage(params).then((res) => {
      console.log("res", res);
      setData({ list: res.data.records, total: res.data.total * 1 });
    });
  };

  const handleCloseDrader = () => {
    setShow(false);
  };
  const handlePaginationChange = (page, pageSize) => {
    console.log("page", page);
    console.log("pageSize", pageSize);
    setPagination({ pageNo: page, pageSize });
  };
  useEffect(() => {
    if (rowId !== null) {
      initDetail(rowId);
    }
  }, [pagination.pageNo, pagination.pageSize, rowId]);
  return (
    <Drawer
      destroyOnClose
      title="查看详情"
      width={1200}
      closable={false}
      visible={show}
      extra={
        <CloseOutlined onClick={handleCloseDrader} className="cursor-pointer" />
      }
    >
      <div style={{ marginBottom: "20px" }}>巡课记录</div>
      <Table
        className="personList"
        columns={columns}
        pagination={false}
        rowKey={(column) => column.patrol_record_id}
        dataSource={data.list}
        scroll={{ x: "max-content" }}
      />
      <div className="text-right mt-5">
        <Pagination
          className="inline-block"
          current={pagination.pageNo}
          pageSize={pagination.pageSize}
          onChange={handlePaginationChange}
          total={data.total}
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `总共 ${total} 条`}
        />
      </div>
    </Drawer>
  );
};
export default forwardRef(PersonalDetailDrawer);
