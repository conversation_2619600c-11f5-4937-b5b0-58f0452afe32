/* 
 观看模式的context
*/
import React, { createContext, useReducer } from "react";

/* 
live - 普通直播
rtsp - 插件模式
*/
const prevState = {
  oWebControl: null,
  mode: "live", // live | rtsp
  vendor: "", // 厂商 阿启视-1000 海康-1001 华三-1002
  isMonitor: false, // 是否选中监控TAB
  isClassRoom: false, //是否选中教室TAB
  // ================
  patrolFreeSetting: null,
  patrolSystemSetting: null,
  // ================
  classRoomInfo: null, // 教室对应的课表相关信息
  isMonitorSwitch: false, // 监控对应的通道详细信息开关
  isPatrol: false, // 是否开启轮巡
  isAdaptiveMode: false, // 新增自适应模式状态
};

export function prevReducer(state, action) {
  switch (action.type) {
    /* 任务巡课 */
    case "change_mode":
      return {
        ...state,
        ...action.payload,
      };
    case "change_is_monitor":
      return {
        ...state,
        ...action.payload,
      };
    case "set_oWebControl":
      return {
        ...state,
        ...action.payload,
      };
    case "change_state":
      return {
        ...state,
        ...action.payload,
      };
    case "change_is_patrol":
      return {
        ...state,
        ...action.payload,
      };
    default:
      break;
  }
}

export const PrevContext = createContext();

export const PrevContextProvider = ({ children }) => {
  const [state, dispatch] = useReducer(prevReducer, prevState);
  return (
    <PrevContext.Provider value={[state, dispatch]}>
      {children}
    </PrevContext.Provider>
  );
};
