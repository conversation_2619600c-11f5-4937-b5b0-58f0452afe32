import { useEffect, useContext } from "react";
import { PrevContext } from "./prevContext";
import { $message } from "@/tools";
import { message } from "antd";
/* 启动阿启视插件 */
let oWebControl = null;
let currentWinCameraid = null;
function InitArgesWebControlPlug() {
  return new Promise((resolve, reject) => {
    oWebControl = new window.ArgesWebControlPlug({
      plugnContainer: "rtsplayer",
      offset: "0 0",
      connSuccess: function connSuccess() {
        resolve();
      },
      connError: function connError(version) {
        reject();
      },
      connClose: function connClose() {
        oWebControl = null;
      },
      winclicked: function (data) {},
      winSelected: function (cameraid, wub) {
        const screenShot = document.querySelector(".screenShot");
        const tape = document.querySelector(".screenShot.tape");
        if (!screenShot) return;
        if (!tape) return;
        screenShot.setAttribute("cameraid", cameraid);
        tape.setAttribute("cameraid", cameraid);
        currentWinCameraid = cameraid;
      },
      winNum: 4,
      cache: false,
      captionBar: false,
    });
  });
}

/* 启动华三插件 */
function InitWebPlugin() {
  return new Promise((resolve, reject) => {
    oWebControl = new window.WebPlugin({
      container: document.getElementById("rtsplayer"),
      successCallback: () => {
        const { ip, username, password } = window.yskjConfig;
        oWebControl
          .JS_RequestInterface({
            method: "system.init",
            data: {
              ip,
              port: 9060,
              username,
              password,
              show_button: 1,
              button_ids: "1",
              show_menu: 1,
              menu_ids: "9",
            },
          })
          .then((res) => {
            if (res.code === 0) {
              oWebControl.JS_RequestInterface({
                method: "window.create",
                data: {
                  layout: "1x1",
                },
              });
              oWebControl.event.on(100001, (data) => {
                if (data.data.channel_id) {
                  $message.emit("dispatchMonitorByCode", data.data.channel_id);
                  // const cameraid = data.data.camera_id;
                  // const screenShot = document.querySelector(".screenShot");
                  // const tape = document.querySelector(".screenShot.tape");
                  // if (!screenShot) return;
                  // if (!tape) return;
                  // screenShot.setAttribute("cameraid", cameraid);
                  // tape.setAttribute("cameraid", cameraid);
                  // currentWinCameraid = cameraid;
                }
              });
              resolve();
            }
          });
      },
    });
  });
}

export default function RtspPlayer({ clearVideo }) {
  const [prevState, prevDispatch] = useContext(PrevContext);
  useEffect(() => {
    function clearPlugin() {
      if (oWebControl) {
        if (prevState.vendor == 1000) {
          oWebControl.JS_Close();
        } else if (prevState.vendor == 1002) {
          oWebControl.destroy();
        }
        oWebControl = null;
        prevDispatch({
          type: "set_oWebControl",
          payload: {
            oWebControl: null,
          },
        });
      }
      const screenShot = document.querySelector(".screenShot");
      const tape = document.querySelector(".screenShot.tape");
      if (!screenShot) return;
      if (!tape) return;
      screenShot.setAttribute("cameraid", "");
      tape.setAttribute("cameraid", "");
    }
    if (prevState.mode === "rtsp" && prevState.isMonitor) {
      // 重置视频
      clearVideo();
      /* 判断用哪种插件 */
      if (prevState.vendor == 1000) {
        InitArgesWebControlPlug().then((res) => {
          prevDispatch({
            type: "set_oWebControl",
            payload: {
              oWebControl,
            },
          });
        });
      }
      if (prevState.vendor == 1002) {
        InitWebPlugin().then((res) => {
          prevDispatch({
            type: "set_oWebControl",
            payload: {
              oWebControl,
            },
          });
        });
      }
    } else {
      clearPlugin();
    }
    return () => {
      clearPlugin();
    };
  }, [prevState.mode, prevState.isMonitor, prevState.vendor]);

  useEffect(() => {
    function onDispatchMonitorResultByCode(val) {
      const monitoringNodeId = val.monitoringNodeId;
      if (!monitoringNodeId) return;

      const screenShot = document.querySelector(".screenShot");
      const tape = document.querySelector(".screenShot.tape");
      if (!screenShot) return;
      if (!tape) return;
      screenShot.setAttribute("cameraid", monitoringNodeId);
      tape.setAttribute("cameraid", monitoringNodeId);
      currentWinCameraid = monitoringNodeId;
    }
    $message.on("dispatchMonitorResultByCode", onDispatchMonitorResultByCode);
    return () => {
      $message.off(
        "dispatchMonitorResultByCode",
        onDispatchMonitorResultByCode
      );
    };
  }, []);

  if (prevState.mode === "live") return null;
  if (!prevState.isMonitor) return null;
  return (
    <div
      id="rtsplayer"
      className="absolute left-0 top-0 right-0 bottom-0 z-20 bg-black"
    ></div>
  );
}
