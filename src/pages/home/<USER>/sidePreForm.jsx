import styled from "styled-components";
import { <PERSON><PERSON>ontext, <PERSON><PERSON><PERSON>, TemSelector } from "@/components";
import { useContext } from "react";
import { Button, Popover, message } from "antd";
import Empty from "@/assets/images/empt.svg";
import { InfoCircleOutlined } from "@ant-design/icons";
import { forminfoSaveData, formsaveAnswer, formGetOneData } from "@/api";
import Icon from "@/assets/images/icon7.svg";
const Root = styled.div`
  border-radius: 4px;
  display: flex;
  flex-direction: column;

  .head {
    border-bottom: 1px solid #e5e5e5;
    i {
      color: #007aff;
      font-size: 20px;
    }
    span {
      font-weight: 700;
      font-size: 14px;
    }
  }
  .body {
    padding: 14px;
    flex: 1;
    overflow-y: auto;
    .title {
      display: flex;
      align-items: center;
      padding: 0 10px;
      font-size: 16px;
      font-weight: bold;
      color: #000000;
      margin-bottom: 20px;
    }
  }
  .footer {
    border-top: 1px sold #e5e5e5;
    .footer-btn {
      padding: 20px;
      display: flex;
    }
    .footer-tips {
      height: 40px;
      background: #e6f6ff;
      display: flex;
      align-items: center;
      padding: 0 20px;
      color: #262626;
      font-size: 14px;
      .icon {
        margin-right: 4px;
        color: #007aff;
        font-size: 14px;
      }
    }
  }
`;

export default function SidePreForm(props) {
  const { currentHeight, isFree, taskInfo } = props;
  const [state, dispatch] = useContext(FormContext);

  async function submit() {
    // 自由巡课
    if (isFree) {
      const res = await forminfoSaveData({
        formInfo: state.tempStr,
        formTemplateInfoId: state.tempId,
        usedObjId: "250",
        usedObjType: 1,
      });
      const result = await formsaveAnswer(res.data.id, state.data);
      if (result && result.code === 0) {
        message.success("保存成功");
        dispatch({
          type: "SAVE",
          payload: {
            formAnswerId: result.data,
            formInfoId: res.data.id,
          },
        });
      }
      return;
    }
    // 任务巡课
    const result = await formsaveAnswer(state.formInfoId, state.data);
    if (result && result.code === 0) {
      message.success("保存成功");
      dispatch({
        type: "SAVE",
        payload: {
          formAnswerId: result.data,
          formInfoId: state.formInfoId,
        },
      });
    }
  }

  const getFormTempInfo = async (tempId) => {
    if (!tempId) return;
    const result = await formGetOneData({ formId: tempId });
    if (!result || !result.data) return;
    const { formInfo, formName } = result.data;

    dispatch({
      type: "INIT",
      payload: {
        editable: true,
        tempId,
        name: formName,
        data: {},
        tempStr: formInfo,
        formInfoId: taskInfo.formInfoId, // 准备答题ID
        formAnswerId: "", // 答题ID
        hasNumberType: formInfo.includes("number"),
      },
    });
  };

  if (!state.tempId && !isFree) {
    getFormTempInfo(taskInfo.formTemplateInfoId);
  }

  function handle2Reset() {
    if (isFree) {
      dispatch({
        type: "RESET",
        payload: {
          formInfoId: "",
        },
      });
      return;
    }
    dispatch({
      type: "RESET",
      payload: {},
    });
  }

  function handle2Edit() {
    if (isFree) {
      dispatch({
        type: "EDIT",
        payload: {
          formInfoId: "",
        },
      });
      return;
    }
    dispatch({
      type: "EDIT",
      payload: {},
    });
  }

  return (
    <Root
      className="w-80 bg-white relative"
      style={{ height: currentHeight + "px" }}
    >
      <div className="head flex justify-between items-center h-12 px-6">
        <span className="bold text-bold">评课量表</span>
        {state.tempId && state.hasNumberType && (
          <span className="flex-1 ml-4" style={{ color: "#8C8C8C" }}>
            总分: {state.total}
          </span>
        )}
        {isFree && (
          <Popover
            overlayClassName="replyContainer"
            getPopupContainer={() => document.getElementById("replyContainer")}
            placement="topRight"
            content={
              <TemSelector
                value={state.tempId}
                isFree
                onChange={({ formInfo, formTemplateInfoId, formName }) => {
                  dispatch({
                    type: "INIT",
                    payload: {
                      editable: true,
                      tempStr: formInfo,
                      tempId: formTemplateInfoId,
                      name: formName,
                      data: {},
                      // 答题相关
                      formInfoId: "", // 准备答题ID
                      formAnswerId: "", // 答题ID
                      hasNumberType: formInfo.includes("number"),
                    },
                  });
                }}
              />
            }
          >
            <Button type="primary" size="small">
              选择量表
            </Button>
          </Popover>
        )}
      </div>
      {!state.tempId ? (
        <div className="absolute left-1/2 transform -translate-x-2/4  top-60 text-center">
          <img src={Empty} alt="" />
          <div style={{ color: "#BFBFBF" }}>请先设置表单</div>
        </div>
      ) : (
        <>
          <div className="body">
            <div className="title">
              <img src={Icon} alt="" />
              <span className="ml-2 flex-1 truncate">{state.name}</span>
              {isFree && (
                <i
                  onClick={() => dispatch({ type: "CANCEL" })}
                  className="iconfont iconshan_chu"
                  style={{ color: "#8C8C8C", cursor: "pointer" }}
                ></i>
              )}
            </div>
            <FormRender />
          </div>
          <div className="footer">
            {state.formAnswerId ? (
              <div>
                <div className="footer-btn">
                  <Button onClick={handle2Reset} danger block>
                    删除
                  </Button>
                  <Button
                    style={{ marginLeft: 8 }}
                    onClick={handle2Edit}
                    type="primary"
                    block
                  >
                    编辑
                  </Button>
                </div>
                <div className="footer-tips">
                  <InfoCircleOutlined className="icon" />
                  <span>评课量表已保存，点击左侧提交按钮提交</span>
                </div>
              </div>
            ) : (
              <Button onClick={submit} type="primary" block>
                保存
              </Button>
            )}
          </div>
        </>
      )}
    </Root>
  );
}
