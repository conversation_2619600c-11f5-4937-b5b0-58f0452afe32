import { <PERSON><PERSON>, <PERSON><PERSON>, Spin, Checkbox } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import speakBg from "@/assets/images/speakBg.png";
import { useMqtt, useAudio } from "@/hooks";
import styled from "styled-components";
import React, { useState } from "react";
import { CloseOutlined } from "@ant-design/icons";
const SpeakModal = ({ showSpeak, setShowSpeak, activeRoomId }) => {
  const mq = useMqtt();
  const [position, setPosition] = useState(() => {
    const initL = document.body.clientWidth / 2 - 200;
    const initT = document.body.clientHeight / 2 - 200;
    return { l: initL, t: initT };
  });
  const {
    connectionState, // 0-未连接, 1-连接中, 2-已连接
    micEnabled,
    startPublish,
    stopPublish,
    toggleMicrophone,
  } = useAudio(activeRoomId, mq.clientId);
  const mouseDown = (e) => {
    console.log('e', e)
    if (
      e.target.className.indexOf &&
      (e.target.className.indexOf("barTip") !== -1)
    ) {
      const elWidth = e.target.clientWidth;
      const elHeight = e.target.clientHeight;
      document.onmousemove = (ev) => {
        let left = ev.clientX - e.nativeEvent.offsetX;
        let top = ev.clientY - e.nativeEvent.offsetY;
        left = Math.max(0, left);
        left = Math.min(left, document.body.clientWidth - elWidth);
        top = Math.max(0, top);
        top = Math.min(top, document.body.clientHeight - elHeight);
        setPosition({
          l: left,
          t: top,
        });
      };
      document.onmouseup = () => {
        document.onmousemove = null;
        document.onmouseup = null;
      };
    }
  };
  const handleCancel = () => {
    stopPublish();
    setShowSpeak(false);
  };
  const onMicChange = (e) => {
    toggleMicrophone(e.target.checked);
  };
  const loadingIcon = (
    <LoadingOutlined
      style={{
        fontSize: 24,
      }}
      spin
    />
  );

  return (
    <Root
      style={{ left: position.l + "px", top: position.t + "px" }}
      className="barTip cursor-move"
    >
      <div className="container barTip" onMouseDown={(e) => mouseDown(e)}>
        <div className="top barTip">
          <div
            onClick={handleCancel}
            data-role="close"
            className="absolute right-4 top-4 cursor-pointer"
          >
            <CloseOutlined
              className="pointer-events-none"
              style={{ color: "#8C8C8C", fontSize: 18 }}
            />
          </div>
        </div>
        <div className="bottom barTip">
          {/* 连接中 */}
          {
            connectionState === 1 && (
              <p>
                <Spin indicator={loadingIcon} />
                <span className="ml-2">喊话通道建立中，请稍等...</span>
              </p>
            )
          }

          {/* 已连接 */}
          {
            connectionState === 2 && (
              <>
                <p className="mt-4">喊话通道已建立，请喊话</p>
                <p className="mb-0">
                  <Checkbox checked={micEnabled} onChange={onMicChange}>
                    开启麦克风
                  </Checkbox>
                </p>
              </>
            )
          }

          {
            connectionState === 0 ? (
              <Button
                type="primary"
                shape="round"
                size="large"
                style={{ width: 160, marginTop: "24px" }}
                onClick={startPublish}
              >
                启动通话
              </Button>
            ) : (
              <Button
                type="primary"
                shape="round"
                size="large"
                style={{ width: 160, marginTop: "24px" }}
                onClick={stopPublish}
              >
                结束通话
              </Button>
            )
          }
        </div>
      </div>



    </Root>
  );
};
const Root = styled.div`
  z-index: 100;
  position: fixed;
  width: 320px;
  border-radius: 6px;
  padding: 0 0 24px 0;
  background: #fff;
  box-shadow: 0px 2px 16px 1px rgba(0, 0, 0, 0.1);
  .top{
    width:320px;
    height: 180px;
    background: url(${speakBg}) no-repeat center center;
  background-size: cover;
  }
  .bottom{
  text-align: center;
  }
`;
export default SpeakModal;
