import React, {
  useState,
  useEffect,
  useContext,
  useMemo,
  useLayoutEffect,
} from "react";
import dayjs from "dayjs";
import styled from "styled-components";
import { useParams } from "react-router-dom";
import {
  formsaveAnswer,
  getMagicLive,
  getPlayUrl,
  getpatrolliveByPri,
  getVendorById,
  getRoomDeviceById,
  getPatrolLiveByFree,
  getTimeTableByRoomId,
  getTimetable,
  getBaseCourseUser,
} from "@/api";
import { Button, message, Modal, Input, Radio, Tree, Empty, Tabs } from "antd";
import moment from "moment";
import { $getTree, getUrlQuery, $message } from "@/tools";
import { FormContext, FormRender } from "@/components";
import { PrevContext } from "./../components/prevContext";
import {
  ClockCircleOutlined,
  CloseOutlined,
  QuestionCircleFilled,
} from "@ant-design/icons";

function Tabar({ onChoose, taskInfo, videoList, currentHeight, setActiveRoomId }) {
  const [prevState, prevDispatch] = useContext(PrevContext);

  const [active, setActive] = useState(0);
  // 场所
  const [classPlace, setClassPlace] = useState([]);
  const [classPlaceNum, setClassPlaceNum] = useState(0);
  // 班级
  const [classGradeNum, setClassGradeNum] = useState(0);
  const [classGradeList, setClassGradeList] = useState([]);
  const [monitor, setMonitor] = useState([]);
  const [device, setDevice] = useState([]);
  const [currentId, setCurrentId] = useState([])

  const monitorNumber = useMemo(() => {
    let num = 0;
    monitor.forEach((item) => {
      if (item.nodeType === "2") {
        num++;
      }
    });
    return num;
  }, [monitor]);
  /* 任务巡课 */
  useEffect(() => {
    if (!taskInfo) return;
    if (!taskInfo.resultMap) return;
    if (taskInfo.resultMap.type1 && Array.isArray(taskInfo.resultMap.type1)) {
      setMonitor(taskInfo.resultMap.type1);
    }
    console.log('taskInfo', taskInfo)
    const thirdEquip = taskInfo.resultMap.type3.map((item) => ({
      ...item,
      monitoringNodeId: item.monitoringRecordId,
      belongs: 30000,
      online: item.online === 1 ? 1 : 0,
    }));
    setDevice([...taskInfo.resultMap.type2, ...thirdEquip]);

    if (taskInfo.resultMap.type4 && Array.isArray(taskInfo.resultMap.type4)) {
      let placeNum = 0;
      const placeList = taskInfo.resultMap.type4
        .map((item) => {
          if (item.nodeType === "ROOM") {
            placeNum++;
          }
          return {
            ...item,
            monitoringNodeId: item.id,
            key: item.id,
            title: item.value,
            parentId: item.parentId,
          };
        })
        .filter((node) => node.nodeType !== "UNIT");
      setClassPlace($getTree(placeList, getUrlQuery("bureauId")));
      setClassPlaceNum(placeNum);
    }
    if (taskInfo.resultMap.type5 && Array.isArray(taskInfo.resultMap.type5)) {
      const treeData = taskInfo.resultMap.type5;
      setClassGradeList(formatTreeData(treeData));
      function getClassNumber(targetArray) {
        let num = 0;
        targetArray.forEach((item) => {
          if (item.parentId !== "0") {
            num++;
          }
        });
        return num;
      }
      setClassGradeNum(getClassNumber(treeData));
    }
  }, [taskInfo]);
  const formatTreeData = (data) => {
    if (!data) return [];
    return $getTree(
      data.map((item) => ({
        ...item,
        title: item.name,
        key: item.id,
      })),
      "0"
    );
  };

  let tabList = [
    {
      label: "任务详情",
      id: 0,
      num: 1,
    },
    {
      label: `教室 ${classPlaceNum}`,
      id: 3,
      num: classPlaceNum,
    },
    {
      label: `班级${classGradeNum}`,
      id: 5,
      num: classGradeNum
    },
    {
      label: `监控 ${monitorNumber}`,
      id: 1,
      num: monitorNumber,
    },
    {
      label: `录播设备 ${device.length}`,
      id: 2,
      num: device.length,
    },
  ];
  async function handleSelect(_, { node }) {
    if (node.nodeType !== "ROOM") return;
    setActiveRoomId(node.id)
    if (currentId[0] === node.id) return
    setCurrentId([node.id])
    const info = await getBaseCourseUser({
      time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      nodeId: node.id,
      type: 3, // 节点类型 1.录播 2.监控 3.教室 4.班级
    });
    if (info.code === 0) {
      prevDispatch({
        type: "change_state",
        payload: {
          classRoomInfo: info.data[0],
        },
      });
    }

    // 获取教室绑定--设备列表
    const result = await getRoomDeviceById({
      orgId: getUrlQuery("bureauId"),
      roomId: node.id,
    });

    /* 普通模式 */
    if (
      prevState.patrolFreeSetting &&
      prevState.patrolFreeSetting.freeMode === 101
    ) {
      if (result.code === 0) {
        let deviceList = result.data.deviceList || [];
        deviceList = deviceList.filter((item) => item.belongs === 20000 || item.belongs === 10000);
        const magicDeviceList = result.data.magicDeviceList || [];
        if (deviceList.length > 0) {
          const { belongs, monitoringNodeId } = deviceList[0];
          getPatrolLiveByFree({
            belong: belongs,
            nodeId: monitoringNodeId,
            bureauId: getUrlQuery("bureauId"),
          }).then((result) => {
            if (result && result.code === 0 && result.data[0]) {
              onChoose({
                url: result.data[0].pcUrl,
                belongs,
                urlList: [],
                monitoringNodeId,
                classRoomId: node.id,
                nodeId: result.data[0].nodeId,
                type: 3,
              });
            }
          });
          return;
        }
        if (magicDeviceList.length > 0) {
          const result = await getMagicLive({ roomId: node.id });
          if (result && result.code === 0 && result.data) {
            let urlList = result.data.liveUrls || [];
            if (result.data.success === 500) {
              urlList = urlList.filter((item) => item.status === 1);
            }
            // urlList = urlList.map((item) => item.flvUrl);
            if (urlList.length === 0) {
              message.error("暂无播放地址");
              return;
            }
            const data = {
              url: urlList[0].flvUrl,
              urlList: urlList.map((item) => item.flvUrl),
              monitoringNodeId: node.id,
              belongs: 40000,
              classRoomId: node.id,
              nodeId: urlList[0].nodeId,
              type: 3,
            };
            onChoose(data);
          }
          return;
        }
        message.info("暂无录播设备或魔盒");
      }
      return;
    }
    /* 教室模式 */
    // if (
    //   prevState.patrolFreeSetting &&
    //   prevState.patrolFreeSetting.freeMode === 102
    // ) {
    //   const result = await getTimeTableByRoomId({
    //     orgId: getUrlQuery("bureauId"),
    //     roomId: node.id,
    //   });
    //   if (result.code === 0) {
    //     prevDispatch({
    //       type: "change_state",
    //       payload: {
    //         classRoomInfo: result.data,
    //       },
    //     });
    //   }
    // }

    if (result.code === 0) {
      const request = [];
      /* 监控和录播设置 */
      const deviceList = result.data.deviceList || [];
      const magicDeviceList = result.data.magicDeviceList || [];

      [...deviceList, ...magicDeviceList].forEach((item) => {
        request.push(getPromiseByBelong(item, node.id));
      });
      let data = await Promise.all(request);
      data = data.filter(Boolean);
      const moheUrls = [];
      data.forEach((item) => {
        if (item.belongs === "10006") {
          if (item.urlList.length > 1) {
            item.urlList.forEach((urlItem, index) => {
              moheUrls.push({
                belongs: 40000,
                monitoringNodeId: item.monitoringNodeId + index,
                url: urlItem.flvUrl,
                urlList: [],
                classRoomId: node.id,
                nodeId: urlItem.nodeId,
                type: 3,
              });
            });
          }
        } else {
          moheUrls.push({ ...item, classRoomId: node.id, type: 3 });
          // moheUrls.push(item);
        }
      });
      data = [...moheUrls];
      // 处理窗口数量相关问题
      const currentWindNum = videoList.length;
      const num = data.length - currentWindNum;
      if (prevState.isAdaptiveMode) {
        const isUseWindList = [];
        const {
          oneFrame,
          twoFrame,
          threeFrame,
          fourFrame,
          sixFrame,
          nineFrame,
          sixteenFrame,
        } = prevState.patrolSystemSetting;
        if (oneFrame === 1) {
          isUseWindList.push(1);
        }
        if (twoFrame === 1) {
          isUseWindList.push(2);
        }
        if (threeFrame === 1) {
          isUseWindList.push(3);
        }
        if (fourFrame === 1) {
          isUseWindList.push(4);
        }
        if (sixFrame === 1) {
          isUseWindList.push(6);
        }
        if (nineFrame === 1) {
          isUseWindList.push(9);
        }
        if (sixteenFrame === 1) {
          isUseWindList.push(16);
        }
        const nearWind = isUseWindList.find((wind) => wind - data.length >= 0);
        const list = [...data];
        new Array(Number(nearWind - list.length)).fill("").forEach((_) => {
          list.push(null);
        });
        onChoose(list);
      } else {
        const list = [...data];
        if (list.length === 0) {
          new Array(Number(currentWindNum) || 1).fill("").forEach((_) => {
            list.push(null);
          });
          onChoose(list);
          return;
        }
        if (num >= 0) {
          const newList = list.slice(0, currentWindNum);
          const result = num === 0 ? list : newList
          onChoose(result);
        } else {
          new Array(Number(currentWindNum - list.length))
            .fill("")
            .forEach((_) => {
              list.push(null);
            });
          onChoose(list);
        }
        // new Array(Number(currentWindNum - list.length))
        //   .fill("")
        //   .forEach((_) => {
        //     list.push(null);
        //   });
        // onChoose(list);
      }
    }
  }

  function getPromiseByBelong(
    { belongs, monitoringNodeId, equipmentTypeInfoId, equipmentInfoId },
    roomId
  ) {
    if (belongs === 20000 || belongs === 10000) {
      return new Promise((resolve, reject) => {
        getPatrolLiveByFree({
          belong: belongs,
          nodeId: monitoringNodeId,
          bureauId: getUrlQuery("bureauId"),
        }).then((result) => {
          if (result && result.code === 0 && result.data[0]) {
            resolve({
              url: result.data[0].pcUrl,
              belongs,
              urlList: [],
              monitoringNodeId,
            });
          } else {
            resolve();
          }
        });
      });
    }
    if (equipmentTypeInfoId === "10006" || equipmentTypeInfoId === "10007") {
      return new Promise((resolve, reject) => {
        getMagicLive({ roomId }).then((result) => {
          if (result && result.code === 0 && result.data) {
            let urlList = result.data.liveUrls || [];
            if (result.data.success === 500) {
              urlList = urlList.filter((item) => item.status === 1);
            }
            // urlList = urlList.map((item) => item.flvUrl);
            if (urlList.length === 0) {
              message.error("暂无播放地址");
              return;
            }
            const data = {
              url: urlList[0].flvUrl,
              urlList,
              monitoringNodeId: equipmentInfoId,
              belongs: equipmentTypeInfoId,
            };
            resolve(data);
          } else {
            resolve();
          }
        });
      });
    }
  }

  function changeTab(tab) {
    if (prevState.patrolFreeSetting?.freeMode === 102) {
      if (Number(tab) === 3 || active === 3) {
        onChoose("clear");
      }
      onChoose("clear");
    }

    prevDispatch({
      type: "change_is_monitor",
      payload: {
        isMonitor: Number(tab) === 1,
        classRoomInfo: null,
        isClassRoom: Number(tab) === 3,
      },
    });
    setActive(Number(tab));
  }
  async function handleClassSelect(_, { node }) {
    // console.log("handleClassSelect", node);
    if (node.type !== 'class') return;
    setActiveRoomId(node.id)
    if (currentId[0] === node.id) return
    setCurrentId([node.id])
    // 获取教室对应的老师\班级\年级信息
    const info = await getBaseCourseUser({
      time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      nodeId: node.id,
      type: 4, // 节点类型 1.录播 2.监控 3.教室 4.班级
    });
    if (info.code === 0) {
      prevDispatch({
        type: "change_state",
        payload: {
          classRoomInfo: info.data[0],
        },
      });
    }
    // 获取教室绑定--设备列表
    const result = await getRoomDeviceById({
      orgId: getUrlQuery("bureauId"),
      roomId: node.roomId,
    });

    /* 普通模式 */
    if (prevState.patrolFreeSetting && prevState.patrolFreeSetting.freeMode === 101) {
      if (result.code === 0) {
        let deviceList = result.data.deviceList || [];
        const magicDeviceList = result.data.magicDeviceList || [];
        if (deviceList.length > 0) {
          const { belongs, monitoringNodeId } = deviceList[0];
          getPatrolLiveByFree({
            belong: belongs,
            nodeId: monitoringNodeId,
            bureauId: getUrlQuery("bureauId"),
          }).then((result) => {
            if (result && result.code === 0 && result.data[0]) {
              onChoose({
                url: result.data[0].pcUrl,
                belongs,
                urlList: [],
                monitoringNodeId,
                classRoomId: node.id,
                type: 4,
                nodeId: result.data[0].nodeId,
              });
            }
          });
          return;
        }
        if (magicDeviceList.length > 0) {
          const result = await getMagicLive({ roomId: node.id });
          if (result && result.code === 0 && result.data) {
            let urlList = result.data.liveUrls || [];
            if (result.data.success === 500) {
              urlList = urlList.filter((item) => item.status === 1);
            }
            // urlList = urlList.map((item) => item.flvUrl);
            if (urlList.length === 0) {
              message.error("暂无播放地址");
              return;
            }
            const data = {
              url: urlList[0].flvUrl,
              urlList: urlList.map((item) => item.flvUrl),
              monitoringNodeId: node.id,
              belongs: 40000,
              classRoomId: node.id,
              type: 4,
              nodeId: urlList[0].nodeId,
            };
            onChoose(data);
          }
          return;
        }
        message.info("暂无录播设备或魔盒");
      }
      return;
    }
    if (result.code === 0) {
      const request = [];
      /* 监控和录播设置 */
      const deviceList = result.data.deviceList || [];
      const magicDeviceList = result.data.magicDeviceList || [];

      [...deviceList, ...magicDeviceList].forEach((item) => {
        request.push(getPromiseByBelong(item, node.id));
      });
      let data = await Promise.all(request);
      data = data.filter(Boolean);

      const moheUrls = [];
      data.forEach((item) => {
        if (item.belongs === "10006") {
          if (item.urlList.length > 1) {
            item.urlList.forEach((urlItem, index) => {
              moheUrls.push({
                belongs: 40000,
                monitoringNodeId: item.monitoringNodeId + index,
                url: urlItem.flvUrl,
                urlList: [],
                classRoomId: node.id,
                type: 4,
                nodeId: urlItem.nodeId,
              });
            });
          }
        } else {
          // moheUrls.push(item);
          moheUrls.push({ ...item, classRoomId: node.id, type: 4 });
        }
      });
      data = [...moheUrls];
      // 处理窗口数量相关问题
      const currentWindNum = videoList.length;
      const num = data.length - currentWindNum;
      if (prevState.isAdaptiveMode) {
        const isUseWindList = [];
        const {
          oneFrame,
          twoFrame,
          threeFrame,
          fourFrame,
          sixFrame,
          nineFrame,
          sixteenFrame,
        } = prevState.patrolSystemSetting;
        if (oneFrame === 1) {
          isUseWindList.push(1);
        }
        if (twoFrame === 1) {
          isUseWindList.push(2);
        }
        if (threeFrame === 1) {
          isUseWindList.push(3);
        }
        if (fourFrame === 1) {
          isUseWindList.push(4);
        }
        if (sixFrame === 1) {
          isUseWindList.push(6);
        }
        if (nineFrame === 1) {
          isUseWindList.push(9);
        }
        if (sixteenFrame === 1) {
          isUseWindList.push(16);
        }
        const nearWind = isUseWindList.find((wind) => wind - data.length >= 0);
        const list = [...data];
        new Array(Number(nearWind - list.length)).fill("").forEach((_) => {
          list.push(null);
        });
        onChoose(list);
      } else {
        const list = [...data];
        if (list.length === 0) {
          new Array(Number(currentWindNum) || 1).fill("").forEach((_) => {
            list.push(null);
          });
          onChoose(list);
          return;
        }
        if (num >= 0) {
          const newList = list.slice(0, currentWindNum);
          const result = num === 0 ? list : newList
          onChoose(result);
        } else {
          new Array(Number(currentWindNum - list.length))
            .fill("")
            .forEach((_) => {
              list.push(null);
            });
          onChoose(list);
        }
        // new Array(Number(currentWindNum - list.length))
        //   .fill("")
        //   .forEach((_) => {
        //     list.push(null);
        //   });
        // onChoose(list);
      }
    }
  }
  return (
    <Root id="tabar" style={{ height: currentHeight + "px" }}>
      <Tabs
        activeKey={active + ''}
        onChange={changeTab}
        destroyInactiveTabPane
        style={{
          paddingLeft: '12px',
        }}
      >
        {
          tabList.map((tab) => (
            tab.num > 0 ? (
              <Tabs.TabPane tab={tab.label} key={tab.id}></Tabs.TabPane>
            ) : (
              ""
            )
          ))
        }
      </Tabs>
      {/* <ul className="tab-head flex list-none">
        {tabList.map((tab) =>
          tab.num > 0 ? (
            <li
              key={tab.id}
              onClick={() => changeTab(tab)}
              className={`${active === tab.id ? "active" : ""}`}
            >
              {tab.label}
            </li>
          ) : (
            ""
          )
        )}
      </ul> */}
      <div className="content overflow-y-auto">
        {active === 0 && <TabDetail taskInfo={taskInfo} />}
        {active === 1 && <TabMonitor onChoose={onChoose} monitor={monitor} setActiveRoomId={setActiveRoomId} />}
        {active === 2 && <TabEquip onChoose={onChoose} device={device} />}

        {active === 3 && classPlace.length > 0 && (
          <Tree
            onSelect={handleSelect}
            defaultExpandAll
            selectedKeys={currentId}
            treeData={classPlace}
          />
        )}
        {active === 3 && classPlace.length === 0 && (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}
        {active === 5 && classGradeList.length > 0 && (
          <Tree
            onSelect={handleClassSelect}
            defaultExpandAll
            selectedKeys={currentId}
            treeData={classGradeList}
          />
        )}

        {active === 5 && classGradeList.length === 0 && (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}
      </div>
    </Root>
  );
}

function TabDetail({ taskInfo }) {
  if (!taskInfo) return null;
  return (
    <>
      <div className="text-base text-black font-bold truncate">
        {taskInfo.taskName}
      </div>
      <div className="mt-8">
        <ClockCircleOutlined />
        <span className="ml-2">
          {moment(taskInfo.startTime).format("YYYY-MM-DD")}
          <span className="mx-1">至</span>
          {moment(taskInfo.endTime).format("YYYY-MM-DD")}
        </span>
      </div>
      <div className="mt-7">
        <div>允许查看监控视频时间段:</div>
        {taskInfo.limitType === 1 && (
          <div className="px-3 leading-8 bg-gray-100 rounded mt-4">无限制</div>
        )}
        {taskInfo.limitType === 2 && (
          <>
            {taskInfo.patrolTaskCustomTimes.map((item, index) => (
              <div
                key={index}
                className="px-3 leading-8 bg-gray-100 rounded mt-4"
              >
                {item.startTime} - {item.endTime}
              </div>
            ))}
            <div className="text-gray-400 mt-6 break-all">
              <i className="iconfont iconcuowu align-middle"></i>
              <span className="align-middle ml-1">
                请巡课老师在规定时间内完成巡课任务并提交评价
              </span>
            </div>
          </>
        )}
      </div>
    </>
  );
}
function TabMonitor({ monitor, onChoose, setActiveRoomId }) {
  const [prevState, prevDispatch] = useContext(PrevContext);
  const [mode, setModel] = useState("tree");
  const [filterValue, setFilterValue] = useState("");
  const [currentId, setCurrentId] = useState([])

  const filteredMonitor = useMemo(() => {
    if (!filterValue) return monitor;
    return monitor.filter(
      (item) => item.name.indexOf(filterValue) !== -1 && item.nodeType === "2"
    );
  }, [filterValue, monitor]);

  const monitorTree = useMemo(() => {
    const monitorList = monitor.map((item) => ({
      ...item,
      key: item.monitoringNodeId,
      title: item.name,
      parentId: item.parentNodeId,
      id: item.monitoringNodeId,
    }));
    return $getTree(monitorList, "0");
  }, [monitor]);

  async function onChooseNode(item) {
    // 获取教室对应的老师\班级\年级信息
    setActiveRoomId(item.monitoringNodeId)
    if (currentId[0] === item.monitoringNodeId) return
    setCurrentId([item.monitoringNodeId])

    const info = await getBaseCourseUser({
      time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      nodeId: item.monitoringNodeId,
      type: 2, // 节点类型 1.录播 2.监控 3.教室 4.班级
    });
    if (info.code === 0) {
      prevDispatch({
        type: "change_state",
        payload: {
          classRoomInfo: info.data[0],
        },
      });
    }
    if (prevState.vendor == 1002) {
      const res = await getVendorById({ nodeId: item.monitoringNodeId });
      if (res.data !== "1002") {
        message.error("请选择正常的厂商节点");
        return;
      }
      if (prevState.oWebControl) {
        prevState.oWebControl.JS_RequestInterface({
          method: "play.preview",
          data: {
            channel_id: item.monitoringNodeCode,
          },
        });
      }
      return;
    }
    if (prevState.vendor == 1000) {
      const res = await getVendorById({ nodeId: item.monitoringNodeId });
      if (res.data !== "1000") {
        message.error("请选择正常的厂商节点");
        return;
      }
      const result = await getpatrolliveByPri({
        nodeId: item.monitoringNodeId,
      });
      if (result && result.code === 0 && result.data[0]) {
        if (prevState.oWebControl) {
          prevState.oWebControl.JS_Video(
            result.data[0].pcUrl,
            item.monitoringNodeId,
            item.name
          );
        } else {
          message.error("插件启动失败，请检查插件是否安装！");
        }
      }
      return;
    }

    const result = await getPlayUrl({
      belong: item.belongs,
      nodeId: item.monitoringNodeId,
    });
    let monitorBindClassInfo = null;
    const monitorBindClassInfoRes = await getTimetable({
      nodeId: item.monitoringNodeId,
    });
    if (monitorBindClassInfoRes.code === 0) {
      monitorBindClassInfo = monitorBindClassInfoRes.data;
    }

    if (result && result.code === 0 && result.data[0]) {
      const { webrtc, flvUrl, pcUrl } = result.data[0];
      let url = webrtc ? webrtc : flvUrl ? flvUrl : pcUrl;
      if (!url) {
        message.error("暂无播放地址");
        return;
      }
      const recordUrl = webrtc ? flvUrl : url;
      onChoose({
        url,
        recordUrl,
        monitoringNodeId: item.monitoringNodeId,
        belongs: item.belongs,
        urlList: [],
        monitorBindClassInfo,
        classRoomId: item.monitoringNodeId,
        type: 2,
      });
    }
  }
  function onSelect(selectedKeys, e) {
    const { selectedNodes } = e;
    const target = selectedNodes[0];
    if (target.nodeType === "2") {
      onChooseNode(target);
    } else {
      message.error("请选择监控节点");
    }
  }
  useEffect(() => {
    function findNodeById(val) {
      const target = monitor.find((item) => item.monitoringNodeCode === val);
      if (target) {
        $message.emit("dispatchMonitorResultByCode", target);
      }
    }
    $message.on("dispatchMonitorByCode", findNodeById);
    return () => {
      $message.off("dispatchMonitorByCode", findNodeById);
    };
  }, [monitor]);
  return (
    <div>
      <div className="mb-5">
        <Input.Search
          placeholder="请输入关键字搜索"
          onSearch={(value) => {
            setFilterValue(value);
            setModel(value ? "list" : "tree");
          }}
        />
      </div>
      {mode === "tree" && monitorTree.length > 0 && (
        <Tree
          defaultExpandAll
          onSelect={onSelect}
          selectedKeys={currentId}
          treeData={monitorTree}
          titleRender={(item) => (
            <div>
              {item.title}
              {item.children?.length > 0 && (
                <span style={{ marginLeft: 6 }}>({item.children.length})</span>
              )}
            </div>
          )}
        />
      )}
      {mode === "list" &&
        filteredMonitor.map((item) => {
          return (
            <EquipItem
              onClick={() => onChooseNode(item)}
              key={item.monitoringNodeId}
              className="truncate relative cursor-pointer"
            >
              {item.name}
            </EquipItem>
          );
        })}
      {mode === "list" && filteredMonitor.length === 0 && (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
      {mode === "tree" && monitorTree.length === 0 && (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
    </div>
  );
}
const EquipItem = styled.div`
  &:hover {
    color: #007aff;
  }
  & + & {
    margin-top: 20px;
  }
`;
function TabEquip({ device, onChoose }) {
  const [prevState, prevDispatch] = useContext(PrevContext);
  const [filter, setFilter] = useState({
    value: "",
    online: -1,
  });

  const filteredDevice = useMemo(() => {
    if (filter.value === "" && filter.online === -1) return device;
    return device.filter((item) => {
      return (
        item.name.indexOf(filter.value) !== -1 && item.online === filter.online
      );
    });
  }, [filter, device]);

  async function onChooseNode(item) {
    // 获取教室对应的老师\班级\年级信息
    const info = await getBaseCourseUser({
      time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      nodeId: item.monitoringNodeId,
      type: 1, // 节点类型 1.录播 2.监控 3.教室 4.班级
    });
    if (info.code === 0 && info.data) {
      prevDispatch({
        type: "change_state",
        payload: {
          classRoomInfo: info.data[0],
        },
      });
    }
    const result = await getPlayUrl({
      belong: item.belongs,
      nodeId: item.monitoringNodeId,
    });

    if (result && result.code === 0 && result.data) {
      let data = result.data;
      if (Array.isArray(data)) {
        data = data[0];
      }
      const { flvUrl, pcUrl } = data;
      let url = flvUrl ? flvUrl : pcUrl;
      if (!url) {
        message.error("暂无播放地址");
        return;
      }
      onChoose({
        url,
        recordUrl: url,
        monitoringNodeId: item.monitoringNodeId,
        belongs: item.belongs,
        urlList: [],
        classRoomId: item.monitoringNodeId,
        type: 1,
      });
    }
  }

  return (
    <>
      <div className="mb-5">
        <Input.Search
          className="mb-3"
          placeholder="请输入关键字搜索"
          onSearch={(value) => {
            setFilter((pre) => ({ ...pre, value }));
          }}
        />
        <div className="flex line-tab">
          <div
            onClick={() => {
              setFilter((pre) => ({ ...pre, online: -1 }));
            }}
            className={`flex-1 rounded bg-gray-100 h-8 leading-8 text-center cursor-pointer ${filter.online === -1 ? "active" : ""
              }`}
          >
            全部
          </div>
          <div
            onClick={() => {
              setFilter((pre) => ({ ...pre, online: 1 }));
            }}
            className={`mx-1 flex-1 rounded bg-gray-100 h-8 leading-8 text-center cursor-pointer ${filter.online === 1 ? "active" : ""
              }`}
          >
            在线
          </div>
          <div
            onClick={() => {
              setFilter((pre) => ({ ...pre, online: 0 }));
            }}
            className={`flex-1 rounded bg-gray-100 h-8 leading-8 text-center cursor-pointer ${filter.online === 0 ? "active" : ""
              }`}
          >
            离线
          </div>
        </div>
      </div>
      {filteredDevice.map((item) => (
        <EquipItem
          onClick={() => onChooseNode(item)}
          key={item.monitoringNodeId}
          className="flex items-center relative cursor-pointer"
        >
          <div
            style={{
              width: 6,
              height: 6,
              top: -1,
              background: item.online === 1 ? "#17BE6B" : "#D9D9D9",
            }}
            className="rounded-full relative"
          ></div>
          <div className="truncate flex-1 ml-2">{item.name}</div>
        </EquipItem>
      ))}
      {filteredDevice.length === 0 && (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
    </>
  );
}

const Root = styled.div`
  text-align: initial;
  display: inline-block;
  position: relative;
  overflow: auto;
  width: 320px;
  background: #fff;
  border-radius: 4px;
  .tab-head {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding-left: 8px;
    padding-right: 8px;
    margin: 0;
    li {
      cursor: pointer;
      color: #262626;
      padding: 16px 0;
      flex: 1;
      text-align: center;
      border-bottom: 2px solid transparent;
    }
    li.active {
      border-bottom: 2px solid #007aff;
    }
  }
  .content {
    height: calc(100% - 57px);
    padding: 24px;
  }
  .line-tab {
    div {
      &.active,
      &:hover {
        color: #fff;
        background: #007aff;
      }
    }
  }
`;
export default Tabar;
