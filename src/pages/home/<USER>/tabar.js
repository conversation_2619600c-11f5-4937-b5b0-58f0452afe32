import React, { useState, useMemo, useContext, useEffect } from "react";
import { Input, Empty, message } from "antd";
import { DownOutlined } from "@ant-design/icons";
import { useTeacherRoom, useTeacherRoomDevice } from "@/hooks";
import { Clickoutside } from "@/components";
import styled from "styled-components";
import {
  getpatrolliveByPri,
  getVendorById,
  getTimetable,
  getPlayUrl,
  getMagicLive,
} from "@/api";
import { PrevContext } from "./../components/prevContext";
import { $message } from "@/tools";

const Root = styled.div`
  text-align: initial;
  display: inline-block;
  position: relative;
  overflow: hidden;
  width: 320px;
  background: #fff;
  border-radius: 4px;
  .head {
    position: relative;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    border-bottom: 1px solid #e5e5e5;
    span {
      font-size: 14px;
      font-weight: bold;
    }
    .room-list {
      height: 208px;
      overflow: auto;
      padding: 4px 0;
      top: 50px;
      left: 0;
      position: absolute;
      width: 100%;
      z-index: 99;
      background: #fff;
      box-shadow: 0px 6px 12px 0px rgba(51, 51, 51, 0.16);
      border-radius: 4px 4px 4px 4px;
      .room {
        cursor: pointer;
        height: 40px;
        line-height: 40px;
        padding: 0 12px;
        font-size: 14px;
        color: #262626;
        &:hover {
          background: #f5f5f5;
        }
        &.active {
          background: #e6f2ff;
        }
      }
    }
  }
  .container {
    height: calc(100% - 48px);
    overflow: auto;
    /* flex: 1; */
  }
  .filter {
    padding: 20px 24px;
  }
  .device-item {
    cursor: pointer;
    font-size: 14px;
    height: 48px;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .device-item:hover {
    background: #f7f7f7;
  }
`;

function Tabar({ onChoose, currentHeight, videoList }) {
  const [prevState, prevDispatch] = useContext(PrevContext);
  const [show, setShow] = useState(false);
  const [search, setSearch] = useState();
  const rooms = useTeacherRoom();
  const [currentRoom, setCurrentRoom] = useState();
  const list = useTeacherRoomDevice(currentRoom?.classRoomId);
  const filteredList = useMemo(() => {
    if (!search) return list;
    return list.filter((item) => item.name.indexOf(search) !== -1);
  }, [search, list]);
  /* 初始化 */
  if (!currentRoom && rooms[0]) {
    setCurrentRoom(rooms[0]);
  }
  async function onChooseNode(item) {
    /* 华三插件 */
    if (prevState.vendor == 1002) {
      const res = await getVendorById({ nodeId: item.monitoringNodeId });
      if (res.data !== "1002") {
        message.error("请选择正常的厂商节点");
        return;
      }
      if (prevState.oWebControl) {
        prevState.oWebControl.JS_RequestInterface({
          method: "play.preview",
          data: {
            channel_id: item.monitoringNodeCode,
          },
        });
      }
      return;
    }
    if (prevState.vendor == 1000) {
      const res = await getVendorById({ nodeId: item.monitoringNodeId });
      if (res.data !== "1000") {
        message.error("请选择正常的厂商节点");
        return;
      }
      const result = await getpatrolliveByPri({
        nodeId: item.monitoringNodeId,
      });
      if (result && result.code === 0 && result.data[0]) {
        if (prevState.oWebControl) {
          prevState.oWebControl.JS_Video(
            result.data[0].pcUrl,
            item.monitoringNodeId,
            item.name
          );
        } else {
          message.error("插件启动失败，请检查插件是否安装！");
        }
      }
      return;
    }
    let monitorBindClassInfo = null;
    const monitorBindClassInfoRes = await getTimetable({
      nodeId: item.monitoringNodeId,
    });
    if (monitorBindClassInfoRes.code === 0) {
      monitorBindClassInfo = monitorBindClassInfoRes.data;
    }

    // 魔盒
    if (item.belongs === 40000) {
      getMagicLive({ roomId: item.monitoringNodeId }).then((result) => {
        if (result && result.code === 0 && result.data) {
          let urlList = result.data.liveUrls || [];
          if (result.data.success === 500) {
            urlList = urlList.filter((item) => item.status === 1);
          }
          // urlList = urlList.map((item) => item.flvUrl);
          if (urlList.length === 0) {
            message.error("暂无播放地址");
            return;
          }
          const data = [];
          urlList.forEach((urlItem, index) => {
            data.push({
              url: urlItem.flvUrl,
              recordUrl: urlItem.flvUrl,
              nodeId: urlItem.nodeId,
              monitoringNodeId: item.monitoringNodeId + index,
              belongs: item.belongs,
              urlList: [],
              monitorBindClassInfo,
            });
          });

          const currentWindNum = videoList.length;
          const num = data.length - currentWindNum;

          if (prevState.isAdaptiveMode) {
            const isUseWindList = [];
            const {
              oneFrame,
              twoFrame,
              threeFrame,
              fourFrame,
              sixFrame,
              nineFrame,
              sixteenFrame,
            } = prevState.patrolSystemSetting;
            if (oneFrame === 1) {
              isUseWindList.push(1);
            }
            if (twoFrame === 1) {
              isUseWindList.push(2);
            }
            if (threeFrame === 1) {
              isUseWindList.push(3);
            }
            if (fourFrame === 1) {
              isUseWindList.push(4);
            }
            if (sixFrame === 1) {
              isUseWindList.push(6);
            }
            if (nineFrame === 1) {
              isUseWindList.push(9);
            }
            if (sixteenFrame === 1) {
              isUseWindList.push(16);
            }
            const nearWind = isUseWindList.find(
              (wind) => wind - data.length >= 0
            );
            const list = [...data];
            new Array(Number(nearWind - list.length)).fill("").forEach((_) => {
              list.push(null);
            });
            onChoose(list);
          } else {
            const list = [...data];
            const num = data.length - currentWindNum;
            if(num <= 0){
              onChoose(list);
            }else{
              onChoose(list.splice(0, list.length - 1))
            }
            // new Array(Number(currentWindNum - list.length))
            //   .fill("")
            //   .forEach((_) => {
            //     list.push(null);
            //   });
            // console.log(list);
            // onChoose(list);
          }
        }
      });
      return;
    }

    const result = await getPlayUrl({
      belong: item.belongs,
      nodeId: item.monitoringNodeId,
    });

    if (result && result.code === 0 && result.data[0]) {
      const { webrtc, flvUrl, pcUrl } = result.data[0];
      let url = webrtc ? webrtc : flvUrl ? flvUrl : pcUrl;
      if (!url) {
        message.error("暂无播放地址");
        return;
      }
      const recordUrl = webrtc ? flvUrl : url;
      onChoose({
        url,
        recordUrl,
        monitoringNodeId: item.monitoringNodeId,
        belongs: item.belongs,
        urlList: [],
        monitorBindClassInfo,
      });
    }
  }
  const filteredNodes = filteredList.map((item) => (
    <div
      onClick={() => onChooseNode(item)}
      className="device-item"
      key={item.monitoringNodeId}
    >
      <span className="flex-1 pr-4 truncate">{item.name}</span>
      {item.online !== null && (
        <span
          style={{
            color: item.online === 1 ? "#17BE6B" : "#BFBFBF",
          }}
        >
          {item.online === 1 ? "在线" : "离线"}
        </span>
      )}
    </div>
  ));
  const roomListNodes = (
    <div className="room-list">
      {rooms.map((item) => (
        <div
          className={`room ${
            item.classRoomId === currentRoom?.classRoomId ? "active" : ""
          }`}
          onClick={(e) => {
            setCurrentRoom(item);
            setShow(false);
            e.stopPropagation();
          }}
          key={item.classRoomId}
        >
          {item.fullName}
        </div>
      ))}
    </div>
  );
  useEffect(() => {
    prevDispatch({
      type: "change_is_monitor",
      payload: { isMonitor: true },
    });
  }, []);
  useEffect(() => {
    function findNodeById(val) {
      const target = list.find((item) => item.monitoringNodeCode === val);
      if (target) {
        $message.emit("dispatchMonitorResultByCode", target);
      }
    }
    $message.on("dispatchMonitorByCode", findNodeById);
    return () => {
      $message.off("dispatchMonitorByCode", findNodeById);
    };
  }, [list]);
  return (
    <Root id="tabar" style={{ height: currentHeight + "px" }}>
      <Clickoutside
        cb={() => {
          setShow(false);
        }}
      >
        <div
          onClick={() => {
            rooms.length > 1 && setShow(true);
          }}
          className={`head ${rooms.length > 1 && "cursor-pointer"}`}
        >
          <span className="flex-1 pr-4 truncate">{currentRoom?.fullName}</span>
          {rooms.length > 1 && <DownOutlined />}
          {show && roomListNodes}
        </div>
      </Clickoutside>
      <div className="container  relative">
        <div className="filter">
          <Input.Search
            onSearch={(value) => {
              setSearch(value);
            }}
            placeholder="请输入关键字搜索"
          />
        </div>
        <div className="list">
          {filteredNodes.length > 0 ? (
            filteredNodes
          ) : (
            <Empty
              className="absolute left-1/2 top-1/3 transform -translate-x-1/2 -translate-y-1/2"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          )}
        </div>
      </div>
    </Root>
  );
}

export default Tabar;
