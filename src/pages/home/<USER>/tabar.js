import React, { useState, useEffect, useContext, useMemo } from "react";
import styled from "styled-components";
import dayjs from "dayjs";
import {
  getFreeUserPassageway,
  formsaveAnswer,
  forminfoSaveData,
  selectLivingList,
  getAllOrgById,
  getMagicClassRoom,
  getRoomDeviceById,
  getPatrolClassRoom,
  getMagicLive,
  getPatrolLiveByFree,
  getPatrolLiveByFreePri,
  getVendorById,
  getTimeTableByRoomId,
  getTimetable,
  getBaseCourseUser,
  getPatrolClassInfo
} from "@/api";
import { Button, message, Modal, Input, Radio, Tree, Empty } from "antd";
import { FormContext, FormRender, Clickoutside } from "@/components";
import { PrevContext } from "./../components/prevContext";
import { CloseOutlined, QuestionCircleFilled } from "@ant-design/icons";
import { data2Tree, $getTree, $message } from "@/tools";

function Tabar({ onChoose, videoList, currentHeight, setActiveRoomId }) {

  const [prevState, prevDispatch] = useContext(PrevContext);
  const [currentOrg, setCurrentOrg] = useState(() => {
    const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
    return {
      name: userInfo?.orgName,
      id: userInfo?.orgId,
      ISJD: Number(userInfo?.unitAttr) < 8,
    };
  });

  const [active, setActive] = useState();
  if (!active && prevState.patrolFreeSetting) {
    if (prevState.patrolFreeSetting.roomSwitch) {
      setActive(4);
    } else if (prevState.patrolFreeSetting.classSwitch) {
      setActive(5);
    } else if (prevState.patrolFreeSetting.monitorSwitch) {
      setActive(1);
    } else if (prevState.patrolFreeSetting.equipmentSwitch) {
      setActive(2);
    } else if (prevState.patrolFreeSetting.livingSwitch) {
      setActive(3);
    }
  }

  // 场所
  const [classPlace, setClassPlace] = useState([]);
  const [classPlaceNum, setClassPlaceNum] = useState(0);
  // 班级
  const [classGradeNum, setClassGradeNum] = useState(0);
  const [classGradeList, setClassGradeList] = useState([]);

  const [monitor, setMonitor] = useState([]);
  const [device, setDevice] = useState([]);
  const [currentId, setCurrentId] = useState([])
  // const [live, setLive] = useState([]);

  /* 自由巡课 */
  const monitorNumber = useMemo(() => {
    let num = 0;
    monitor.forEach((item) => {
      if (item.nodeType === "2") {
        num++;
      }
    });
    return num;
  }, [monitor]);
  useEffect(() => {
    getFreeUserPassageway({ bureauId: currentOrg.id }).then((result) => {
      if (result.data.type1 && Array.isArray(result.data.type1)) {
        setMonitor(result.data.type1);
      }
      // setDevice(result.data.type2 || []);
      const type3 = result.data.type3.map((item) => {
        if (item.online !== 1) {
          item.online = 0;
        }
        return item;
      });
      setDevice([...result.data.type2, ...type3]);
    });
    // selectLivingList({
    //   pageNo: 1,
    //   pageSize: 999,
    //   bureauId: currentOrg.id,
    // }).then((result) => {
    //   if (result.code === 0) {
    //     setLive(result.data);
    //   }
    // });
    getPatrolClassRoom({ orgId: currentOrg.id }).then((res) => {
      if (res.code === 0 && res.data && Array.isArray(res.data)) {
        function getClassRoomNumber(targetArray) {
          let num = 0;
          targetArray.forEach((item) => {
            if (item.children && item.children.length > 0) {
              num += getClassRoomNumber(item.children);
            }
            if (item.classroom) {
              num++;
            }
          });
          return num;
        }
        setClassPlace(res.data);
        setClassPlaceNum(getClassRoomNumber(res.data));
      } else {
        setClassPlace([]);
        setClassPlaceNum(0);
      }
    });
    // 获取班级信息
    getPatrolClassInfo({ orgId: currentOrg.id }).then((res) => {
      if (res.code === 0 && res.data && Array.isArray(res.data)) {
        const treeData = res.data;
        setClassGradeList(formatTreeData(treeData));
        function getClassNumber(targetArray) {
          let num = 0;
          targetArray.forEach((item) => {
            if (item.parentId !== "0") {
              num++;
            }
          });
          return num;
        }
        setClassGradeNum(getClassNumber(treeData));
      }
    });

  }, [currentOrg.id]);

  const formatTreeData = (data) => {
    if (!data) return [];
    return $getTree(
      data.map((item) => ({
        ...item,
        title: item.name,
        key: item.id,
      })),
      "0"
    );
  };

  let tabList = [
    {
      label: `教室 ${classPlaceNum}`,
      id: 4,
      show: prevState.patrolFreeSetting?.roomSwitch,
    },
    {
      label: `班级${classGradeNum}`,
      id: 5,
      show: prevState.patrolFreeSetting?.classSwitch
    },
    {
      label: `监控 ${monitorNumber}`,
      id: 1,
      show: prevState.patrolFreeSetting?.monitorSwitch,
    },
    // {
    //   label: `录播设备 ${device.length}`,
    //   id: 2,
    //   show: prevState.patrolFreeSetting?.equipmentSwitch,
    // },
    // {
    //   label: `直播 ${live.length}`,
    //   id: 3,
    //   show: prevState.patrolFreeSetting?.livingSwitch,
    // },
  ];

  async function handleSelect(_, { node }) {
    if (!node.classroom) return;
    setActiveRoomId(node.id);
    if (currentId[0] === node.id) return
    setCurrentId([node.id])
    // 获取教室对应的老师\班级\年级信息
    const info = await getBaseCourseUser({
      time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      nodeId: node.id,
      type: 3, // 节点类型 1.录播 2.监控 3.教室 4.班级
    });
    if (info.code === 0) {
      prevDispatch({
        type: "change_state",
        payload: {
          classRoomInfo: info.data[0],
        },
      });
    }

    // console.log("info", info);
    // 获取教室绑定--设备列表
    const result = await getRoomDeviceById({
      orgId: currentOrg.id,
      roomId: node.id,
    });

    /* 普通模式 */
    if (
      prevState.patrolFreeSetting &&
      prevState.patrolFreeSetting.freeMode === 101
    ) {
      if (result.code === 0) {
        let deviceList = result.data.deviceList || [];
        deviceList = deviceList.filter((item) => item.belongs === 20000 || item.belongs === 10000);
        const magicDeviceList = result.data.magicDeviceList || [];
        if (deviceList.length > 0) {
          const { belongs, monitoringNodeId } = deviceList[0];
          getPatrolLiveByFree({
            belong: belongs,
            nodeId: monitoringNodeId,
            bureauId: currentOrg.id,
          }).then((result) => {
            if (result && result.code === 0 && result.data[0]) {
              onChoose({
                url: result.data[0].pcUrl,
                belongs,
                urlList: [],
                monitoringNodeId,
                classRoomId: node.id,
                type: 3,
                nodeId: result.data[0].nodeId,
              });
            }
          });
          return;
        }
        if (magicDeviceList.length > 0) {
          const result = await getMagicLive({ roomId: node.id });
          if (result && result.code === 0 && result.data) {
            let urlList = result.data.liveUrls || [];
            if (result.data.success === 500) {
              urlList = urlList.filter((item) => item.status === 1);
            }
            // urlList = urlList.map((item) => item.flvUrl);
            if (urlList.length === 0) {
              message.error("暂无播放地址");
              return;
            }
            const data = {
              url: urlList[0].flvUrl,
              urlList: urlList.map((item) => item.flvUrl),
              monitoringNodeId: node.id,
              belongs: 40000,
              classRoomId: node.id,
              type: 3,
              nodeId: urlList[0].nodeId,
            };
            onChoose(data);
          }
          return;
        }
        message.info("暂无录播设备或魔盒");
      }
      return;
    }
    /* 教室模式 */
    // if (
    //   prevState.patrolFreeSetting &&
    //   prevState.patrolFreeSetting.freeMode === 102
    // ) {
    //   const result = await getTimeTableByRoomId({
    //     orgId: currentOrg.id,
    //     roomId: node.id,
    //   });
    //   if (result.code === 0) {
    //     prevDispatch({
    //       type: "change_state",
    //       payload: {
    //         classRoomInfo: result.data,
    //       },
    //     });
    //   }
    // }

    if (result.code === 0) {
      const request = [];
      /* 监控和录播设置 */
      const deviceList = result.data.deviceList || [];
      const magicDeviceList = result.data.magicDeviceList || [];

      [...deviceList, ...magicDeviceList].forEach((item) => {
        request.push(getPromiseByBelong(item, node.id));
      });
      let data = await Promise.all(request);
      data = data.filter(Boolean);

      const moheUrls = [];
      data.forEach((item) => {
        if (item.belongs === "10006") {
          if (item.urlList.length > 1) {
            item.urlList.forEach((urlItem, index) => {
              moheUrls.push({
                belongs: 40000,
                monitoringNodeId: item.monitoringNodeId + index,
                url: urlItem.flvUrl,
                urlList: [],
                classRoomId: node.id,
                type: 3,
                nodeId: urlItem.nodeId,
              });
            });
          }
        } else {
          // moheUrls.push(item);
          moheUrls.push({ ...item, classRoomId: node.id, type: 3 });
        }
      });
      data = [...moheUrls];
      // 处理窗口数量相关问题
      const currentWindNum = videoList.length;
      const num = data.length - currentWindNum;
      if (prevState.isAdaptiveMode) {
        const isUseWindList = [];
        const {
          oneFrame,
          twoFrame,
          threeFrame,
          fourFrame,
          sixFrame,
          nineFrame,
          sixteenFrame,
        } = prevState.patrolSystemSetting;
        if (oneFrame === 1) {
          isUseWindList.push(1);
        }
        if (twoFrame === 1) {
          isUseWindList.push(2);
        }
        if (threeFrame === 1) {
          isUseWindList.push(3);
        }
        if (fourFrame === 1) {
          isUseWindList.push(4);
        }
        if (sixFrame === 1) {
          isUseWindList.push(6);
        }
        if (nineFrame === 1) {
          isUseWindList.push(9);
        }
        if (sixteenFrame === 1) {
          isUseWindList.push(16);
        }
        const nearWind = isUseWindList.find((wind) => wind - data.length >= 0);
        const list = [...data];
        new Array(Number(nearWind - list.length)).fill("").forEach((_) => {
          list.push(null);
        });
        onChoose(list);
      } else {
        const list = [...data];
        if (list.length === 0) {
          new Array(Number(currentWindNum) || 1).fill("").forEach((_) => {
            list.push(null);
          });
          onChoose(list);
          return;
        }
        if (num >= 0) {
          const newList = list.slice(0, currentWindNum);
          const result = num === 0 ? list : newList
          onChoose(result);
        } else {
          new Array(Number(currentWindNum - list.length))
            .fill("")
            .forEach((_) => {
              list.push(null);
            });
          onChoose(list);
        }
        // new Array(Number(currentWindNum - list.length))
        //   .fill("")
        //   .forEach((_) => {
        //     list.push(null);
        //   });
        // onChoose(list);
      }
    }
  }

  async function handleClassSelect(_, { node }) {
    // console.log("handleClassSelect", node);
    if (node.type !== 'class') return;
    setActiveRoomId(node.id);
    if (currentId[0] === node.id) return
    setCurrentId([node.id])
    // 获取教室对应的老师\班级\年级信息
    const info = await getBaseCourseUser({
      time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      nodeId: node.id,
      type: 4, // 节点类型 1.录播 2.监控 3.教室 4.班级
    });
    if (info.code === 0) {
      prevDispatch({
        type: "change_state",
        payload: {
          classRoomInfo: info.data[0],
        },
      });
    }
    // 获取教室绑定--设备列表
    const result = await getRoomDeviceById({
      orgId: currentOrg.id,
      roomId: node.roomId,
    });

    /* 普通模式 */
    if (prevState.patrolFreeSetting && prevState.patrolFreeSetting.freeMode === 101) {
      if (result.code === 0) {
        let deviceList = result.data.deviceList || [];
        const magicDeviceList = result.data.magicDeviceList || [];
        if (deviceList.length > 0) {
          const { belongs, monitoringNodeId } = deviceList[0];
          getPatrolLiveByFree({
            belong: belongs,
            nodeId: monitoringNodeId,
            bureauId: currentOrg.id,
          }).then((result) => {
            if (result && result.code === 0 && result.data[0]) {
              onChoose({
                url: result.data[0].pcUrl,
                belongs,
                urlList: [],
                monitoringNodeId,
                classRoomId: node.id,
                type: 4,
                nodeId: result.data[0].nodeId,
              });
            }
          });
          return;
        }
        if (magicDeviceList.length > 0) {
          const result = await getMagicLive({ roomId: node.id });
          if (result && result.code === 0 && result.data) {
            let urlList = result.data.liveUrls || [];
            if (result.data.success === 500) {
              urlList = urlList.filter((item) => item.status === 1);
            }
            // urlList = urlList.map((item) => item.flvUrl);
            if (urlList.length === 0) {
              message.error("暂无播放地址");
              return;
            }
            const data = {
              url: urlList[0].flvUrl,
              urlList: urlList.map((item) => item.flvUrl),
              monitoringNodeId: node.id,
              belongs: 40000,
              classRoomId: node.id,
              type: 4,
              nodeId: urlList[0].nodeId,
            };
            onChoose(data);
          }
          return;
        }
        message.info("暂无录播设备或魔盒");
      }
      return;
    }
    if (result.code === 0) {
      const request = [];
      /* 监控和录播设置 */
      const deviceList = result.data.deviceList || [];
      const magicDeviceList = result.data.magicDeviceList || [];

      [...deviceList, ...magicDeviceList].forEach((item) => {
        request.push(getPromiseByBelong(item, node.id));
      });
      let data = await Promise.all(request);
      data = data.filter(Boolean);

      const moheUrls = [];
      data.forEach((item) => {
        if (item.belongs === "10006") {
          if (item.urlList.length > 1) {
            item.urlList.forEach((urlItem, index) => {
              moheUrls.push({
                belongs: 40000,
                monitoringNodeId: item.monitoringNodeId + index,
                url: urlItem.flvUrl,
                urlList: [],
                classRoomId: node.id,
                type: 4,
                nodeId: urlItem.nodeId,
              });
            });
          }
        } else {
          // moheUrls.push(item);
          moheUrls.push({ ...item, classRoomId: node.id, type: 4 });
        }
      });
      data = [...moheUrls];
      // 处理窗口数量相关问题
      const currentWindNum = videoList.length;
      const num = data.length - currentWindNum;
      if (prevState.isAdaptiveMode) {
        const isUseWindList = [];
        const {
          oneFrame,
          twoFrame,
          threeFrame,
          fourFrame,
          sixFrame,
          nineFrame,
          sixteenFrame,
        } = prevState.patrolSystemSetting;
        if (oneFrame === 1) {
          isUseWindList.push(1);
        }
        if (twoFrame === 1) {
          isUseWindList.push(2);
        }
        if (threeFrame === 1) {
          isUseWindList.push(3);
        }
        if (fourFrame === 1) {
          isUseWindList.push(4);
        }
        if (sixFrame === 1) {
          isUseWindList.push(6);
        }
        if (nineFrame === 1) {
          isUseWindList.push(9);
        }
        if (sixteenFrame === 1) {
          isUseWindList.push(16);
        }
        const nearWind = isUseWindList.find((wind) => wind - data.length >= 0);
        const list = [...data];
        new Array(Number(nearWind - list.length)).fill("").forEach((_) => {
          list.push(null);
        });
        onChoose(list);
      } else {
        const list = [...data];
        if (list.length === 0) {
          new Array(Number(currentWindNum) || 1).fill("").forEach((_) => {
            list.push(null);
          });
          onChoose(list);
          return;
        }
        if (num >= 0) {
          const newList = list.slice(0, currentWindNum);
          const result = num === 0 ? list : newList
          onChoose(result);
        } else {
          new Array(Number(currentWindNum - list.length))
            .fill("")
            .forEach((_) => {
              list.push(null);
            });
          onChoose(list);
        }
        // new Array(Number(currentWindNum - list.length))
        //   .fill("")
        //   .forEach((_) => {
        //     list.push(null);
        //   });
        // onChoose(list);
      }
    }
  }

  function getPromiseByBelong(
    { belongs, monitoringNodeId, equipmentTypeInfoId, equipmentInfoId },
    roomId
  ) {
    if (belongs === 20000 || belongs === 10000) {
      return new Promise((resolve, reject) => {
        getPatrolLiveByFree({
          belong: belongs,
          nodeId: monitoringNodeId,
          bureauId: currentOrg.id,
        }).then((result) => {
          if (result && result.code === 0 && result.data[0]) {
            resolve({
              url: result.data[0].pcUrl,
              belongs,
              urlList: [],
              monitoringNodeId,
            });
          } else {
            resolve();
          }
        });
      });
    }
    if (equipmentTypeInfoId === "10006" || equipmentTypeInfoId === "10007") {
      return new Promise((resolve, reject) => {
        getMagicLive({ roomId }).then((result) => {
          if (result && result.code === 0 && result.data) {
            let urlList = result.data.liveUrls || [];
            // if (result.data.success === 500) {
            //   urlList = urlList.filter((item) => item.status === 1);
            // }
            // urlList = urlList.map((item) => item.flvUrl);
            if (urlList.length === 0) {
              message.error("暂无播放地址");
              return;
            }
            const data = {
              url: urlList[0].flvUrl,
              urlList,
              monitoringNodeId: equipmentInfoId,
              belongs: "10006",
            };
            resolve(data);
          } else {
            resolve();
          }
        });
      });
    }
  }
  function changeTab(tab) {
    if (prevState.patrolFreeSetting?.freeMode === 102) {
      if (tab.id === 4 || active === 4) {
        onChoose("clear");
      }
      onChoose("clear");
    }

    setActive(tab.id);
    prevDispatch({
      type: "change_is_monitor",
      payload: {
        isMonitor: tab.id === 1,
        classRoomInfo: null,
        isClassRoom: tab.id === 4,
      },
    });
  }

  return (
    <Root id="tabar" style={{ height: currentHeight + "px" }}>
      <OrgSelector {...currentOrg} setCurrentOrg={setCurrentOrg} />
      <ul className="tab-head flex list-none">
        {tabList.map((tab) => {
          if (tab.show === 1) {
            return (
              <li
                key={tab.id}
                onClick={() => changeTab(tab)}
                className={`${active === tab.id ? "active" : ""}`}
              >
                {tab.label}
              </li>
            );
          }
          return null;
        })}
      </ul>

      <div className="content overflow-y-auto">
        {active === 4 && classPlace.length > 0 && (
          <Tree
            onSelect={handleSelect}
            defaultExpandAll
            fieldNames={{
              title: "name",
              key: "id",
              children: "children",
            }}
            selectedKeys={currentId}
            treeData={classPlace}
          />
        )}

        {active === 4 && classPlace.length === 0 && (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}

        {active === 5 && classGradeList.length > 0 && (
          <Tree
            onSelect={handleClassSelect}
            defaultExpandAll
            selectedKeys={currentId}
            treeData={classGradeList}
          />
        )}

        {active === 5 && classGradeList.length === 0 && (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}

        {active === 1 && (
          <TabMonitor
            onChoose={onChoose}
            monitor={monitor}
            {...currentOrg}
            treeHeight={currentHeight - 204}
            setActiveRoomId={setActiveRoomId}
          />
        )}
        {/* {active === 2 && (
          <TabEquip onChoose={onChoose} device={device} {...currentOrg} />
        )} */}
        {/* {active === 3 && <TabLive onChoose={onChoose} live={live} />} */}
      </div>
    </Root>
  );
}

function TabMonitor({ monitor, onChoose, id, treeHeight, setActiveRoomId }) {
  const [prevState, prevDispatch] = useContext(PrevContext);
  const [mode, setModel] = useState("tree");
  const [filterValue, setFilterValue] = useState("");
  const [currentId, setCurrentId] = useState([])
  const filteredMonitor = useMemo(() => {
    if (!filterValue) return monitor;
    return monitor.filter(
      (item) => item.name.indexOf(filterValue) !== -1 && item.nodeType === "2"
    );
  }, [filterValue, monitor]);

  const monitorTree = useMemo(() => {
    const monitorList = monitor.map((item) => ({
      ...item,
      key: item.monitoringNodeId,
      title: item.name,
      parentId: item.parentNodeId,
      id: item.monitoringNodeId,
    }));
    return $getTree(monitorList, "0");
  }, [monitor]);

  async function onChooseNode(item) {
    setActiveRoomId(item.monitoringNodeId)
    if (currentId[0] === item.monitoringNodeId) return
    setCurrentId([item.monitoringNodeId])
    // 获取教室对应的老师\班级\年级信息
    const info = await getBaseCourseUser({
      time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      nodeId: item.monitoringNodeId,
      type: 2, // 节点类型 1.录播 2.监控 3.教室 4.班级
    });
    if (info.code === 0) {
      prevDispatch({
        type: "change_state",
        payload: {
          classRoomInfo: info.data[0],
        },
      });
    }
    /* 华三插件 */
    if (prevState.vendor == 1002) {
      const res = await getVendorById({ nodeId: item.monitoringNodeId });
      if (res.data !== "1002") {
        message.error("请选择正常的厂商节点");
        return;
      }
      if (prevState.oWebControl) {
        prevState.oWebControl.JS_RequestInterface({
          method: "play.preview",
          data: {
            channel_id: item.monitoringNodeCode,
          },
        });
      } else {
        message.error("插件启动失败，请检查插件是否安装！");
      }
      return;
    }
    /* 阿启视插件 */
    if (prevState.vendor == 1000) {
      const res = await getVendorById({ nodeId: item.monitoringNodeId });
      if (res.data !== "1000") {
        message.error("请选择正常的厂商节点");
        return;
      }
      const result = await getPatrolLiveByFreePri({
        nodeId: item.monitoringNodeId,
        publishOrgId: id,
      });
      if (result && result.code === 0 && result.data[0]) {
        if (prevState.oWebControl) {
          prevState.oWebControl.JS_Video(
            result.data[0].pcUrl,
            item.monitoringNodeId,
            item.name
          );
        } else {
          message.error("插件启动失败，请检查插件是否安装！");
        }
      }
      return;
    }

    const result = await getPatrolLiveByFree({
      belong: item.belongs,
      nodeId: item.monitoringNodeId,
      bureauId: id,
    });
    let monitorBindClassInfo = null;
    const monitorBindClassInfoRes = await getTimetable({
      nodeId: item.monitoringNodeId,
    });
    if (monitorBindClassInfoRes.code === 0) {
      monitorBindClassInfo = monitorBindClassInfoRes.data;
    }

    if (result && result.code === 0 && result.data[0]) {
      const { webrtc, flvUrl, pcUrl } = result.data[0];
      let url = webrtc ? webrtc : flvUrl ? flvUrl : pcUrl;
      if (!url) {
        message.error("暂无播放地址");
        return;
      }
      const recordUrl = webrtc ? flvUrl : url;
      onChoose({
        url,
        recordUrl,
        monitoringNodeId: item.monitoringNodeId,
        belongs: item.belongs,
        urlList: [],
        monitorBindClassInfo,
        classRoomId: item.monitoringNodeId,
        type: 2,
      });
    }
  }
  function onSelect(selectedKeys, e) {
    const { selectedNodes } = e;
    const target = selectedNodes[0];
    if (target.nodeType === "2") {
      onChooseNode(target);
    } else {
      message.error("请选择监控节点");
    }
  }
  useEffect(() => {
    function findNodeById(val) {
      const target = monitor.find((item) => item.monitoringNodeCode === val);
      if (target) {
        $message.emit("dispatchMonitorResultByCode", target);
      }
    }
    $message.on("dispatchMonitorByCode", findNodeById);
    return () => {
      $message.off("dispatchMonitorByCode", findNodeById);
    };
  }, [monitor]);
  return (
    <div>
      <div className="mb-5">
        <Input.Search
          placeholder="请输入关键字搜索"
          onSearch={(value) => {
            setFilterValue(value);
            setModel(value ? "list" : "tree");
          }}
        />
      </div>
      {mode === "tree" && monitorTree.length > 0 && (
        <Tree
          blockNode
          height={treeHeight}
          defaultExpandAll
          onSelect={onSelect}
          selectedKeys={currentId}
          treeData={monitorTree}
          titleRender={(item) => (
            <div>
              {item.title}
              {item.children?.length > 0 && (
                <span style={{ marginLeft: 6 }}>({item.children.length})</span>
              )}
            </div>
          )}
        />
      )}
      {mode === "list" &&
        filteredMonitor.map((item) => {
          return (
            <EquipItem
              onClick={() => onChooseNode(item)}
              key={item.monitoringNodeId}
              className="truncate relative cursor-pointer"
            >
              {item.name}
            </EquipItem>
          );
        })}
      {mode === "list" && filteredMonitor.length === 0 && (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
      {mode === "tree" && monitorTree.length === 0 && (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
    </div>
  );
}
const EquipItem = styled.div`
  &:hover {
    color: #007aff;
  }
  & + & {
    margin-top: 20px;
  }
`;
// function TabEquip({ device, onChoose, id }) {
//   const [filter, setFilter] = useState({
//     value: "",
//     online: -1,
//   });

//   const filteredDevice = useMemo(() => {
//     if (filter.value === "" && filter.online === -1) return device;
//     return device.filter((item) => {
//       return (
//         item.name.indexOf(filter.value) !== -1 && item.online === filter.online
//       );
//     });
//   }, [filter, device]);

//   async function onChooseNode(item) {
//     const result = await getPatrolLiveByFree({
//       belong: item.belongs,
//       nodeId: item.monitoringNodeId,
//       bureauId: id,
//     });
//     if (result && result.code === 0 && result.data) {
//       let data = result.data;
//       if (Array.isArray(data)) {
//         data = data[0];
//       }
//       const { flvUrl, pcUrl } = data;
//       let url = flvUrl ? flvUrl : pcUrl;
//       if (!url) {
//         message.error("暂无播放地址");
//         return;
//       }
//       console.log({
//         url,
//         recordUrl: url,
//         monitoringNodeId: item.monitoringNodeId,
//         belongs: item.belongs,
//         urlList: [],
//       });
//       onChoose({
//         url,
//         recordUrl: url,
//         monitoringNodeId: item.monitoringNodeId,
//         belongs: item.belongs,
//         urlList: [],
//       });
//     }
//   }

//   return (
//     <>
//       <div className="mb-5">
//         <Input.Search
//           className="mb-3"
//           placeholder="请输入关键字搜索"
//           onSearch={(value) => {
//             setFilter((pre) => ({ ...pre, value }));
//           }}
//         />

//         <div className="flex line-tab">
//           <div
//             onClick={() => {
//               setFilter((pre) => ({ ...pre, online: -1 }));
//             }}
//             className={`flex-1 rounded bg-gray-100 h-8 leading-8 text-center cursor-pointer ${
//               filter.online === -1 ? "active" : ""
//             }`}
//           >
//             全部
//           </div>
//           <div
//             onClick={() => {
//               setFilter((pre) => ({ ...pre, online: 1 }));
//             }}
//             className={`mx-1 flex-1 rounded bg-gray-100 h-8 leading-8 text-center cursor-pointer ${
//               filter.online === 1 ? "active" : ""
//             }`}
//           >
//             在线
//           </div>
//           <div
//             onClick={() => {
//               setFilter((pre) => ({ ...pre, online: 0 }));
//             }}
//             className={`flex-1 rounded bg-gray-100 h-8 leading-8 text-center cursor-pointer ${
//               filter.online === 0 ? "active" : ""
//             }`}
//           >
//             离线
//           </div>
//         </div>
//       </div>
//       {filteredDevice.map((item) => (
//         <EquipItem
//           onClick={() => onChooseNode(item)}
//           key={item.monitoringNodeId}
//           className="flex items-center relative cursor-pointer"
//         >
//           <div
//             style={{
//               width: 6,
//               height: 6,
//               top: -1,
//               background: item.online === 1 ? "#17BE6B" : "#D9D9D9",
//             }}
//             className="rounded-full relative"
//           ></div>
//           <div className="truncate flex-1 ml-2">{item.name}</div>
//         </EquipItem>
//       ))}
//       {filteredDevice.length === 0 && (
//         <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
//       )}
//     </>
//   );
// }

// function TabLive({ live, onChoose }) {
//   const [filterValue, setFilterValue] = useState("");
//   const filteredLive = useMemo(() => {
//     if (!filterValue) return live;
//     return live.filter((item) => item.eventName.indexOf(filterValue) !== -1);
//   }, [filterValue, live]);
//   const handleChoose = (item) => {
//     const flvUrls = item.urls
//       .filter((i) => i.status === "1")
//       .map((s) => s.flvUrl);
//     onChoose({
//       monitoringNodeId: item.eventInfoId,
//       belongs: 30000,
//       url: flvUrls[0] ? flvUrls[0] : "",
//       recordUrl: flvUrls[0] ? flvUrls[0] : "",
//       urlList: flvUrls,
//     });
//   };
//   const typeIcons = ["iconbofang", "icon-user", "icon-school"];
//   return (
//     <>
//       <div className="mb-5">
//         <Input.Search
//           className="mb-3"
//           placeholder="请输入关键字搜索"
//           onSearch={setFilterValue}
//         />
//       </div>
//       {filteredLive.map((item) => (
//         <EquipItem
//           onClick={() => handleChoose(item)}
//           key={item.eventInfoId}
//           className="truncate relative cursor-pointer"
//         >
//           <div className="flex items-center">
//             <i
//               className={`iconfont align-middle ${typeIcons[item.type - 1]}`}
//             ></i>
//             <span className="ml-1 flex-1 truncate">{item.eventName}</span>
//           </div>
//         </EquipItem>
//       ))}
//       {filteredLive.length === 0 && (
//         <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
//       )}
//     </>
//   );
// }

const Root = styled.div`
  text-align: initial;
  display: inline-block;
  position: relative;
  overflow: hidden;
  width: 320px;
  background: #fff;
  border-radius: 4px;
  .tab-head {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding-left: 0;
    margin: 0;
    li {
      cursor: pointer;
      color: #262626;
      padding: 16px 0;
      flex: 1;
      text-align: center;
      border-bottom: 2px solid transparent;
    }
    li.active {
      border-bottom: 2px solid #007aff;
    }
  }
  .content {
    height: calc(100% - 104px);
    padding: 24px;
  }
  .line-tab {
    div {
      &.active,
      &:hover {
        color: #fff;
        background: #007aff;
      }
    }
  }
`;
const OrgSelectorRoot = styled.div`
  position: relative;
  .display {
    padding: 0 16px;
    height: 48px;
    background: #fafafa;
    display: flex;
    align-items: center;
    justify-content: space-between;
    i.active {
      transition: all 0.1s linear;
      display: inline-block;
      transform: rotate(180deg);
    }
  }
  .org-name {
    padding-right: 20px;
    color: #262626;
    font-weight: 700;
  }
  .drop-conatiner {
    position: absolute;
    z-index: 9;
    width: 100%;
    top: 48px;
    left: 0;
    background: #fff;
    box-shadow: 0px 6px 12px 0px rgba(51, 51, 51, 0.16);
    border-radius: 0 0 4px 4px;
    .filter {
    }
    .list {
      height: 350px;
      overflow-y: auto;
    }
    .filter-item {
      padding: 0 16px;
      line-height: 40px;
      margin: 0 -16px;
      cursor: pointer;
    }
    .filter-item:hover {
      background: #f5f5f5;
    }
  }
`;

function OrgSelector({ name, id, setCurrentOrg, ISJD }) {
  const [isFiltering, setIsFiltering] = useState(false);
  const [filetrValue, setFilterValue] = useState("");

  const [show, setShow] = useState(false);
  const [list, setList] = useState([]);
  const [data, setData] = useState([]);

  function onChoosed(title, id) {
    setShow(false);
    setCurrentOrg((pre) => ({ ...pre, id, name: title }));
  }

  const filterListNodes = useMemo(() => {
    const nodes = list
      .filter((val) => val.title.indexOf(filetrValue) !== -1)
      .map((org) => (
        <div
          key={org.id}
          onClick={() => onChoosed(org.title, org.id)}
          className="filter-item"
        >
          {org.title}
        </div>
      ));
    if (nodes.length > 0) return nodes;
    return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filetrValue, list]);

  useEffect(() => {
    const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));

    getAllOrgById({ orgId: userInfo.orgId }).then((result) => {
      const list = result.data.map((org) => ({
        id: org.id,
        key: org.id,
        pid: org.parentId,
        value: org.id,
        title: org.orgName,
      }));
      if (list.length > 0) {
        setList(list);
        setData(data2Tree(list, "0"));
      }
    });
  }, []);

  return (
    <OrgSelectorRoot id="orgSelector" data-id={id}>
      <Clickoutside
        cb={() => {
          setShow(false);
          setFilterValue("");
          setIsFiltering(false);
        }}
      >
        <div
          onClick={() => {
            ISJD && setShow(true);
          }}
          className="display"
        >
          <span className="org-name truncate">{name}</span>
          {ISJD && (
            <i
              className={`iconfont iconxiajiantou ${show ? "active" : ""}`}
            ></i>
          )}
        </div>
        {show && (
          <div className="drop-conatiner">
            <div className="filter p-4">
              <Input.Search
                onSearch={(value) => {
                  setFilterValue(value);
                  if (value) {
                    setIsFiltering(true);
                  } else {
                    setIsFiltering(false);
                  }
                }}
                placeholder="请输入关键字搜索"
              />
            </div>
            <div className="list px-4 py-1">
              {isFiltering ? (
                filterListNodes
              ) : (
                <Tree
                  selectedKeys={[id]}
                  onSelect={(selectedKeys, e) => {
                    const { title, id } = e.node;
                    onChoosed(title, id);
                  }}
                  defaultExpandAll
                  treeData={data}
                />
              )}
            </div>
          </div>
        )}
      </Clickoutside>
    </OrgSelectorRoot>
  );
}

export default Tabar;
