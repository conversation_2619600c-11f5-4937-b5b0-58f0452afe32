import React, { useState, useRef } from "react"
import { useCountDown } from "ahooks"
import { Space, message } from "antd"
import styled from "styled-components"
import { getScreenShot, startRecordLive, stopRecordLive } from "@/api/upload"
import { fileCenterCallback,getpatrolliveByRtsp } from "@/api"
import { $showLoading, $hideLoading } from "@/tools"

const Root = styled.div`
  background: #ffffff;
  border-radius: 4px;
  padding: 24px;
  margin-top: 24px;
  .screenShot {
    cursor: pointer;
    display: flex;
    align-items: center;
    color: #595959;
    line-height: 1;
    padding: 0 12px;
    height: 32px;
    border-radius: 2px;
    border: 1px solid #d9d9d9;
    * {
    pointer-events: none;
    }
    &:hover {
      transition: all 0.3s linear;
      border-color: #007aff;
      color: #007aff;
    }
  }
  .tape {
    position: relative;
    /* width: 100px; */
    .progress {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 100%;
      background: rgba(0, 122, 255, 0.08);
      transition: right 1s;
    }
    &.active {
      border-color: #007aff;
      color: #007aff;
    }
  }
`
let currentRtsp = null;
function TourLog({ activeIndex, videoList }) {
  const taskRef = useRef()

  const handle2screenShot = async (e) => {
     // RTMP模式下的控制
     const cameraid = e.target.getAttribute('cameraid')
     if(cameraid) {
       const result = await getpatrolliveByRtsp({nodeId: cameraid})
       onScreenShoting(result.data);
       return;
     }
     if (activeIndex === -1) {
       return message.error("请选择通道");
     }
     if (!videoList[activeIndex].recordUrl) {
       return message.error("该通道没有播放地址");
     }
     onScreenShoting(videoList[activeIndex].recordUrl)
  }
  const onScreenShoting = (url) => {
    $showLoading("截图中...");
    getScreenShot({ flvUrl: url })
      .then((res) => {
        $hideLoading();
        if (res.code === 0 && res.data) {
          fileCenterCallback({
            fileSource: 4,
            filename: res.data,
            oldFilename: res.data,
            size: 0,
          }).then(res => {
            if (res.code === 0) {
              message.success("截图已保存至“我的文件”")
            }
          })
        } else {
          message.error("截图失败，请重试");
        }
      })
      .catch((e) => {
        $hideLoading();
      });
  }
  /* 视频录制 */
  const [activeTap, setActiveTap] = useState(false)
  const [targetDate, setTargetDate] = useState()

  const [countdown] = useCountDown({
    targetDate,
    onEnd: () => {
      setActiveTap(false)
      handleEndTap(15)
    },
  })

  const handleStartTap = async (e) => {
    if (activeTap && taskRef.current) {
      /* 结束录制 */
      /* 手动暂停 录制多少秒 */
      const time = Math.round((15000 - countdown) / 1000)
      if (time > 5) {
        setTargetDate(undefined)
        setActiveTap(false)
        handleEndTap(time)
      } else {
        message.error("录制时间太短")
      }
      return
    }
    let url = '';
    // RTMP模式下的控制
    const cameraid = e.target.getAttribute('cameraid')
    if(cameraid) {
      const result = await getpatrolliveByRtsp({nodeId: cameraid})
      url = result.data;
      currentRtsp = result.data;
    } else {
      if (activeIndex === -1) {
        return message.error("请选择通道");
      }
      if (!videoList[activeIndex].recordUrl) {
        return message.error("该通道没有播放地址");
      }
      url =  videoList[activeIndex].recordUrl
    }

    /* 开始录制 */
    startRecordLive({ flvUrl: url }).then(res => {
      if (res.code === 0 && res.data) {
        taskRef.current = res.data
        setTargetDate(Date.now() + 15000)
        setActiveTap(true)
      } else {
        return message.error("录制失败")
      }
    })
  }

  const handleEndTap = sec => {
    $showLoading("生成中...")
    const apis = [
      getScreenShot({ flvUrl: currentRtsp?currentRtsp:videoList[activeIndex].recordUrl }),
      stopRecordLive({ processId: taskRef.current, sec }),
    ]

    Promise.all(apis)
      .then(data => {
        if (!data[0].data || !data[1].data) {
          message.error("录制失败")
          setTargetDate(undefined)
          setActiveTap(false)
          $hideLoading()
          return
        }
        fileCenterCallback({
          fileSource: 4,
          filename: data[1].data,
          oldFilename: data[1].data,
          size: 0,
        }).then(res => {
          if (res.code === 0) {
            message.success("录像已保存至“我的文件”")
          }
        })
        // const params = {
        //   attrPath: data[1].data,
        //   attrPic: data[0].data,
        //   attrType: 2,
        //   patrolRecordId: "",
        // }
        // setAttr(c => [...c, params])
        // message.success("录制成功")
        setTargetDate(undefined)
        setActiveTap(false)
        $hideLoading()
      })
      .catch(error => {
        message.error("录制失败")
        setTargetDate(undefined)
        setActiveTap(false)
        $hideLoading()
      })
  }

  return (
    <Root id="replyContainer">
      <div className="flex justify-end">
        <Space>
          <div
            onClick={handle2screenShot}
            className="screenShot"
          >
            <i className="iconfont iconxiangji"></i>
            <span className="ml-1">截图</span>
          </div>
          <div
            onClick={handleStartTap}
            className={`screenShot tape ${activeTap ? "active" : ""}`}
          >
            {activeTap && (
              <div
                style={{
                  right:
                    countdown === 0 ? "100%" : (countdown / 15000) * 100 + "%",
                }}
                className="progress"
              ></div>
            )}
            <i className="iconfont iconshipin2"></i>
            <span className="ml-1">
              录像
              {activeTap && (
                <span
                  className="inline-block text-center"
                  style={{ width: "25px" }}
                >
                  {" "}
                  {Math.round((15000 - countdown) / 1000)}s
                </span>
              )}
            </span>
          </div>
        </Space>
      </div>
    </Root>
  )
}

export default TourLog
