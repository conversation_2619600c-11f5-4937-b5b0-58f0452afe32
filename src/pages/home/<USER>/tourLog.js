import React, { useState, useRef, useContext } from "react";
import { useCountDown } from "ahooks";
import { useReplyList } from "@/hooks";
import moment from "moment";
import _ from "lodash";
import { Button, Input, message, Popover, Form } from "antd";
import { MessageOutlined } from "@ant-design/icons";
import { YsEmpt, FormContext, TourObjInlineForm } from "@/components";
import styled from "styled-components";
import { addPatrolRecord, getpatrolliveByRtsp } from "@/api";
import { getScreenShot, startRecordLive, stopRecordLive } from "@/api/upload";
import { $showLoading, $hideLoading } from "@/tools";
import { PrevContext } from "./../components/prevContext";
import Pic<PERSON>ox from "../../common/picBox";
import PreviewTool from "../../common/previewTool";
const { TextArea } = Input;
const Root = styled.div`
  position: relative;
  background: #ffffff;
  border-radius: 4px;
  padding: 24px;
  margin-top: 24px;
  .screenShot {
    cursor: pointer;
    display: flex;
    align-items: center;
    color: #595959;
    line-height: 1;
    padding: 0 12px;
    height: 32px;
    border-radius: 2px;
    border: 1px solid #d9d9d9;
    * {
      pointer-events: none;
    }
    &:hover {
      transition: all 0.3s linear;
      border-color: #007aff;
      color: #007aff;
    }
  }
  .tape {
    position: relative;
    /* width: 100px; */
    .progress {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 100%;
      background: rgba(0, 122, 255, 0.08);
      transition: right 1s;
    }
    &.active {
      border-color: #007aff;
      color: #007aff;
    }
  }
  .replyContainer {
    .ant-popover-inner-content {
      padding: 0;
    }
    .replyItem {
      cursor: pointer;
      height: 40px;
      line-height: 40px;
      padding: 0 16px;
      &:hover {
        background: #f7f7f7;
      }
    }
  }

  .fstReplyBtn {
    z-index: 9;
    position: absolute;
    right: 10px;
    bottom: 4px;
    .fastBtn {
      cursor: pointer;
    }
    .fastBtn:hover {
      color: #007aff;
    }
  }
  .ant-form-inline .ant-form-item-with-help {
    margin-bottom: 0 !important;
  }
`;
const patrolDefaultObject = {
  personalIsFill: 0,
  personalIsShow: 1,
  gradeIsFill: 0,
  gradeIsShow: 1,
  classIsFill: 0,
  classIsShow: 1,
};
let currentRtsp = null;
function TourLog({ activeIndex, videoList, id }) {
  const [prevState, prevDispatch] = useContext(PrevContext);
  // ===
  const patrolObject = Object.assign(patrolDefaultObject, {
    personalIsShow: prevState.patrolFreeSetting?.nameIsFull,
    classIsShow: prevState.patrolFreeSetting?.classIsFull,
    gradeIsShow: prevState.patrolFreeSetting?.gradeIsFull,
  });

  const defaultPatrolObjectValues = {
    defaultUserId: prevState.patrolFreeSetting?.nameIsFull
      ? prevState.classRoomInfo?.subjectTeacherId
      : "",
    defaultUserLabel: prevState.patrolFreeSetting?.nameIsFull
      ? prevState.classRoomInfo?.subjectTeacherName
      : "",
    defaultClassId: prevState.patrolFreeSetting?.classIsFull
      ? prevState.classRoomInfo?.classId
      : "",
    defaultClassName: prevState.patrolFreeSetting?.classIsFull
      ? prevState.classRoomInfo?.className
      : "",
    defaultGradeId: prevState.patrolFreeSetting?.gradeIsFull
      ? prevState.classRoomInfo?.gradeId
      : "",
    defaultGradeName: prevState.patrolFreeSetting?.gradeIsFull
      ? prevState.classRoomInfo?.gradeName
      : "",
  };

  const [form] = Form.useForm();
  const tourObjRef = useRef();
  const taskRef = useRef();
  const [state, dispatch] = useContext(FormContext);
  const [attr, setAttr] = useState([]);

  /* ----------------------------------- */
  const [comment, setComment] = useState("");

  /* ---------------------------------- */
  const handle2screenShot = async (e) => {
    // RTMP模式下的控制
    const cameraid = e.target.getAttribute("cameraid");
    if (cameraid) {
      let publishOrgId = "";
      const orgSelector = document.getElementById("orgSelector");
      if (orgSelector && orgSelector.dataset.id) {
        publishOrgId = orgSelector.dataset.id;
      }
      const result = await getpatrolliveByRtsp({
        nodeId: cameraid,
        publishOrgId,
      });
      onScreenShoting(result.data);
      return;
    }
    if (activeIndex === -1) {
      return message.error("请选择通道");
    }
    if (!videoList[activeIndex].recordUrl && !videoList[activeIndex].url) {
      return message.error("该通道没有播放地址");
    }
    onScreenShoting(
      videoList[activeIndex].recordUrl || videoList[activeIndex].url
    );
  };
  const onScreenShoting = (url) => {
    $showLoading("截图中...");
    getScreenShot({ flvUrl: url })
      .then((res) => {
        $hideLoading();
        if (res.code === 0 && res.data) {
          message.success("截图成功");
          setAttr([
            ...attr,
            {
              attrPath: res.data,
              attrPic: "",
              attrType: 1,
              patrolRecordId: "",
            },
          ]);
        } else {
          message.error("截图失败，请重试");
        }
      })
      .catch((e) => {
        $hideLoading();
      });
  };
  const submit = async () => {
    const tourInfo = tourObjRef.current.validate();
    if (!tourInfo) return;
    if (!comment) {
      return message.error("请输入评论");
    }
    saveTourLog(tourInfo);
  };
  const saveTourLog = (tourInfo) => {
    $showLoading("loading...");
    const patrolRecordAttrList = attr.map((item) => ({
      attrPath: item.attrPath,
      attrPic: item.attrPic,
      attrType: item.attrType,
      patrolRecordId: item.patrolRecordId,
    }));
    const data = {
      taskId: id,
      patrolRecordId: 0,
      recordedUserName: tourInfo.userInfo.label,
      recordedUserId: tourInfo.userInfo.value,
      patrolComment: comment,
      patrolRecordAttrList,
      patrolTime: moment(+new Date()).format("YYYY-MM-DD HH:mm:ss"),
      useTable: state.formAnswerId ? 1 : 2,
      formTemplateInfoId: state.tempId,
      formInfoName: state.name,
      formInfoId: state.formInfoId,
      formTemplateAnswerId: state.formAnswerId,
      classId: tourInfo.classInfo.value
        ? tourInfo.classInfo.value[tourInfo.classInfo.value.length - 1]
        : "",
      className: tourInfo.classInfo.label,
      gradeId: tourInfo.gradeInfo.value ? tourInfo.gradeInfo.value[1] : "",
      gradeName: tourInfo.gradeInfo.label,
    };

    addPatrolRecord(data)
      .then((res) => {
        if (res.code === 0) {
          /* 清除数据 */
          setAttr([]);
          setComment("");
          tourObjRef.current.resetValues();
          dispatch({
            type: "RESET",
            payload: {
              // 修复提交后无法保存量表
              // formInfoId: "",
              formInfoId: state.formInfoId,
            },
          });
          message.success("保存成功");
          $hideLoading();
        }
      })
      .catch((error) => {
        $hideLoading();
      });
  };
  // 资源预览
  const [previewInfo, setPreviewInfo] = useState({
    list: [],
    index: -1,
    show: false,
  });
  const handle2Preview = (index) => {
    if (prevState.oWebControl) {
      prevState.oWebControl.JS_SetHide();
    }
    if (prevState.oWebControl && prevState.oWebControl.JS_RequestInterface) {
      prevState.oWebControl.JS_RequestInterface({
        method: "window.hide",
      });
    }

    const list = attr.map((item) => item.attrPath);
    setPreviewInfo({ list, show: true, index });
  };
  const closePreview = () => {
    if (prevState.oWebControl) {
      if (prevState.oWebControl.JS_SetShow) {
        prevState.oWebControl.JS_SetShow();
      }
      if (prevState.oWebControl.JS_RequestInterface) {
        prevState.oWebControl.JS_RequestInterface({
          method: "window.show",
        });
      }
    }
    setPreviewInfo({ list: [], show: false, index: -1 });
  };
  const handle2Delete = (index) => {
    const newAttr = _.cloneDeep(attr);
    newAttr.splice(index, 1);
    setAttr(newAttr);
  };
  /* 视频录制 */
  const [activeTap, setActiveTap] = useState(false);
  const [targetDate, setTargetDate] = useState();

  const [countdown] = useCountDown({
    targetDate,
    onEnd: () => {
      setActiveTap(false);
      handleEndTap(15);
    },
  });

  const handleStartTap = async (e) => {
    if (activeTap && taskRef.current) {
      /* 结束录制 */
      /* 手动暂停 录制多少秒 */
      const time = Math.round((15000 - countdown) / 1000);
      if (time > 5) {
        setTargetDate(undefined);
        setActiveTap(false);
        handleEndTap(time);
      } else {
        message.error("录制时间太短");
      }
      return;
    }

    let url = "";
    // RTMP模式下的控制
    const cameraid = e.target.getAttribute("cameraid");
    if (cameraid) {
      let publishOrgId = "";
      const orgSelector = document.getElementById("orgSelector");
      if (orgSelector && orgSelector.dataset.id) {
        publishOrgId = orgSelector.dataset.id;
      }
      const result = await getpatrolliveByRtsp({
        nodeId: cameraid,
        publishOrgId,
      });
      url = result.data;
      currentRtsp = result.data;
    } else {
      if (activeIndex === -1) {
        return message.error("请选择通道");
      }
      if (!videoList[activeIndex].recordUrl && !videoList[activeIndex].url) {
        return message.error("该通道没有播放地址");
      }
      url = videoList[activeIndex].recordUrl || videoList[activeIndex].url;
    }
    /* 开始录制 */
    startRecordLive({ flvUrl: url }).then((res) => {
      if (res.code === 0 && res.data) {
        taskRef.current = res.data;
        setTargetDate(Date.now() + 15000);
        setActiveTap(true);
      } else {
        return message.error("录制失败");
      }
    });
  };

  const handleEndTap = (sec) => {
    $showLoading("生成中...");

    const apis = [
      getScreenShot({
        flvUrl: currentRtsp
          ? currentRtsp
          : videoList[activeIndex].recordUrl || videoList[activeIndex].url,
      }),
      stopRecordLive({ processId: taskRef.current, sec }),
    ];
    currentRtsp = null;
    Promise.all(apis)
      .then((data) => {
        if (!data[0].data || !data[1].data) {
          message.error("录制失败");
          setTargetDate(undefined);
          setActiveTap(false);
          $hideLoading();
          return;
        }
        const params = {
          attrPath: data[1].data,
          attrPic: data[0].data,
          attrType: 2,
          patrolRecordId: "",
        };
        setAttr((c) => [...c, params]);
        message.success("录制成功");
        setTargetDate(undefined);
        setActiveTap(false);
        $hideLoading();
      })
      .catch((error) => {
        message.error("录制失败");
        setTargetDate(undefined);
        setActiveTap(false);
        $hideLoading();
      });
  };

  const { list: replyList } = useReplyList({ pageNo: 1, pageSize: 9999 });

  return (
    <Root id="replyContainer">
      <Form layout="inline" form={form}>
        <TourObjInlineForm
          ref={tourObjRef}
          {...patrolObject}
          {...defaultPatrolObjectValues}
        />
        <Form.Item>
          <div onClick={handle2screenShot} className="screenShot">
            <i className="iconfont iconxiangji"></i>
            <span className="ml-1">截图</span>
          </div>
        </Form.Item>
        <Form.Item>
          <div
            onClick={handleStartTap}
            className={`screenShot tape ${activeTap ? "active" : ""}`}
          >
            {activeTap && (
              <div
                style={{
                  right:
                    countdown === 0 ? "100%" : (countdown / 15000) * 100 + "%",
                }}
                className="progress"
              ></div>
            )}
            <i className="iconfont iconshipin2"></i>
            <span className="ml-1">
              录像
              {activeTap && (
                <span
                  className="inline-block text-center"
                  style={{ width: "25px" }}
                >
                  {" "}
                  {Math.round((15000 - countdown) / 1000)}s
                </span>
              )}
            </span>
          </div>
        </Form.Item>

        <Form.Item>
          <Button onClick={submit} type="primary">
            提交
          </Button>
        </Form.Item>
      </Form>

      <div className="relative">
        <div className="fstReplyBtn">
          <Popover
            overlayClassName="replyContainer"
            getPopupContainer={() => document.getElementById("replyContainer")}
            placement="top"
            content={
              <div
                className="w-60 relative overflow-y-auto"
                style={{ height: 120 }}
              >
                {replyList.length > 0 ? (
                  replyList.map((item) => (
                    <div
                      className="h-10 replyItem truncate"
                      key={item.id}
                      onClick={() => {
                        setComment(comment + item.content);
                      }}
                    >
                      {item.content}
                    </div>
                  ))
                ) : (
                  <YsEmpt msg="暂无快捷评语" />
                )}
              </div>
            }
            title={<span className="font-bold text-black">快捷评语</span>}
          >
            <div className="fastBtn">
              <MessageOutlined className="text-sm mr-1" />
              <span>快捷评语</span>
            </div>
          </Popover>
        </div>

        <TextArea
          className="mt-4"
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          placeholder="请在此输入评价（必填）"
          rows={4}
          maxLength={200}
        />
      </div>
      {attr.length > 0 ? (
        <div className="attr-container flex flex-wrap mt-5">
          {attr.map((item, index) => (
            <PicBox
              onPreview={() => handle2Preview(index)}
              onDelete={() => handle2Delete(index)}
              key={index}
              src={item.attrPic ? item.attrPic : item.attrPath}
              realPath={item.attrPath}
            />
          ))}
        </div>
      ) : (
        ""
      )}

      {previewInfo.show && (
        <PreviewTool onClose={closePreview} {...previewInfo} />
      )}
    </Root>
  );
}

export default TourLog;
