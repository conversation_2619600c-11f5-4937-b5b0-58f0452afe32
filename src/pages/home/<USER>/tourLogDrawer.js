import React, {
  useState,
  useRef,
  useImperative<PERSON>andle,
  forwardRef,
  useMemo,
  useContext,
  useEffect,
} from "react";
import moment from "moment";
import {
  Drawer,
  Form,
  Input,
  Button,
  DatePicker,
  message,
  Space,
  Popover,
} from "antd";
import {
  PlusOutlined,
  CloseOutlined,
  MessageOutlined,
} from "@ant-design/icons";
import { FormContext, FormRender, TourObjForm, YsEmpt } from "@/components";
import { useTableAndAnswer, useReplyList } from "@/hooks";
import {
  addMyPatrolRecord,
  selectPatrolRecordDetail,
  updateMyPatrolRecord,
  updateAnswerForRepeat,
  savePatrolRecordEditRecord,
  getBaseCourseUser,
} from "@/api";
import { $upload, $isCdn, $showLoading, $hideLoading } from "@/tools";
import styled from "styled-components";
import PreviewTool from "../../common/previewTool";
const UploadList = styled.div``;
const UploadItem = styled.div`
  display: inline-block;
  position: relative;
  height: 100px;
  width: 100px;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  margin: 0 12px 12px 0;
  overflow: hidden;
  &:hover .cover {
    display: block;
  }
  .cover {
    transition: all 0.3s;
    display: none;
    background: rgba(0, 0, 0, 0.4);
  }
`;
const Tab = styled.div`
  margin-bottom: 30px;
`;
const TabItem = styled.span`
  margin-right: 32px;
  cursor: pointer;
  padding-bottom: 6px;
  border-bottom: 2px solid transparent;
  &.active {
    color: #007aff;
    border-block-color: #007aff;
  }
  &:hover {
    color: #007aff;
  }
`;
const FormBtton = styled.div`
  text-align: right;
  border-top: 1px solid #f0f0f0;
  padding: 12px 16px;
  width: 480px;
  position: fixed;
  right: 0;
  bottom: 0;
  background: #fff;
`;
const FastCommentBox = styled.div`
  position: relative;
  top: -24px;
  .replyContainer {
    .ant-popover-inner-content {
      padding: 0;
    }
    .replyItem {
      cursor: pointer;
      height: 40px;
      line-height: 40px;
      padding: 0 16px;
      &:hover {
        background: #f7f7f7;
      }
    }
  }
  .fstReplyBtn {
    z-index: 9;
    position: absolute;
    right: 10px;
    bottom: 4px;
    .fastBtn {
      cursor: pointer;
    }
    .fastBtn:hover {
      color: #007aff;
    }
  }
`;
function EditLog({ onSuccess, taskInfo }, ref) {
  const { list: replyList } = useReplyList({ pageNo: 1, pageSize: 9999 });
  const inputRef = useRef();
  const tourObjRef = useRef();
  const [form] = Form.useForm();
  const [log, setLog] = useState({ show: false, patrolRecordId: 0, taskId: 0 });
  const [attr, setAttr] = useState([]);
  const [defaultTourInfo, setDefaultInfo] = useState({
    defaultUserId: "",
    defaultUserLabel: "",
    defaultClassId: "",
    defaultClassName: "",
    defaultGradeId: "",
    defaultGradeName: "",
  });

  const [active, setActive] = useState(1);
  const [state, dispatch] = useContext(FormContext);
  const [temp, setTemp] = useState({
    formInfoId: "",
    formInfoName: "",
    formTemplateAnswerId: "",
    formTemplateInfoId: "",
    useTable: 2,
    refresh: false,
  });
  const { formStr, anwser } = useTableAndAnswer(
    temp.formInfoId,
    temp.formTemplateInfoId,
    temp.formTemplateAnswerId,
    temp.refresh
  );

  useEffect(() => {
    if (!formStr || !anwser) return;
    const data = { ...anwser };
    if (data.id) {
      delete data.id;
      delete data.form_template_use_id;
      delete data.user_id;
    }
    dispatch({
      type: "INIT",
      payload: {
        editable: log.patrolRecordId ? true : false,
        formInfoId: temp.formInfoId, // 准备答题ID
        formAnswerId: anwser.id, // 答题ID
        // 模板相关
        tempId: temp.formTemplateInfoId,
        tempStr: formStr,
        name: temp.formInfoName,
        data,
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formStr, anwser, temp, log.patrolRecordId]);

  useImperativeHandle(ref, () => ({
    showDrawer(patrolRecordId, taskId) {
      setActive(1);
      if (patrolRecordId) {
        initPatrolDetail(patrolRecordId);
        /* 编辑 */
      } else {
        form.setFields([
          {
            name: "time",
            value: moment(),
          },
        ]);
      }
      setLog({ show: true, patrolRecordId, taskId });
    },
  }));

  const initPatrolDetail = async (patrolRecordId) => {
    const result = await selectPatrolRecordDetail({ patrolRecordId });
    if (result.code === 0) {
      const {
        recordedUserName,
        recordedUserId,
        patrolTime,
        patrolComment,
        patrolRecordAttrList,
        formInfoId,
        formInfoName,
        formTemplateAnswerId,
        formTemplateInfoId,
        useTable,
        classId: defaultClassId,
        className: defaultClassName,
        gradeId: defaultGradeId,
        gradeName: defaultGradeName,
      } = result.data;
      if (useTable === 1) {
        setTemp({
          formInfoId,
          formInfoName,
          formTemplateAnswerId,
          formTemplateInfoId,
          useTable,
          refresh: !temp.refresh,
        });
      }
      const attr = patrolRecordAttrList.map((item) => ({
        attrPath: item.attrPath,
        attrPic: item.attrPic,
        attrType: item.attrType,
        patrolRecordId: item.patrolRecordId,
        patrolRecordAttrId: item.patrolRecordAttrId,
      }));
      setAttr(attr);

      setDefaultInfo({
        defaultUserId: recordedUserId,
        defaultUserLabel: recordedUserName,
        defaultClassId,
        defaultClassName,
        defaultGradeId,
        defaultGradeName,
      });

      form.setFields([
        {
          name: "time",
          value: moment(patrolTime),
        },
        {
          name: "patrolComment",
          value: patrolComment,
        },
      ]);
    }
  };

  const handleCloseDrader = () => {
    form.resetFields();
    setAttr([]);
    setDefaultInfo({
      defaultUserId: "",
      defaultUserLabel: "",
      defaultClassId: "",
      defaultClassName: "",
      defaultGradeId: "",
      defaultGradeName: "",
    });
    setLog({ ...log, show: false });
  };
  const beforeUpload = (file) => {
    const isImage = file.type.indexOf("image/") !== -1;
    const isVideo = file.type.indexOf("video/") !== -1;
    const isLt100M = file.size / 1024 / 1024 < 100;
    if (!isLt100M && isVideo) {
      message.error("视频文件大小小于100M");
    }
    if (!(isImage || isVideo)) {
      message.error("请上传正确的格式");
    }
    return (isImage || isVideo) && isLt100M;
  };
  const fileChange = async (e) => {
    if (!beforeUpload(e.target.files[0])) {
      e.target.value = "";
      return;
    }
    $showLoading("上传中...");
    let result = await $upload(e);
    $hideLoading();
    if (!result) return;
    if (!result.screenUrl) {
      // 图片
      setAttr([
        ...attr,
        {
          attrPath: result.fileUrl,
          attrPic: "",
          attrType: 1,
          patrolRecordId: log.patrolRecordId,
          patrolRecordAttrId: 0,
        },
      ]);
    } else {
      // video
      setAttr([
        ...attr,
        {
          attrPath: result.fileUrl,
          attrPic: result.screenUrl,
          attrType: 2,
          patrolRecordId: log.patrolRecordId,
          patrolRecordAttrId: 0,
        },
      ]);
    }
  };
  const handleDeleteAttr = (index) => {
    const newAttr = [...attr];
    newAttr.splice(index, 1);
    setAttr(newAttr);
  };
  const submit = async () => {
    const tourInfo = tourObjRef.current.validate();
    const values = await form.validateFields();
    if (!tourInfo) return;
    if (!values) return;

    const startTime = taskInfo ? taskInfo.startTime : null;
    const endTime = taskInfo ? taskInfo.endTime + 86400000 : null;
    if (!startTime || !endTime) {
      return message.error("系统繁忙，请重试");
    }
    const time = values.time.unix() * 1000;
    if (time < startTime || time > endTime) {
      return message.error("请在规定时间内提交巡课记录");
    }
    const data = {
      patrolComment: values.patrolComment,
      recordedUserName: tourInfo.userInfo.label,
      recordedUserId: tourInfo.userInfo.value,
      patrolRecordId: log.patrolRecordId,
      taskId: log.taskId,
      patrolRecordAttrList: attr,
      patrolTime: moment(values.time).format("YYYY-MM-DD HH:mm:ss"),
      classId: tourInfo.classInfo.value ? tourInfo.classInfo.value[2] : "",
      className: tourInfo.classInfo.label,
      gradeId: tourInfo.gradeInfo.value ? tourInfo.gradeInfo.value[1] : "",
      gradeName: tourInfo.gradeInfo.label,
    };
    let result;
    if (log.patrolRecordId) {
      result = await updateMyPatrolRecord(data);
    } else {
      result = await addMyPatrolRecord(data);
    }
    if (result.code === 0) {
      message.success("保存成功");
      onSuccess();
      handleCloseDrader();
    }
  };

  const [prevInfo, setPreview] = useState({
    show: false,
    list: [],
    index: -1,
  });

  const handle2Preview = (index) => {
    const list = attr.map((item) => item.attrPath);
    setPreview({
      list,
      show: true,
      index,
    });
  };
  const handle2ClosePreview = () => {
    setPreview({
      show: false,
      list: [],
      index: -1,
    });
  };
  const hasTableTab = useMemo(() => {
    if (!log.patrolRecordId) return false;
    if (temp.useTable === 1 && temp.formTemplateAnswerId) return true;
    return false;
  }, [log, temp]);

  const saveAnswer = async () => {
    const result = await updateAnswerForRepeat(
      temp.formInfoId,
      temp.formTemplateAnswerId,
      state.data
    );
    await savePatrolRecordEditRecord({
      patrolRecordId: log.patrolRecordId,
      operationType: 3,
    });
    if (result.code === 0) {
      message.success("保存成功");
    }
  };
  /* 时间变动 */
  function onTimeChange(_, time) {
    const classInfo = tourObjRef.current.getClassInfo();
    if (!classInfo.value) return;
    if (!time) return;
    getTeacherInfo(classInfo.value[2], time);
  }
  /* 班级变动 */
  function onClassInfoChange(node) {
    let time = form.getFieldValue("time");
    if (!time) return;
    if (!node[2]) return;
    time = moment(time).format("YYYY-MM-DD HH:mm:ss");
    getTeacherInfo(node[2], time);
  }
  async function getTeacherInfo(nodeId, time) {
    const result = await getBaseCourseUser({
      time,
      nodeId,
      type: 4, // 节点类型 1.录播 2.监控 3.教室 4.班级
    });
    if (result.code === 0 && result.data) {
      const info = result.data[0];
      if (!info) {
        setDefaultInfo((pre) => ({
          ...pre,
          defaultUserId: "",
          defaultUserLabel: "",
          defaultGradeId: "",
          defaultGradeName: "",
        }));
        return;
      }
      setDefaultInfo((pre) => ({
        ...pre,
        defaultUserId: info.subjectTeacherId,
        defaultUserLabel: info.subjectTeacherName,
        defaultGradeId: info.gradeId,
        defaultGradeName: info.gradeName,
      }));
    }
  }

  return (
    <Drawer
      destroyOnClose
      title={`${log.patrolRecordId ? "编辑" : "添加"}`}
      width={480}
      closable={false}
      visible={log.show}
      extra={
        <CloseOutlined onClick={handleCloseDrader} className="cursor-pointer" />
      }
    >
      <input
        ref={(c) => (inputRef.current = c)}
        onChange={fileChange}
        type="file"
        className="hidden"
      />
      <Tab>
        <TabItem
          onClick={() => {
            setActive(1);
          }}
          className={active === 1 ? "active" : ""}
        >
          基本信息
        </TabItem>
        {hasTableTab && (
          <TabItem
            onClick={() => {
              setActive(3);
            }}
            className={active === 3 ? "active" : ""}
          >
            量表
          </TabItem>
        )}
      </Tab>
      {active === 1 && (
        <Form
          form={form}
          labelCol={{ span: 3 }}
          wrapperCol={{ span: 21 }}
          autoComplete="off"
        >
          <TourObjForm
            ref={tourObjRef}
            {...taskInfo?.patrolObject}
            {...defaultTourInfo}
            onClassInfoChange={onClassInfoChange}
          >
            <Form.Item
              label="时间"
              name="time"
              rules={[{ required: true, message: "请选择时间" }]}
            >
              <DatePicker
                onChange={onTimeChange}
                placeholder="请选择时间"
                className="w-full"
                showTime
                format="YYYY-MM-DD HH:mm:ss"
              />
            </Form.Item>
          </TourObjForm>

          <Form.Item
            label="评价"
            name="patrolComment"
            rules={[{ required: true, message: "请输入评价" }]}
          >
            <Input.TextArea
              style={{ paddingBottom: 30 }}
              maxLength={200}
              rows={4}
              placeholder="请输入评价"
            />
          </Form.Item>
          <FastCommentBox id="fastCommentBox">
            <div className="fstReplyBtn">
              <Popover
                overlayClassName="replyContainer"
                getPopupContainer={() =>
                  document.getElementById("fastCommentBox")
                }
                placement="bottomRight"
                content={
                  <div
                    className="w-60 relative overflow-y-auto"
                    style={{ height: 120 }}
                  >
                    {replyList.length > 0 ? (
                      replyList.map((item) => (
                        <div
                          className="h-10 replyItem truncate"
                          key={item.id}
                          onClick={() => {
                            const previousValue =
                              form.getFieldValue("patrolComment");
                            if (previousValue) {
                              form.setFieldsValue({
                                patrolComment: previousValue + item.content,
                              });
                            } else {
                              form.setFieldsValue({
                                patrolComment: item.content,
                              });
                            }
                          }}
                        >
                          {item.content}
                        </div>
                      ))
                    ) : (
                      <YsEmpt msg="暂无快捷评语" />
                    )}
                  </div>
                }
                title={<span className="font-bold text-black">快捷评语</span>}
              >
                <div className="fastBtn">
                  <MessageOutlined className="text-sm mr-1" />
                  <span>快捷评语</span>
                </div>
              </Popover>
            </div>
          </FastCommentBox>

          <Form.Item label="截图">
            <div className="mt-1 mb-2">
              <div
                onClick={() => inputRef.current.click()}
                className="inline-block mr-3 cursor-pointer"
                style={{ color: "#007AFF" }}
              >
                <PlusOutlined />
                <span> 添加</span>
              </div>
              <span style={{ color: "#BFBFBF" }}>视频文件不超过100mb</span>
            </div>
            <UploadList>
              {attr.map((item, index) => (
                <UploadItem key={index}>
                  <img
                    className="w-full h-full object-cover"
                    src={$isCdn(item.attrPic ? item.attrPic : item.attrPath)}
                    alt=""
                  />
                  <div className="cover absolute left-0 top-0 right-0 bottom-0">
                    <div className="flex h-full justify-center items-center">
                      <i
                        onClick={() => handle2Preview(index)}
                        className="iconfont icon-eyes text-white mr-2 text-2xl cursor-pointer"
                      ></i>
                      <i
                        onClick={() => handleDeleteAttr(index)}
                        className="iconfont icon-del text-white text-2xl cursor-pointer"
                      ></i>
                    </div>
                  </div>
                </UploadItem>
              ))}
            </UploadList>
          </Form.Item>

          <Form.Item wrapperCol={{ offset: 3, span: 16 }}>
            <Button type="primary" onClick={submit}>
              确定
            </Button>
          </Form.Item>
        </Form>
      )}

      {active === 3 && (
        <div style={{ paddingBottom: 60 }}>
          <FormRender />
          <FormBtton>
            <Space>
              <Button onClick={handleCloseDrader}>取消</Button>
              <Button onClick={saveAnswer} type="primary">
                提交
              </Button>
            </Space>
          </FormBtton>
        </div>
      )}

      {prevInfo.show && (
        <PreviewTool onClose={handle2ClosePreview} {...prevInfo} />
      )}
    </Drawer>
  );
}

export default forwardRef(EditLog);
