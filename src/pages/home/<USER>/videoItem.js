import React, { useEffect, useMemo, useRef, useContext } from "react";
import dayjs from "dayjs";
import flvjs from "flv.js";
import Hls from "hls.js";
import { Tooltip } from "antd";
import { $fileType, tryPcLink, webrtcPlayer } from "@/tools";
import { useIactiveLoop, useWatermark} from "@/hooks";
import styled from "styled-components";
import { PrevContext } from "./../components/prevContext";
import { MonitorTips } from "./../components/monitorTip";
import { getBaseCourseUser, getPatrolCourse } from "@/api";
import { PresetStatusColorTypes } from "antd/lib/_util/colors";
// 1,   4,  9,  16
// 100, 50, 33, 25
const windMap = {
  1: "100%",
  2: "50%",
  3: "75%",
  4: "50%",
  6: "33.33%",
  9: "33.33%",
  16: "25%",
};

const Root = styled.div`
  display: inline-block;
  aspect-ratio: 16 / 9;
  width: ${(props) => {
    if (props.windNum === 3) {
      if (props.index === 0) {
        return "66.66%";
      } else {
        return "33.33%";
      }
    }
    return windMap[props.windNum];
  }};
  border: ${(props) =>
    props.active ? "4px solid #FFAA00" : "4px solid transparent"};
  position: ${(props) => {
    if (props.windNum === 3) {
      if (props.index === 2 || props.index === 1) {
        return "absolute";
      }
    }
  }};
  right: ${(props) => {
    if (props.windNum === 3) {
      if (props.index === 2 || props.index === 1) {
        return "0";
      }
    }
  }};
  top: ${(props) => {
    if (props.windNum === 3) {
      if (props.index === 1) {
        return "0";
      }
    }
  }};
  bottom: ${(props) => {
    if (props.windNum === 3) {
      if (props.index === 2) {
        return "0";
      }
    }
  }};
  height: ${(props) => {
    if (props.windNum === 3) {
      if (props.index === 2 || props.index === 1) {
        return "50%";
      }
    }
  }};
`;
const VideoContainer = styled.div`
  position: relative;
  height: 100%;
  /* padding-top: ${(props) => (props.isFullscreen ? 0 : "56.25%")}; */
  /* height: ${(props) => (props.isFullscreen ? "100%" : "0")}; */

  video::-webkit-media-controls-fullscreen-button {
    display: ${(props) => (props.hideFullscreenButton ? 'none !important' : 'auto')};
  }
  
  video::-moz-media-controls-fullscreen-button {
    display: ${(props) => (props.hideFullscreenButton ? 'none !important' : 'auto')};
  }
  video {
    z-index: 1;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: #242424;
    object-fit: ${(props) => (props.isFullscreen ? "contain" : "fill")};
  }
  &:hover .close {
    display: block;
  }
  .close {
    display: none;
    cursor: pointer;
    text-align: center;
    line-height: 24px;
    border-radius: 2px;
    font-size: 12px;
    right: 8px;
    top: 8px;
    width: 44px;
    height: 24px;
    background: rgba(0, 0, 0, 0.65);
  }
  &:hover .turn {
    display: block;
  }
  .turn {
    display: none;
    cursor: pointer;
    line-height: 104px;
    font-size: 18px;
    border-radius: 2px;
    background: rgba(0, 0, 0, 0.65);
    width: 224px;
  }
  .nextUrl {
    width: 60px;
    right: 60px;
  }
  .doubleUrl {
    text-align: center;
    line-height: 24px;
    border-radius: 2px;
    font-size: 12px;
    left: 8px;
    top: 8px;
    width: 30px;
    height: 24px;
    background: rgba(0, 0, 0, 0.65);
    i {
      display: inline-block;
      transform: rotate(180deg);
    }
  }
  .document-container{
    position: relative;
    width: 100%;
    height: 100%;
  }
`;

function VideoItem(props) {
  const [prevState, prevDispatch] = useContext(PrevContext);
  const videoRef = useRef();
  const {
    active,
    windNum,
    openWaterMark,
    info,
    onChoose,
    isFullscreen,
    onClose,
    onToggleUrl,
    index,
    onChangeIndex,
  } = props;

  const showTurnBtn = useMemo(() => {
    if (windNum !== 3) return false;
    if (!info.url) return false;
    if (index === 0) return false;
    return true;
  }, [windNum, info.url, index]);

  const showNextUrl = useMemo(() => {
    if (info.urlList && info.urlList.length > 1) {
      return true;
    }
    return false;
  }, [info]);
  useIactiveLoop(info);
  useEffect(() => {
    let videoElement = videoRef.current;
    videoElement.addEventListener("canplaythrough", () => {
      videoElement.play();
    });
    let webrtcPlay = null;
    let hls = null;
    let flvPlayer = null;
    let lastDecodedFrame = 0;
    let timer = null;
    function destroy() {
      if (webrtcPlay) {
        webrtcPlay.close();
        webrtcPlay = null;
      }
      if (hls) {
        hls.destroy();
        hls = null;
      }
      if (flvPlayer) {
        flvPlayer.destroy();
        flvPlayer = null;
      }
      if (timer) {
        clearTimeout(timer);
      }
    }
    function startPlayFlv() {
      destroy();
      flvPlayer = flvjs.createPlayer(
        {
          type: "flv",
          url: info.url,
          isLive: true,
        },
        {
          enableStashBuffer: false, // 關閉IO隱藏緩衝區
          stashInitialSize: 128,
        }
      );
      flvPlayer.attachMediaElement(videoElement);
      flvPlayer.load();
      flvPlayer.play();
      flvPlayer.on(flvjs.Events.ERROR, () => {
        timer = setTimeout(startPlayFlv, 3000);
      });

      flvPlayer.on("statistics_info", function (res) {
        if (lastDecodedFrame === 0) {
          this.lastDecodedFrame = res.decodedFrames;
          return;
        }
        if (lastDecodedFrame !== res.decodedFrames) {
          lastDecodedFrame = res.decodedFrames;
        } else {
          lastDecodedFrame = 0;
          destroy();
          timer = setTimeout(startPlayFlv, 3000);
        }
      });

      videoElement.addEventListener("progress", () => {
        if (!flvPlayer || !flvPlayer.buffered) return;
        if (flvPlayer.buffered.length === 0) return;
        let end = flvPlayer.buffered.end(0);
        let delta = end - flvPlayer.currentTime;
        // console.log("--deltadelta", delta);
        // if (delta > 10 || delta < 0) {
        //   flvPlayer.currentTime = flvPlayer.buffered.end(0) - 1;
        //   return;
        // }
        if (delta > 1) {
          videoElement.playbackRate = 1.1;
        } else {
          videoElement.playbackRate = 1;
        }
      });
    }
    function startPlayHls() {
      destroy();
      if (Hls.isSupported()) {
        tryPcLink(info.url).then((data) => {
          hls = new Hls();
          hls.loadSource(data);
          hls.attachMedia(videoElement);
        });
      }
    }

    if ($fileType(info.url) === "m3u8") {
      startPlayHls();
    } else if ($fileType(info.url) === "flv") {
      startPlayFlv();
    } else if (info.url.includes("webrtc")) {
      webrtcPlay = webrtcPlayer(info.url, videoElement);
    }

    return () => {
      destroy();
      videoElement.removeEventListener("canplaythrough", () => {
        videoElement.play();
      });
    };
  }, [info.url]);

  const handleChoosedVideo = (e) => {
    e.preventDefault();
    onChoose();
  };
  const handleNextUrl = (e) => {
    e.preventDefault();
    onToggleUrl(info);
  };
  const isRoomTabAndFreeMode =
    prevState.isClassRoom && prevState.patrolFreeSetting?.freeMode === 102;

  const showMonitorSwtchTips =
    prevState.isMonitorSwitch && info.monitorBindClassInfo;

  useEffect(() => {
    if (!active) return;
    if (!info.classRoomId || !info.type) {
      prevDispatch({
        type: "change_state",
        payload: {
          classRoomInfo: null,
        },
      });
      return;
    }
    if (!info.isPatrol) {
      getBaseCourseUser({
        time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
        nodeId: info.classRoomId,
        type: info.type, // 节点类型 1.录播 2.监控 3.教室 4.班级
      }).then((data) => {
        if (data.code === 0) {
          prevDispatch({
            type: "change_state",
            payload: {
              classRoomInfo: data.data[0],
            },
          });
        }
      });
    } else {
      getPatrolCourse({
        id: info.classRoomId,
        type: info.type, // 节点类型 1.录播 2.监控 3.教室 4.班级
      }).then((data) => {
        if (data.code === 0) {
          prevDispatch({
            type: "change_state",
            payload: {
              classRoomInfo: data.data[0],
            },
          });
        }
      });
    }


  }, [active, info]);
  function onToggleIndex(e) {
    e.stopPropagation();
    onChangeIndex(index);
  }

  const watermarkRef = useWatermark(openWaterMark == 1);

  return (
    <Root
      onClick={handleChoosedVideo}
      active={active}
      windNum={windNum}
      index={index}
    >
      <VideoContainer active={active} isFullscreen={isFullscreen} hideFullscreenButton={openWaterMark === 1}>
        <video
          controls={info.url ? true : false}
          ref={(c) => (videoRef.current = c)}
          muted
        ></video>

        {showNextUrl && (
          <Tooltip
            placement="top"
            title={info.urlList.length > 2 ? "多画面" : "双画面"}
            getPopupContainer={() =>
              document.getElementById("videoListContainer")
            }
          >
            <div className="doubleUrl text-white absolute z-20">
              <i className="iconfont icon-mix" />
            </div>
          </Tooltip>
        )}
        {showNextUrl && (
          <div
            onClick={handleNextUrl}
            className="close nextUrl text-white absolute z-20"
          >
            画面切换
          </div>
        )}

        {/* 关闭 */}
        {info.url && (
          <div
            onClick={() => onClose(info)}
            className="close text-white absolute z-20"
          >
            关闭
          </div>
        )}
        {/* 转为主画面 */}
        {showTurnBtn && (
          <div
            onClick={onToggleIndex}
            className="turn  text-white absolute left-1/2 top-1/2 w-full text-center transform -translate-x-2/4 -translate-y-2/4 z-10"
          >
            转为主画面
          </div>
        )}
        {/* 无信号 */}
        {!info.url && (
          <div className="text-white absolute left-1/2 top-1/2 w-full text-center transform -translate-x-2/4 -translate-y-2/4 z-10 text-base">
            {isRoomTabAndFreeMode ? "暂无画面" : "请选择通道"}
          </div>
        )}
        {showMonitorSwtchTips && <MonitorTips {...info.monitorBindClassInfo} />}
        
        {/* 水印 */}
        <div ref={watermarkRef} className="document-container"></div>
      </VideoContainer>
    </Root>
  );
}

export default VideoItem;
