import React, { useEffect, useMemo, useRef, useContext } from "react";
import flvjs from "flv.js";
import Hls from "hls.js";
import { Tooltip } from "antd";
import { $fileType, tryPcLink, webrtcPlayer } from "@/tools";
import { useIactiveLoop } from "@/hooks";
import styled from "styled-components";
import { PrevContext } from "./../components/prevContext";
import { MonitorTips } from "./../components/monitorTip";
// 1,   4,  9,  16
// 100, 50, 33, 25
const windMap = {
  1: "100%",
  4: "50%",
  6: "33.33%",
  9: "33.33%",
  16: "25%",
};

const Root = styled.div`
  display: inline-block;
  width: ${(props) => windMap[props.windNum]};
  border: ${(props) =>
    props.active ? "4px solid #FFAA00" : "4px solid transparent"};
`;
const VideoContainer = styled.div`
  position: relative;
  padding-top: ${(props) => (props.isFullscreen ? 0 : "56.25%")};
  height: ${(props) => (props.isFullscreen ? "100%" : "0")};

  video {
    z-index: 1;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: #242424;
    object-fit: fill;
  }
  &:hover .close {
    display: block;
  }
  .close {
    display: none;
    cursor: pointer;
    text-align: center;
    line-height: 24px;
    border-radius: 2px;
    font-size: 12px;
    right: 8px;
    top: 8px;
    width: 44px;
    height: 24px;
    background: rgba(0, 0, 0, 0.65);
  }
  .nextUrl {
    width: 60px;
    right: 60px;
  }
  .doubleUrl {
    text-align: center;
    line-height: 24px;
    border-radius: 2px;
    font-size: 12px;
    left: 8px;
    top: 8px;
    width: 30px;
    height: 24px;
    background: rgba(0, 0, 0, 0.65);
    i {
      display: inline-block;
      transform: rotate(180deg);
    }
  }
`;

function VideoItem(props) {
  const [prevState, prevDispatch] = useContext(PrevContext);
  const videoRef = useRef();
  const {
    active,
    windNum,
    info,
    onChoose,
    isFullscreen,
    onClose,
    onToggleUrl,
  } = props;

  const showNextUrl = useMemo(() => {
    if (info.urlList && info.urlList.length > 1) {
      return true;
    }
    return false;
  }, [info]);
  useIactiveLoop(info);
  useEffect(() => {
    let videoElement = videoRef.current;
    videoElement.addEventListener("canplaythrough", () => {
      videoElement.play();
    });
    let webrtcPlay = null;
    let hls = null;
    let flvPlayer = null;
    let lastDecodedFrame = 0;
    let timer = null;
    function destroy() {
      if (webrtcPlay) {
        webrtcPlay.close();
        webrtcPlay = null;
      }
      if (hls) {
        hls.destroy();
        hls = null;
      }
      if (flvPlayer) {
        // flvPlayer.destroy();
        // flvPlayer = null;

        flvPlayer.unload();
        flvPlayer.detachMediaElement();
        flvPlayer.destroy();
        flvPlayer = null;
      }
      if (timer) {
        clearTimeout(timer);
      }
    }
    function startPlayFlv() {
      destroy();
      flvPlayer = window.mpegts.createPlayer(
        {
          type: "flv", // could also be mpegts, m2ts, flv
          isLive: true,
          url: info.url,
        },
        {
          enableWorker: true,
          lazyLoadMaxDuration: 3 * 60,
          seekType: "range",
          liveBufferLatencyChasing: true,
        }
      );
      flvPlayer.attachMediaElement(videoElement);
      flvPlayer.load();
      flvPlayer.play();

      flvPlayer.on(window.mpegts.Events.ERROR, (e) => {
        console.log("error", e);
      });
      flvPlayer.on(window.mpegts.Events.STATISTICS_INFO, (e) => {
        console.log("解码帧", e.decodedFrames);
      });
      // flvPlayer.on(flvjs.Events.ERROR, () => {
      //   timer = setTimeout(startPlayFlv, 3000);
      // });

      // flvPlayer.on("statistics_info", function (res) {
      //   if (lastDecodedFrame === 0) {
      //     this.lastDecodedFrame = res.decodedFrames;
      //     return;
      //   }
      //   if (lastDecodedFrame !== res.decodedFrames) {
      //     lastDecodedFrame = res.decodedFrames;
      //   } else {
      //     lastDecodedFrame = 0;
      //     destroy();
      //     timer = setTimeout(startPlayFlv, 3000);
      //   }
      // });

      // videoElement.addEventListener("progress", () => {
      //   if (!flvPlayer || !flvPlayer.buffered) return;
      //   if (flvPlayer.buffered.length === 0) return;
      //   let end = flvPlayer.buffered.end(0);
      //   let delta = end - flvPlayer.currentTime;
      //   console.log("--deltadelta", delta);
      //   // if (delta > 10 || delta < 0) {
      //   //   flvPlayer.currentTime = flvPlayer.buffered.end(0) - 1;
      //   //   return;
      //   // }
      //   if (delta > 1) {
      //     videoElement.playbackRate = 1.1;
      //   } else {
      //     videoElement.playbackRate = 1;
      //   }
      // });
    }
    function startPlayHls() {
      destroy();
      if (Hls.isSupported()) {
        tryPcLink(info.url).then((data) => {
          hls = new Hls();
          hls.loadSource(data);
          hls.attachMedia(videoElement);
        });
      }
    }

    if ($fileType(info.url) === "m3u8") {
      startPlayHls();
    } else if ($fileType(info.url) === "flv") {
      startPlayFlv();
    } else if (info.url.includes("webrtc")) {
      webrtcPlay = webrtcPlayer(info.url, videoElement);
    }

    return () => {
      destroy();
      // videoElement.removeEventListener("canplaythrough", () => {
      //   videoElement.play();
      // });
    };
  }, [info.url]);

  const handleChoosedVideo = (e) => {
    e.preventDefault();
    onChoose();
  };
  const handleNextUrl = (e) => {
    e.preventDefault();
    onToggleUrl(info);
  };
  const isRoomTabAndFreeMode =
    prevState.isClassRoom && prevState.patrolFreeSetting?.freeMode === 102;

  const showMonitorSwtchTips =
    prevState.isMonitorSwitch && info.monitorBindClassInfo;
  return (
    <Root onClick={handleChoosedVideo} active={active} windNum={windNum}>
      <VideoContainer active={active} isFullscreen={isFullscreen}>
        <video
          controls={info.url ? true : false}
          ref={(c) => (videoRef.current = c)}
          muted
        ></video>

        {showNextUrl && (
          <Tooltip
            placement="top"
            title={info.urlList.length > 2 ? "多画面" : "双画面"}
            getPopupContainer={() =>
              document.getElementById("videoListContainer")
            }
          >
            <div className="doubleUrl text-white absolute z-20">
              <i className="iconfont icon-mix" />
            </div>
          </Tooltip>
        )}
        {showNextUrl && (
          <div
            onClick={handleNextUrl}
            className="close nextUrl text-white absolute z-20"
          >
            画面切换
          </div>
        )}

        {/* 关闭 */}
        {info.url && (
          <div
            onClick={() => onClose(info)}
            className="close text-white absolute z-20"
          >
            关闭
          </div>
        )}
        {/* 无信号 */}
        {!info.url && (
          <div className="text-white absolute left-1/2 top-1/2 w-full text-center transform -translate-x-2/4 -translate-y-2/4 z-10 text-base">
            {isRoomTabAndFreeMode ? "暂无画面" : "请选择通道"}
          </div>
        )}
        {showMonitorSwtchTips && <MonitorTips {...info.monitorBindClassInfo} />}
      </VideoContainer>
    </Root>
  );
}

export default VideoItem;
