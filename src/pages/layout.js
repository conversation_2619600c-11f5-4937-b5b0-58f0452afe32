import React, { useEffect, useState, createContext } from "react";
import { Outlet, useNavigate, useLocation } from "react-router-dom";
import CourseHead from "./common/courseHead";
import { getPermission } from "@/api";

export const MenuContext = createContext([]);
const menuList = [0, "/admin/tasks", "/admin/log", "/admin/setting", "/"];
function Index() {
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const [menu, setMenu] = useState([]);
  useEffect(() => {
    getPermission().then((res) => {
      if (res.code === 0 && res.data.menuIds) {
        setMenu(res.data.menuIds);
        const menus = res.data.menuIds;
        const menuMap = menus.map((item) => menuList[item]);
        /* 输入的有效链接 */
        if (menuList.includes(pathname)) {
          if (!menuMap.includes(pathname)) {
            navigate("403", { replace: true });
          }
        }
      } else {
        navigate("403", { replace: true });
      }
    });
  }, [navigate, pathname]);
  return (
    <MenuContext.Provider value={menu}>
      <CourseHead />
      <Outlet />
    </MenuContext.Provider>
  );
}
export default Index;
