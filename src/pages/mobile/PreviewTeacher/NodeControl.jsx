import {useRef,useState} from "react"
import styled from "styled-components"
import { Toast } from "antd-mobile"
import { useCountDown } from "ahooks"
import { getScreenShot, startRecordLive, stopRecordLive } from "@/api/upload"
import { fileCenterCallback } from "@/api"
import IMAGETIP  from "@/assets/images/mobile/tips.png"
const Root = styled.div`
  height: 52px;
  background: #fff;
  display: flex;
  align-items: center;
  color: #007AFF;
  .progress {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    background: rgba(0, 122, 255, 0.08);
    transition: width 1s;
  }
`

export default function NodeControl() {
  function handleScreenShot() {
    const videoEle = document.getElementById("videoPlayer");
    if(!videoEle) return;
    const playUrl = videoEle.dataset.url;
    if (!playUrl) {
      return Toast.show({
        icon: "fail",
        content: "没有播放地址",
      })
    }
    Toast.show({
      icon: "loading",
      content: "截图中...",
      duration: 0,
      maskClickable: false,
    })
    getScreenShot({ flvUrl: playUrl }).then((res) => {
      Toast.clear()
      if (res.code === 0 && res.data) {
        fileCenterCallback({
          fileSource: 4,
          filename: res.data,
          oldFilename: res.data,
          size: 0,
        }).then(res => {
          if (res.code === 0) {
            const content = <div className="flex items-center">
              <img width={16} height={16} src={IMAGETIP} alt="" />
              <span className="ml-1">截图已保存至“我的文件”</span>
              </div>
            Toast.show({
              content,
            })
          }
        })
      } else {
        return Toast.show({
          icon: "fail",
          content: "截图失败",
        })
      }
    })

  }

/* 视频录制 */
const taskRef = useRef()
const [activeTap, setActiveTap] = useState(false)
const [targetDate, setTargetDate] = useState()
const [countdown] = useCountDown({
  targetDate,
  onEnd: () => {
    setActiveTap(false)
    handleEndTap(15)
  },
})
const handleStartTap = () => {
  if (activeTap && taskRef.current) {
    const time = Math.round((15000 - countdown) / 1000)
    if (time > 5) {
      setTargetDate(undefined)
      setActiveTap(false)
      handleEndTap(time)
    } else {
      Toast.show({
        icon: "fail",
        content: "录制时间太短",
      })
    }
    return
  }

  const videoEle = document.getElementById("videoPlayer");
  if(!videoEle) return;
  const playUrl = videoEle.dataset.url;

  if (!playUrl) {
    return Toast.show({
      icon: "fail",
      content: "该通道没有播放地址",
    })
  }

  startRecordLive({ flvUrl: playUrl }).then((res) => {
    if (res.code === 0 && res.data) {
      taskRef.current = res.data
      setTargetDate(Date.now() + 15000)
      setActiveTap(true)
    } else {
      return Toast.show({
        icon: "fail",
        content: "录制失败",
      })
    }
  })
}
    const handleEndTap = (sec) => {
      Toast.clear()
      Toast.show({
        icon: "loading",
        content: "生成中...",
        duration: 0,
        maskClickable: false,
      })


      const videoEle = document.getElementById("videoPlayer");
      if(!videoEle) return;
      const playUrl = videoEle.dataset.url;

      const apis = [
        getScreenShot({ flvUrl: playUrl }),
        stopRecordLive({ processId: taskRef.current, sec }),
      ]
      Promise.all(apis)
        .then((data) => {
          if (!data[0].data || !data[1].data) {
            Toast.clear()
            Toast.show({
              icon: "success",
              content: "录制失败",
            })
            setTargetDate(undefined)
            setActiveTap(false)
            return
          }

          fileCenterCallback({
            fileSource: 4,
            filename: data[1].data,
            oldFilename: data[1].data,
            size: 0,
          }).then(res => {
            if (res.code === 0) {
              const content = <div className="flex items-center">
                <img width={16} height={16} src={IMAGETIP} alt="" />
                <span className="ml-1">录像已保存至“我的文件”</span>
                </div>
              Toast.show({
                content,
              })
            }
          })
          // const params = {
          //   fileUrl: data[1].data,
          //   coverUrl: data[0].data,
          // }
          // setAttrs((c) => [...c, params])
          // Toast.clear()
          setTargetDate(undefined)
          setActiveTap(false)
        })
        .catch((error) => {
          Toast.clear()
          Toast.show({
            icon: "success",
            content: "录制失败",
          })
          setTargetDate(undefined)
          setActiveTap(false)
        })
    }

  return <Root>
    <div onClick={handleScreenShot} className="flex-1 h-full flex items-center justify-center">
      <i className="iconfont iconxiangji mr-1 text-lg"></i>
      <span className="text-sm">截图</span>
    </div>
    <div onClick={handleStartTap} className="flex-1 h-full border-0 border-l border-gray-300 border-solid flex items-center justify-center relative"> 
    {activeTap && (
            <div
              style={{
                width:
                  countdown === 0
                    ? "0px"
                    : ((15000 - countdown) / 15000) * 100 + "%",
              }}
              className="progress"
            ></div>
          )}
      <i className="iconfont iconshipin2 mr-1 text-lg"></i>
      <span className="text-sm">录像
      {activeTap && (
              <span> {Math.round((15000 - countdown) / 1000)}s</span>
            )}
      </span>
    </div>
  </Root>
}