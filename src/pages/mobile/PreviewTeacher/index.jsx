import { useEffect, useState } from "react";
import Hls from "hls.js";
import { $getIsIOS, tryLink, tryPcLink } from "@/tools";
import { Empty } from "antd";
import { Toast } from "antd-mobile";
import { getPlayUrl, getMagicLive } from "@/api";
import styled from "styled-components";
import NodeControl from "./NodeControl";
import { useTeacherRoom, useTeacherRoomDevice } from "@/hooks";

const Root = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  #videoPlayer {
    width: 100%;
    height: 210px;
    background: #000;
    vertical-align: middle;
  }
  .tab {
    padding: 0 16px;
    height: 60px;
    display: flex;
    align-items: center;
    i {
      font-size: 24px;
      color: #b3b3b3;
      display: inline-block;
    }
    i.left {
      transform: rotate(180deg);
    }
    .title {
      padding: 0 10px;
      flex: 1;
      text-align: center;
      font-weight: 500;
      color: #1a1a1a;
      font-size: 16px;
    }
  }
  .nodeContainer {
    position: relative;
    padding: 12px;
    flex: 1;
    overflow: auto;
    background: #f2f2f6;
    .node {
      background: #fff;
      height: 60px;
      border-radius: 6px;
      padding: 0 16px;
      display: flex;
      align-items: center;
      color: #1a1a1a;
      font-size: 15px;
      border: 1px solid transparent;
    }
    .node.active {
      color: #4465f3;
      border-color: #4465f3;
      background: rgba(68, 101, 243, 0.07);
    }
    .node + .node {
      margin-top: 12px;
    }
  }
`;

async function getNodePlayer(belongs, nodeId) {
  const result = await getPlayUrl({
    belong: belongs,
    nodeId: nodeId,
  });
  if (result.code === 0) {
    return result.data;
  }
  return false;
}
let hls = null;

function onVideoPlay(playUrl) {
  if (hls) {
    hls.destroy();
    hls = null;
  }
  const isIOS = $getIsIOS();
  const videoEle = document.getElementById("videoPlayer");
  videoEle.setAttribute("data-url", playUrl);
  if (videoEle.canPlayType("application/vnd.apple.mpegurl") && isIOS) {
    tryLink(playUrl).then((data) => {
      videoEle.src = playUrl;
      videoEle.addEventListener("loadedmetadata", () => {
        videoEle.play();
      });
    });
  } else if (Hls.isSupported()) {
    tryPcLink(playUrl).then((data) => {
      hls = new Hls();
      hls.loadSource(playUrl);
      hls.attachMedia(videoEle);
      videoEle.addEventListener("loadedmetadata", () => {
        videoEle.play();
      });
    });
  }
}

export default function PreviewTeacher() {
  const rooms = useTeacherRoom();
  const [currentRoom, setCurrentRoom] = useState();
  const list = useTeacherRoomDevice(currentRoom?.classRoomId);
  const [currentMonitorInfo, setCurrentMonitorInfo] = useState({
    monitoringNodeId: "",
    playUrl: "",
  });
  /* 初始化 */
  if (!currentRoom && rooms[0]) {
    setCurrentRoom(rooms[0]);
  }
  function handlePrevRoom() {
    if (!currentRoom) return;
    const index = rooms.findIndex(
      (room) => room.classRoomId === currentRoom?.classRoomId
    );
    if (index === 0) {
      return Toast.show({
        icon: "fail",
        content: "已经是第一间教室",
      });
    }
    setCurrentRoom(rooms[index - 1]);
  }
  function handleNextRoom() {
    if (!currentRoom) return;
    const index = rooms.findIndex(
      (room) => room.classRoomId === currentRoom?.classRoomId
    );
    if (index === rooms.length - 1) {
      return Toast.show({
        icon: "fail",
        content: "已经是最后一间教室",
      });
    }
    setCurrentRoom(rooms[index + 1]);
  }

  async function onChoose({ belongs, monitoringNodeId }) {
    if (belongs === 40000) {
      getMagicLive({ roomId: monitoringNodeId }).then((result) => {
        if (result && result.code === 0 && result.data) {
          let urlList = result.data.liveUrls || [];
          if (result.data.success === 500) {
            urlList = urlList.filter((item) => item.status === 1);
          }

          if (urlList.length === 0) {
            Toast.error("暂无播放地址");
            return;
          }
          onVideoPlay(urlList[0].m3u8Url);
        }
      });
      return;
    }

    const data = await getNodePlayer(belongs, monitoringNodeId);
    if (!data || !Array.isArray(data)) {
      return Toast.show({
        icon: "fail",
        content: "服务器异常",
      });
    }
    const m3u8URL = data[0].pcUrl;
    if (!m3u8URL) {
      return Toast.show({
        icon: "fail",
        content: "播放地址异常",
      });
    }
    setCurrentMonitorInfo({
      monitoringNodeId: monitoringNodeId,
      playUrl: m3u8URL,
    });
    onVideoPlay(m3u8URL);
  }

  const nodeList = list.map((item) => (
    <div
      onClick={() => onChoose(item)}
      className={`node ${
        item.monitoringNodeId === currentMonitorInfo.monitoringNodeId
          ? "active"
          : ""
      }`}
      key={item.monitoringNodeId}
    >
      <span className="flex-1 pr-4 truncate">{item.name}</span>
      {item.online !== null && (
        <span
          style={{
            color: item.online === 1 ? "#17BE6B" : "#BFBFBF",
          }}
        >
          {item.online === 1 ? "在线" : "离线"}
        </span>
      )}
    </div>
  ));
  useEffect(() => {
    return () => {
      if (hls) {
        hls.destroy();
      }
    };
  }, []);
  return (
    <Root>
      <div>
        <video
          id="videoPlayer"
          playsInline
          muted
          webkitplaysinline="true"
          x5-playsinline="true"
          controls
          x5-video-player-type="h5-page"
        />
      </div>
      <div className="tab">
        {rooms.length > 1 && (
          <i onClick={handlePrevRoom} className="iconfont iconjiantou left"></i>
        )}
        <span className="title truncate">{currentRoom?.fullName}</span>
        {rooms.length > 1 && (
          <i onClick={handleNextRoom} className="iconfont iconjiantou"></i>
        )}
      </div>
      <div className="nodeContainer">
        {nodeList.length > 0 ? (
          nodeList
        ) : (
          <Empty
            className="absolute left-1/2 top-1/3 transform -translate-x-1/2 -translate-y-1/2"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}
      </div>
      <NodeControl />
    </Root>
  );
}
