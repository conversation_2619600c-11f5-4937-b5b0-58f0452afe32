import { useEffect, useMemo, useState } from "react";
import { getGradeStageTreeByOrgId, getStageGradeByOrgId } from "@/api";
import { Popup } from "antd-mobile";
import styled from "styled-components";

const PanelContainer = styled.div`
  height: 40vh;
  display: flex;
`;
const Panel = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  .title {
    text-align: center;
    font-weight: 500;
    height: 52px;
    line-height: 52px;
    font-size: 16px;
    color: #1a1a1a;
  }
  .list {
    flex: 1;
    overflow-y: auto;
    .item {
      line-height: 52px;
      text-align: center;
      font-size: 16px;
    }
    .item.active {
      background: #e0efff;
    }
  }
`;

async function getGradeClassInfo(type) {
  const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
  let result;
  if (type === "class") {
    result = await getGradeStageTreeByOrgId({ orgId: userInfo.orgId });
  } else {
    result = await getStageGradeByOrgId({ orgId: userInfo.orgId });
  }
  if (result.code === 0) {
    return result.data || [];
  }
}
function useClassOptions() {
  const [options, setOptions] = useState([]);
  useEffect(() => {
    getGradeClassInfo("class").then((optionList) => {
      setOptions(optionList);
    });
  }, []);
  return options;
}
function useGradeOptions() {
  const [options, setOptions] = useState([]);
  useEffect(() => {
    getGradeClassInfo().then((optionList) => {
      setOptions(optionList);
    });
  }, []);
  return options;
}

export function ClassPopup({ show, onHide, onChoose }) {
  const options = useClassOptions();
  const [stageId, setStage] = useState();
  const [gradeId, setGradeId] = useState();
  const [classId, setClassId] = useState();

  const gradeList = useMemo(() => {
    if (!stageId) return [];
    const targetGrade = options.find((item) => item.id === stageId);
    if (
      targetGrade &&
      targetGrade.children &&
      targetGrade.children.length > 0
    ) {
      return targetGrade.children;
    }
    return [];
  }, [stageId, options]);

  const classList = useMemo(() => {
    if (!gradeId) return [];
    const targetClass = gradeList.find((item) => item.id === gradeId);
    if (
      targetClass &&
      targetClass.children &&
      targetClass.children.length > 0
    ) {
      return targetClass.children;
    }
    return [];
  }, [gradeList, gradeId]);

  useEffect(() => {
    if (options && options.length === 1) {
      setStage(options[0].id);
    }
  }, [options]);
  return (
    <Popup
      visible={show}
      onMaskClick={onHide}
      bodyStyle={{
        borderTopLeftRadius: "16px",
        borderTopRightRadius: "16px",
      }}
    >
      {/* {options.length === 0 && (
        <div style={{ height: "30vh", overflowY: "scroll" }}>
          <YsEmpt msg="暂无数据" />
        </div>
      )} */}

      <PanelContainer>
        {options.length > 1 && (
          <Panel>
            <div className="title">阶段</div>
            <div className="list">
              {options.map((item) => (
                <div
                  onClick={() => {
                    setStage(item.id);
                    setGradeId();
                    setClassId();
                  }}
                  className={`item ${item.id === stageId ? "active" : ""}`}
                  key={item.id}
                >
                  {item.name}
                </div>
              ))}
            </div>
          </Panel>
        )}

        <Panel>
          <div className="title">年级</div>
          <div className="list">
            {gradeList.map((item) => (
              <div
                onClick={() => {
                  setGradeId(item.id);
                  setClassId();
                }}
                className={`item ${item.id === gradeId ? "active" : ""}`}
                key={item.id}
              >
                {item.name}
              </div>
            ))}
          </div>
        </Panel>
        <Panel>
          <div className="title">班级</div>
          <div className="list">
            {classList.map((item) => (
              <div
                onClick={() => {
                  setClassId(item.id);
                  onChoose({
                    stageId,
                    gradeId,
                    classId: item.id,
                    classLabel: item.name,
                  });
                }}
                className={`item ${item.id === classId ? "active" : ""}`}
                key={item.id}
              >
                {item.name}
              </div>
            ))}
          </div>
        </Panel>
      </PanelContainer>
    </Popup>
  );
}

export function GradePopup({ show, onHide, onChoose }) {
  const options = useGradeOptions();
  const [stageId, setStage] = useState();
  const [gradeId, setGradeId] = useState();

  const gradeList = useMemo(() => {
    if (!stageId) return [];
    const targetGrade = options.find((item) => item.id === stageId);
    if (
      targetGrade &&
      targetGrade.children &&
      targetGrade.children.length > 0
    ) {
      return targetGrade.children;
    }
    return [];
  }, [stageId, options]);

  useEffect(() => {
    if (options && options.length === 1) {
      setStage(options[0].id);
    }
  }, [options]);
  return (
    <Popup
      visible={show}
      onMaskClick={onHide}
      bodyStyle={{
        borderTopLeftRadius: "16px",
        borderTopRightRadius: "16px",
      }}
    >
      {/* {options.length === 0 && (
        <div style={{ height: "30vh", overflowY: "scroll" }}>
          <YsEmpt msg="暂无数据" />
        </div>
      )} */}

      <PanelContainer>
        <Panel>
          <div className="title">阶段</div>
          <div className="list">
            {options.map((item) => (
              <div
                onClick={() => {
                  setStage(item.id);
                  setGradeId();
                }}
                className={`item ${item.id === stageId ? "active" : ""}`}
                key={item.id}
              >
                {item.name}
              </div>
            ))}
          </div>
        </Panel>

        <Panel>
          <div className="title">年级</div>
          <div className="list">
            {gradeList.map((item) => (
              <div
                onClick={() => {
                  setGradeId(item.id);
                  onChoose({
                    stageId,
                    gradeId: item.id,
                    gradeLabel: item.name,
                  });
                }}
                className={`item ${item.id === gradeId ? "active" : ""}`}
                key={item.id}
              >
                {item.name}
              </div>
            ))}
          </div>
        </Panel>
      </PanelContainer>
    </Popup>
  );
}
