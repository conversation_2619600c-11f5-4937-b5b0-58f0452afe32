import React, { useState, useImper<PERSON><PERSON><PERSON><PERSON>, forwardRef } from "react"
import { Input, AutoComplete } from "antd"
import moment from "moment"
import { $hidePhone } from "@/tools"
import { DatePicker, Toast, Popup } from "antd-mobile"
import { MessageOutlined } from "@ant-design/icons"
import styled from "styled-components"
import { YsEmpt } from "@/components"
import { searchByUserName } from "@/api"
import { useReplyList } from "@/hooks"
import { ClassPopup, GradePopup } from "./classPopup"
const { Option } = AutoComplete

const Root = styled.div`
  .item {
    margin-bottom: 20px;
    .label {
      transform: translateY(-50%);
      top: 50%;
      color: rgba(144, 147, 153, 1);
    }
    .label.require {
      &::before {
        content: "*";
        position: absolute;
        left: -8px;
        transform: translateY(-50%);
        top: 50%;
        color: #ff4d4f;
        font-weight: bold;
      }
    }
  }
  .replyBtn {
    color: #1a1a1a;
    padding: 4px 8px;
    border: 1px solid #eaeaea;
    border-radius: 50px;
  }
`
const ReplyItem = styled.div`
  padding: 0 16px;
  height: 50px;
  line-height: 50px;
  color: #1a1a1a;
  border-bottom: 1px solid #ededed;
`
const now = new Date()
function AssessForm(props, ref) {
  const {
    classIsFill,
    classIsShow,
    gradeIsFill,
    gradeIsShow,
    personalIsFill,
    personalIsShow,
  } = props.patrolObject
  const [time, setTime] = useState("")
  const [remarks, setRemarks] = useState("")
  const [user, setUser] = useState({ name: "", id: "" })
  const [showDate, setShowDate] = useState(false)
  const [classInfo, setClassInfo] = useState({
    show: false,
    id: "",
    label: "",
  })
  const [gradeInfo, setGradeInfo] = useState({
    show: false,
    id: "",
    label: "",
  })

  const handleSelectDate = val => {
    setTime(moment(+val).format("YYYY-MM-DD HH:mm:ss"))
  }
  const handleFocus = e => {
    setShowDate(true)
  }

  const [options, setOptions] = useState([])
  const onSearch = async userName => {
    if (!userName) {
      setOptions([])
    } else {
      const result = await searchByUserName({ userName })
      if (result.code === 0) {
        const data = result.data.map(item => ({
          value: item.coreUserInfoId,
          label: item.userName,
          phone: item.phone,
        }))
        setOptions(data)
      }
    }
  }
  const onChange = e => {
    setUser({ id: "", name: e })
  }
  const onSelect = async value => {
    const target = options.find(item => item.value === value)
    setUser({
      name: target.label,
      id: target.value,
    })
  }
  useImperativeHandle(ref, () => ({
    onCheck,
    onreset,
  }))
  const onCheck = callback => {
    if (personalIsFill && !user.id) {
      Toast.show({
        icon: "fail",
        content: "请输入姓名",
      })
      return
    }
    if (classIsFill && !classInfo.id) {
      Toast.show({
        icon: "fail",
        content: "请选择班级",
      })
      return
    }
    if (gradeIsFill && !gradeInfo.id) {
      Toast.show({
        icon: "fail",
        content: "请选择年级",
      })
      return
    }

    if (
      personalIsShow &&
      gradeIsShow &&
      classIsShow &&
      !personalIsFill &&
      !gradeIsFill &&
      !classIsFill &&
      !user.id &&
      !classInfo.id &&
      !gradeInfo.id
    ) {
      Toast.show({
        icon: "fail",
        content: "姓名、班级、年级至少填一项",
      })
      return
    }

    if (!time) {
      Toast.show({
        icon: "fail",
        content: "请选择时间",
      })
      return
    }

    if (!remarks) {
      Toast.show({
        icon: "fail",
        content: "请填写评价",
      })
      return
    }
    callback({
      time,
      userName: user.name,
      userId: user.id,
      remarks,
      classInfo,
      gradeInfo,
    })
  }
  const onreset = () => {
    setUser({ id: "", name: "" })
    setRemarks("")
    setTime("")
    setClassInfo({
      show: false,
      id: "",
      label: "",
    })
    setGradeInfo({
      show: false,
      id: "",
      label: "",
    })
  }
  // ================
  const { list: replyList } = useReplyList({ pageNo: 1, pageSize: 999 })
  const [showReply, setReply] = useState(false)
  return (
    <Root>
      {personalIsShow === 1 && (
        <div className="item relative">
          <span
            className={`absolute label z-10 left-4 ${
              personalIsFill === 1 ? "require" : ""
            }`}
          >
            姓名
          </span>
          <AutoComplete
            size="large"
            className="w-full mobileAuto"
            onSelect={onSelect}
            onChange={onChange}
            onSearch={onSearch}
            value={user.name}
            placeholder="请输入"
            style={{ textAlign: "right" }}
          >
            {options.map(option => {
              return (
                <Option key={option.value}>
                  <div className="flex justify-between">
                    <span className="flex-1 truncate">{option.label}</span>
                    <span>{$hidePhone(option.phone)}</span>
                  </div>
                </Option>
              )
            })}
          </AutoComplete>
        </div>
      )}

      {classIsShow === 1 && (
        <div className="item relative">
          <span
            className={`absolute label z-10 left-4 ${
              classIsFill === 1 ? "require" : ""
            }`}
          >
            班级
          </span>
          <Input
            style={{ textAlign: "right", paddingLeft: "100px" }}
            onFocus={() =>
              setClassInfo(pre => ({
                ...pre,
                show: true,
              }))
            }
            size="large"
            readOnly
            value={classInfo.label}
            placeholder="请选择"
          />
        </div>
      )}

      {gradeIsShow === 1 && (
        <div className="item relative">
          <span
            className={`absolute label z-10 left-4 ${
              gradeIsFill === 1 ? "require" : ""
            }`}
          >
            年级
          </span>
          <Input
            style={{ textAlign: "right", paddingLeft: "100px" }}
            onFocus={() =>
              setGradeInfo(pre => ({
                ...pre,
                show: true,
              }))
            }
            size="large"
            readOnly
            value={gradeInfo.label}
            placeholder="请选择"
          />
        </div>
      )}

      <div className="item relative">
        <span className="absolute label require z-10 left-4">时间</span>
        <Input
          style={{ textAlign: "right", paddingLeft: "100px" }}
          onFocus={handleFocus}
          size="large"
          readOnly
          value={time}
          placeholder="请选择"
        />
      </div>

      <div className="item relative">
        <Input.TextArea
          value={remarks}
          onChange={e => setRemarks(e.target.value)}
          placeholder="请输入评价(必填)"
          rows={4}
          maxLength={200}
        />
        <div
          onClick={() => setReply(true)}
          className="absolute right-2 bottom-2 replyBtn"
        >
          <MessageOutlined />
          <span className="ml-1">快捷评语</span>
        </div>
      </div>

      <DatePicker
        visible={showDate}
        onClose={() => {
          setShowDate(false)
        }}
        defaultValue={now}
        precision="minute"
        onConfirm={handleSelectDate}
      />

      {/* 班级 */}
      <ClassPopup
        show={classInfo.show}
        onHide={() => {
          setClassInfo({
            show: false,
            id: "",
            label: "",
          })
        }}
        onChoose={({ stageId, gradeId, classId, classLabel }) => {
          setClassInfo({
            show: false,
            id: classId,
            label: classLabel,
          })
        }}
      />
      {/* 年级 */}
      <GradePopup
        show={gradeInfo.show}
        onHide={() => {
          setGradeInfo({
            show: false,
            id: "",
            label: "",
          })
        }}
        onChoose={({ stageId, gradeId, gradeLabel }) => {
          setGradeInfo({
            show: false,
            id: gradeId,
            label: gradeLabel,
          })
        }}
      />

      <Popup
        visible={showReply}
        onMaskClick={() => {
          setReply(false)
        }}
        bodyStyle={{
          borderTopLeftRadius: "16px",
          borderTopRightRadius: "16px",
        }}
      >
        <div style={{ height: "40vh", overflowY: "scroll" }}>
          {replyList.length ? (
            replyList.map(item => (
              <ReplyItem
                className="truncate"
                onClick={() => {
                  setRemarks(remarks + item.content)
                  setReply(false)
                }}
                key={item.id}
              >
                {item.content}
              </ReplyItem>
            ))
          ) : (
            <YsEmpt msg="暂无快捷评语" />
          )}
        </div>
      </Popup>
    </Root>
  )
}
export default forwardRef(AssessForm)
