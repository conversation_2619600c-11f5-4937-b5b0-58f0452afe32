import React, {
  useState,
  useImper<PERSON><PERSON><PERSON><PERSON>,
  forwardRef,
  useEffect,
} from "react";
import moment from "moment";
import dayjs from "dayjs";
import { $hidePhone } from "@/tools";
import { DatePicker, Toast, Popup, TextArea, Empty } from "antd-mobile";
import { MessageOutlined } from "@ant-design/icons";
import styled from "styled-components";
import { YsEmpt } from "@/components";
import { searchByUserName, getBaseCourseUser } from "@/api";
import { useReplyList } from "@/hooks";
import { ClassPopup, GradePopup } from "./classPopup";

const Root = styled.div``;
const ReplyItem = styled.div`
  padding: 0 16px;
  height: 50px;
  line-height: 50px;
  color: #1a1a1a;
  border-bottom: 1px solid #ededed;
`;

const FormItem = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e5e5e5;
  font-size: 16px;
  padding-bottom: 16px;
  margin-bottom: 16px;
  .title {
    color: #4d4d4d;
    position: relative;
  }
  .require {
    &::before {
      content: "*";
      position: absolute;
      right: -8px;
      transform: translateY(-50%);
      top: 50%;
      color: #ff4d4f;
      font-weight: bold;
    }
  }
  .label {
    color: #b3b3b3;
  }
  /* mark */
  &.mark {
    display: block;
    .head {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .replyBtn {
      color: #1a1a1a;
      font-size: 12px;
      padding: 4px 8px;
      border-radius: 50px;
      background: #f8f8f8;
    }
  }
  input {
    outline: none;
    border: none;
    text-align: right;
    ::placeholder {
      color: #b3b3b3;
    }
  }
`;
const UserList = styled.div`
  position: absolute;
  overflow: auto;
  border-radius: 4px;
  right: 16px;
  top: 30px;
  z-index: 99;
  box-shadow: 0px 0px 30px 0px rgba(1, 1, 1, 0.2);
  background: #fff;
  width: 260px;
  height: 310px;
  padding: 0 16px;
  .item-user + .item-user {
    border-top: 1px solid #e5e5e5;
  }
  .item-user {
    font-size: 16px;
    padding: 13px 0;

    color: #333333;
    .user {
      display: flex;
      align-items: center;
    }
    .org {
      margin-top: 10px;
      color: #808080;
    }
  }
`;

const now = new Date();
function AssessForm(props, ref) {
  const {
    classIsFill,
    classIsShow,
    gradeIsFill,
    gradeIsShow,
    personalIsFill,
    personalIsShow,
  } = props.patrolObject;
  const [time, setTime] = useState(() => {
    if (!props.showTime) {
      return dayjs().format("YYYY-MM-DD HH:mm:ss");
    }
    return "";
  });
  const [remarks, setRemarks] = useState("");
  const [user, setUser] = useState({ name: "", id: "" });
  const [showDate, setShowDate] = useState(false);
  const [classInfo, setClassInfo] = useState({
    show: false,
    id: "",
    label: "",
  });
  const [gradeInfo, setGradeInfo] = useState({
    show: false,
    id: "",
    label: "",
  });

  const [userList, setUserList] = useState({
    list: [],
    show: false,
  });

  const onSearch = async (userName) => {
    if (!userName) {
      setUserList({
        list: [],
        show: false,
      });
    } else {
      const result = await searchByUserName({ userName });
      if (result.code === 0) {
        setUserList({
          list: result.data,
          show: true,
        });
      }
    }
  };

  useImperativeHandle(ref, () => ({
    onCheck,
    onreset,
  }));
  const onCheck = (callback) => {
    if (personalIsFill && !user.id) {
      Toast.show({
        icon: "fail",
        content: "请输入姓名",
      });
      return;
    }
    if (classIsFill && !classInfo.id) {
      Toast.show({
        icon: "fail",
        content: "请选择班级",
      });
      return;
    }
    if (gradeIsFill && !gradeInfo.id) {
      Toast.show({
        icon: "fail",
        content: "请选择年级",
      });
      return;
    }

    if (
      personalIsShow &&
      gradeIsShow &&
      classIsShow &&
      !personalIsFill &&
      !gradeIsFill &&
      !classIsFill &&
      !user.id &&
      !classInfo.id &&
      !gradeInfo.id
    ) {
      Toast.show({
        icon: "fail",
        content: "姓名、班级、年级至少填一项",
      });
      return;
    }

    if (!time) {
      Toast.show({
        icon: "fail",
        content: "请选择时间",
      });
      return;
    }

    if (!remarks) {
      Toast.show({
        icon: "fail",
        content: "请填写评价",
      });
      return;
    }
    callback({
      time,
      userName: user.name,
      userId: user.id,
      remarks,
      classInfo,
      gradeInfo,
    });
  };
  const onreset = () => {
    setUser({ id: "", name: "" });
    setRemarks("");
    setTime("");
    setClassInfo({
      show: false,
      id: "",
      label: "",
    });
    setGradeInfo({
      show: false,
      id: "",
      label: "",
    });
  };
  // ================
  const { list: replyList } = useReplyList({ pageNo: 1, pageSize: 999 });
  const [showReply, setReply] = useState(false);
  /* ================ */

  /* 时间变动 */
  function onTimeChange(time) {
    if (!time) return;
    if (!classInfo.id) return;
    getTeacherInfo(classInfo.id, time);
  }
  /* 班级变动 */
  function onClassInfoChange(node) {
    if (!time) return;
    if (!node) return;
    getTeacherInfo(node, time);
  }

  async function getTeacherInfo(nodeId, time) {
    const result = await getBaseCourseUser({
      time,
      nodeId,
      type: 4, // 节点类型 1.录播 2.监控 3.教室 4.班级
    });
    if (result.code === 0 && result.data) {
      const info = result.data[0];
      if (!info) return;
      setUser({
        name: info.subjectTeacherName,
        id: info.subjectTeacherId,
      });
      setGradeInfo({
        show: false,
        id: info.gradeId,
        label: info.gradeName,
      });
    }
  }

  useEffect(() => {
    if (!props.classRoomId || !props.type) {
      // onreset();
      return;
    }
    const time = dayjs().format("YYYY-MM-DD HH:mm:ss");
    getBaseCourseUser({
      time,
      // time: "2025-03-04 16:30:00",
      nodeId: props.classRoomId,
      type: props.type, // 节点类型 1.录播 2.监控 3.教室 4.班级
    }).then((data) => {
      const info = data.data[0];
      if (data.code === 0 && info) {
        setUser({
          name: info.subjectTeacherName,
          id: info.subjectTeacherId,
        });
        setClassInfo({
          show: false,
          id: info.classId,
          label: info.className,
        });
        setGradeInfo({
          show: false,
          id: info.gradeId,
          label: info.gradeName,
        });
        setTime(time);
      } else {
        // onreset();
      }
    });
  }, [props.classRoomId, props.type]);
  return (
    <Root>
      {classIsShow === 1 && (
        <FormItem>
          <div className={`title ${classIsFill === 1 ? "require" : ""}`}>
            班级
          </div>
          <div
            className="label"
            onClick={() =>
              setClassInfo((pre) => ({
                ...pre,
                show: true,
              }))
            }
          >
            <span>{classInfo.label ? classInfo.label : "请选择"}</span>
            <i className="iconfont iconyoujiantou"></i>
          </div>
        </FormItem>
      )}

      {props.showTime && (
        <FormItem>
          <div className="title require">时间</div>
          <div className="label" onClick={() => setShowDate(true)}>
            <span>{time ? time : "请选择"}</span>
            <i className="iconfont iconyoujiantou"></i>
          </div>
        </FormItem>
      )}

      {gradeIsShow === 1 && (
        <FormItem>
          <div className={`title ${gradeIsFill === 1 ? "require" : ""}`}>
            年级
          </div>
          <div
            className="label"
            onClick={() =>
              setGradeInfo((pre) => ({
                ...pre,
                show: true,
              }))
            }
          >
            <span>{gradeInfo.label ? gradeInfo.label : "请选择"}</span>
            <i className="iconfont iconyoujiantou"></i>
          </div>
        </FormItem>
      )}

      {personalIsShow === 1 && (
        <FormItem>
          <div className={`title ${personalIsFill === 1 ? "require" : ""}`}>
            姓名
          </div>
          <input
            onChange={(e) => {
              onSearch(e.target.value);
              setUser((pre) => ({
                ...pre,
                name: e.target.value,
              }));
            }}
            value={user.name}
            placeholder="请输入"
          />
          {userList.show && (
            <UserList>
              {userList.list.length === 0 && (
                <Empty
                  className="absolute left-1/2 top-1/2 transform -translate-y-2/4 -translate-x-2/4"
                  description="暂无数据"
                />
              )}
              {userList.list.map((option) => (
                <div
                  onClick={(e) => {
                    setUser({
                      name: option.userName,
                      id: option.coreUserInfoId,
                    });
                    setUserList((pre) => ({
                      ...pre,
                      show: false,
                    }));
                    e.stopPropagation();
                  }}
                  key={option.coreUserInfoId}
                  className="item-user"
                >
                  <div className="user">
                    <span className="name truncate">{option.userName}</span>
                    {option.phone && (
                      <span className="shrink-0">
                        , {$hidePhone(option.phone)}
                      </span>
                    )}
                  </div>
                  <div className="org">{option.orgName}</div>
                </div>
              ))}
            </UserList>
          )}
        </FormItem>
      )}

      <FormItem className="mark">
        <div className="head">
          <div className="title require">评价</div>
          <div onClick={() => setReply(true)} className="replyBtn">
            <MessageOutlined />
            <span className="ml-1">快捷评语</span>
          </div>
        </div>
        <TextArea
          placeholder="请输入"
          value={remarks}
          onChange={(value) => setRemarks(value)}
          maxLength={200}
          rows={4}
        />
      </FormItem>

      <DatePicker
        visible={showDate}
        onClose={() => {
          setShowDate(false);
        }}
        defaultValue={now}
        precision="minute"
        onConfirm={(val) => {
          const time = moment(+val).format("YYYY-MM-DD HH:mm:ss");
          onTimeChange(time);
          setTime(time);
        }}
      />

      {/* 班级 */}
      <ClassPopup
        show={classInfo.show}
        onHide={() => {
          setClassInfo({
            show: false,
            id: "",
            label: "",
          });
        }}
        onChoose={({ stageId, gradeId, classId, classLabel }) => {
          onClassInfoChange(classId);
          setClassInfo({
            show: false,
            id: classId,
            label: classLabel,
          });
        }}
      />
      {/* 年级 */}
      <GradePopup
        show={gradeInfo.show}
        onHide={() => {
          setGradeInfo({
            show: false,
            id: "",
            label: "",
          });
        }}
        onChoose={({ stageId, gradeId, gradeLabel }) => {
          setGradeInfo({
            show: false,
            id: gradeId,
            label: gradeLabel,
          });
        }}
      />

      <Popup
        visible={showReply}
        onMaskClick={() => {
          setReply(false);
        }}
        bodyStyle={{
          borderTopLeftRadius: "16px",
          borderTopRightRadius: "16px",
        }}
      >
        <div style={{ height: "40vh", overflowY: "scroll" }}>
          {replyList.length ? (
            replyList.map((item) => (
              <ReplyItem
                className="truncate"
                onClick={() => {
                  setRemarks(remarks + item.content);
                  setReply(false);
                }}
                key={item.id}
              >
                {item.content}
              </ReplyItem>
            ))
          ) : (
            <YsEmpt msg="暂无快捷评语" />
          )}
        </div>
      </Popup>
    </Root>
  );
}
export default forwardRef(AssessForm);
