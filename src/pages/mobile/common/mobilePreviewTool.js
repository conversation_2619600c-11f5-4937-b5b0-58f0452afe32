import React, { useState, useEffect } from "react";
import { $isCdn, $fileType } from "@/tools";
import { CloseOutline } from "antd-mobile-icons";
import styled from "styled-components";
const Root = styled.div`
  padding: 10vh 0;
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: rgba(0, 0, 0, 1);
  display: flex;
  align-items: center;
  video {
    width: 100vw;
    max-height: 100%;
  }
  img {
    width: 100%;
    max-height: 100%;
  }
  .close-Icon {
    z-index: 99;
    font-size: 30px;
    position: absolute;
    right: 20px;
    top: 3vh;
    color: #fff;
  }
`;
function MobilePreviewTool({ list, index, onClose }) {
  const [preview] = useState({ list, index });
  useEffect(() => {
    document.body.style.overflow = "hidden";
    return () => {
      document.body.style.overflow = "auto";
    };
  }, []);
  return (
    <Root className="mobilePreview">
      <CloseOutline onClick={onClose} className="close-Icon" />
      {$fileType(preview.list[preview.index]) === "mp4" ||
      $fileType(preview.list[preview.index]) === "mov" ? (
        <video
          autoPlay
          muted
          controls
          src={$isCdn(preview.list[preview.index])}
        />
      ) : (
        <img src={$isCdn(preview.list[preview.index])} alt="" />
      )}
    </Root>
  );
}

export default MobilePreviewTool;
