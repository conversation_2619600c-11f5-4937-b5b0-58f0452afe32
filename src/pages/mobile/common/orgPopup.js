import { useEffect, useState } from "react"
import { Popup, Empty, Toast } from "antd-mobile"
import { CloseOutline, CheckOutline } from "antd-mobile-icons"
import { getAllOrgById } from "@/api"
import styled from "styled-components"
const Head = styled.div`
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  .icon {
    font-size: 18px;
  }
  .label {
    font-weight: 600;
    font-size: 16px;
  }
  .btn {
    color: #007aff;
    font-size: 16px;
  }
`
const Scrollbar = styled.div`
  line-height: 50px;
  height: 52px;
  padding: 0 16px;
  width: 100%;
  overflow-x: auto;
  white-space: nowrap;
  ::-webkit-scrollbar {
    display: none;
  }
  div.item + div.item {
    margin-left: 20px;
  }
  div.item {
    display: inline-block;
    font-size: 16px;
    color: #b3b3b3;
    &.active {
      position: relative;
      &::before {
        content: "";
        position: absolute;
        bottom: 3px;
        left: 50%;
        transform: translateX(-50%);
        width: 28px;
        height: 3px;
        background: #007aff;
        border-radius: 3px;
      }
    }
    &.title {
      color: #1a1a1a;
      font-weight: bold;
    }
  }
`
const OrgList = styled.div`
  position: relative;
  height: 40vh;
  overflow-y: auto;
  padding: 0 16px;
  .item {
    font-size: 16px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #1a1a1a;
    span {
      padding-right: 20px;
      flex: 1;
    }
  }
  .item.active {
    color: #007aff;
  }
`

export function OrgPopup({ show, onHide, onChange }) {
  const [option, setOption] = useState([])
  const [orgList, setOrgList] = useState([])

  const [selectedOrgList, setSelectedOrgList] = useState([
    { id: "-1", label: "请选择" },
  ])

  const [activeIndex, setActiveIndex] = useState(0)

  useEffect(() => {
    const userInfo = JSON.parse(sessionStorage.getItem("userInfo"))
    getAllOrgById({ orgId: userInfo.orgId }).then(result => {
      if (result.code === 0) {
        setOption(result.data)
        setOrgList(result.data.filter(item => item.parentId === "0"))
      }
    })
  }, [])

  function handleSelectOrg({ id, orgName, parentId }) {
    // 编辑当前选中
    if (selectedOrgList[activeIndex].id !== "-1") {
      const preSelectedOrgList = []
      for (let index = 0; index < activeIndex; index++) {
        preSelectedOrgList.push(selectedOrgList[index])
      }

      /* 取消当前选中 */
      if (selectedOrgList[activeIndex].id === id) {
        preSelectedOrgList.push({ id: "-1", label: "请选择" })
        setSelectedOrgList(preSelectedOrgList)

        if (activeIndex - 1 > 0) {
          setOrgList(
            option.filter(
              item =>
                item.parentId === selectedOrgList[activeIndex - 1].parentId
            )
          )
          setActiveIndex(pre => pre - 1)
        } else {
          setOrgList(option.filter(item => item.parentId === "0"))
          setActiveIndex(0)
        }
        return
      }

      preSelectedOrgList.push(
        { id, label: orgName, parentId },
        { id: "-1", label: "请选择" }
      )
      setSelectedOrgList(preSelectedOrgList)

      setActiveIndex(pre => pre + 1)
      setOrgList(option.filter(item => item.parentId === id))
      return
    }
    // 前进
    setSelectedOrgList(pre => {
      pre.pop()
      return [
        ...pre,
        { id, label: orgName, parentId },
        { id: "-1", label: "请选择" },
      ]
    })
    setActiveIndex(pre => pre + 1)
    setOrgList(option.filter(item => item.parentId === id))
  }

  return (
    <Popup
      visible={show}
      onMaskClick={onHide}
      bodyStyle={{
        borderTopLeftRadius: "16px",
        borderTopRightRadius: "16px",
      }}
    >
      <Head>
        <CloseOutline
          onClick={onHide}
          className="icon"
        />
        <div className="label">请选择机构</div>
        <span
          onClick={() => {
            if (selectedOrgList.length === 1) {
              Toast.show({
                icon: "fail",
                content: "请选择机构",
              })
              return
            }
            const target = selectedOrgList[selectedOrgList.length - 2]
            onChange(target.id, target.label)
          }}
          className="btn"
        >
          确定
        </span>
      </Head>
      <Scrollbar>
        {selectedOrgList.map((item, key) => (
          <div
            key={item.id}
            onClick={() => {
              if (item.id === "-1") return
              setActiveIndex(key)
              setOrgList(() => {
                return option.filter(org => org.parentId === item.parentId)
              })
            }}
            className={`item ${activeIndex === key ? "active" : ""} ${
              item.id !== "-1" ? "title" : ""
            }`}
          >
            {item.label}
          </div>
        ))}
      </Scrollbar>
      <OrgList>
        {orgList.length === 0 && (
          <Empty
            className="absolute left-1/2 top-1/2 transform -translate-y-2/4 -translate-x-2/4"
            description="暂无数据"
          />
        )}
        {orgList.map(org => (
          <div
            onClick={() => handleSelectOrg(org)}
            key={org.id}
            className={`item ${
              selectedOrgList[activeIndex]?.id === org.id ? "active" : ""
            }`}
          >
            <span className="truncate">{org.orgName}</span>
            {selectedOrgList[activeIndex]?.id === org.id && (
              <CheckOutline style={{ color: "#007AFF", fontSize: 20 }} />
            )}
          </div>
        ))}
      </OrgList>
    </Popup>
  )
}
