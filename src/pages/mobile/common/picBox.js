import React, { useRef, useMemo } from "react"
import { $upload, $isCdn, $fileType } from "@/tools"
import { Toast } from "antd-mobile"
import styled from "styled-components"
import { CloseCircleFill } from "antd-mobile-icons"
// import Frame from "@/assets/images/Frame.png"
// import Upload from "@/assets/images/mobile/upload.png"
import Play from "@/assets/images/play.png"
const PicBoxRoot = styled.div`
  position: relative;
  height: 100px;
  background: #f9fafb;
  border: 1px dashed transparent;
  border-radius: 6px;
  img.source {
    border-radius: 6px;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .icon {
    width: 30px;
    height: 30px;
    z-index: 10;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
`

/* 
  预览pic容器
  coverUrl: 封面路径
    1.视频截图
    2.上传视频
  fileUrl: 真实路径
*/
export function PicBox({
  coverUrl,
  fileUrl,
  onDelete,
  onPreview,
  mode = "edit",
}) {
  const handle2Delete = e => {
    e.stopPropagation()
    onDelete()
  }
  const showPlayerIcon = useMemo(() => {
    if (!fileUrl) return false
    if ($fileType(fileUrl).toLowerCase() === "mp4") {
      return true
    }
    if ($fileType(fileUrl).toLowerCase() === "mov") {
      return true
    }
  }, [fileUrl])
  return (
    <PicBoxRoot onClick={onPreview}>
      <img
        className="source"
        src={$isCdn(coverUrl)}
        alt=""
      />
      {showPlayerIcon && (
        <img
          className="icon"
          src={Play}
          alt=""
        />
      )}
      {mode === "edit" && (
        <CloseCircleFill
          onClick={e => {
            handle2Delete(e)
          }}
          className="absolute -right-2 text-xl  -top-2 z-10"
          style={{ color: "rgba(0, 5, 32, 0.6)" }}
        />
      )}
    </PicBoxRoot>
  )
}

const UploadRoot = styled.div`
  /* width: 166px; */
  height: 42px;
  background: #f8f8f8;
  border-radius: 100px;
`
export function UploadBox({ onSuccess }) {
  const fileRef = useRef()

  const beforeUpload = file => {
    const isImage = file.type.indexOf("image/") !== -1
    const isVideo = file.type.indexOf("video/") !== -1
    const isLt100M = file.size / 1024 / 1024 < 100
    if (!isLt100M && isVideo) {
      Toast.show({
        icon: "fail",
        content: "视频文件大小小于100M",
      })
    }
    if (!(isImage || isVideo)) {
      Toast.show({
        icon: "fail",
        content: "上传正确的格式",
      })
    }
    return (isImage || isVideo) && isLt100M
  }
  const fileChange = async e => {
    if (!beforeUpload(e.target.files[0])) {
      e.target.value = ""
      return
    }

    Toast.show({
      icon: "loading",
      content: "加载中...",
      duration: 0,
      maskClickable: false,
    })

    const result = await $upload(e)
    Toast.clear()
    if (result.fileUrl) {
      onSuccess(result)
    }
  }
  return (
    <UploadRoot
      onClick={() => fileRef.current.click()}
      className="flex items-center justify-center"
    >
      <input
        ref={c => (fileRef.current = c)}
        onChange={fileChange}
        className="hidden"
        type="file"
        accept="image/*,video/*"
      />
      <i className="iconfont icontianjia mr-2"></i>
      <span
        style={{ color: "#1A1A1A" }}
        className="text-base"
      >
        上传
      </span>
    </UploadRoot>
  )
}

/* 上传方块 */

const UploadBlockRoot = styled.div`
  height: 100px;
  background: #f8f8f8;
  border-radius: 4px;
  i {
    font-size: 30px;
    color: #808080;
  }
`
export function UploadBox2({ onSuccess }) {
  const fileRef = useRef()

  const beforeUpload = file => {
    const isImage = file.type.indexOf("image/") !== -1
    const isVideo = file.type.indexOf("video/") !== -1
    const isLt100M = file.size / 1024 / 1024 < 100
    if (!isLt100M && isVideo) {
      Toast.show({
        icon: "fail",
        content: "视频文件大小小于100M",
      })
    }
    if (!(isImage || isVideo)) {
      Toast.show({
        icon: "fail",
        content: "上传正确的格式",
      })
    }
    return (isImage || isVideo) && isLt100M
  }
  const fileChange = async e => {
    if (!beforeUpload(e.target.files[0])) {
      e.target.value = ""
      return
    }

    Toast.show({
      icon: "loading",
      content: "加载中...",
      duration: 0,
      maskClickable: false,
    })

    const result = await $upload(e)
    Toast.clear()
    if (result.fileUrl) {
      onSuccess(result)
    }
  }
  return (
    <UploadBlockRoot
      onClick={() => fileRef.current.click()}
      className="flex items-center justify-center"
    >
      <input
        ref={c => (fileRef.current = c)}
        onChange={fileChange}
        className="hidden"
        type="file"
        accept="image/*,video/*"
      />
      <i className="iconfont icontianjia"></i>
    </UploadBlockRoot>
  )
}
