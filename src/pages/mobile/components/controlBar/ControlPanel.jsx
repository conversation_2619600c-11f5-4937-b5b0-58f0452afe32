import { useState } from "react";
import styled from "styled-components";
import { Slider, Toast } from "antd-mobile";
import { getptcmd, stopptcmd, zoomcmd, stopzoomcmd } from "@/api";
import { CloseOutline } from "antd-mobile-icons";
const ControlPanelRoot = styled.div`
  top: ${(props) => props.top + "px"};

  .top {
    margin: 0 auto;
    transform: rotate(45deg);
    width: 240px;
    height: 240px;
    border-radius: 50%;
    background: #e4e7ed;
    overflow: hidden;
    position: relative;
    clip-path: circle(50% at 50% 50%);
    .item {
      position: absolute;
      width: 120px;
      height: 120px;
      cursor: pointer;
      &:active {
        background: rgba(76, 132, 255, 0.2);
        i {
          color: #4c84ff;
        }
      }
      i {
        position: relative;
        color: #909399;
        position: absolute;
        left: 50%;
        top: 50%;
        font-size: 30px;
        pointer-events: none;
      }
    }
  }

  .lt {
    left: 0;
    top: 0;
    border-right: 2px solid #fff;
    border-bottom: 2px solid #fff;
    i {
      transform: translate(-50%, -50%) rotate(135deg);
      bottom: -8px;
    }
  }
  .rt {
    right: 0;
    top: 0;
    border-left: 2px solid #fff;
    border-bottom: 2px solid #fff;
    i {
      transform: translate(-50%, -50%) rotate(-135deg);
      bottom: -8px;
    }
  }
  .lb {
    left: 0;
    bottom: 0;
    border-right: 2px solid #fff;
    border-top: 2px solid #fff;
    i {
      transform: translate(-50%, -50%) rotate(45deg);
      bottom: -8px;
    }
  }
  .rb {
    right: 0;
    bottom: 0;
    border-left: 2px solid #fff;
    border-top: 2px solid #fff;
    i {
      transform: translate(-50%, -50%) rotate(-45deg);
      bottom: -8px;
    }
  }
  .zz {
    position: absolute;
    left: 50%;
    top: 50%;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    width: 76px;
    height: 76px;
    border: 4px solid #fff;
    background: #e4e7ed;
    z-index: 9;
    overflow: hidden;
    clip-path: circle(50% at 50% 50%);
    .mask {
      cursor: pointer;
      position: absolute;
      background: #e4e7ed;
      left: 0;
      top: 0;
      bottom: 0;
      right: 0;
      z-index: 10;
      &:active {
        background: rgba(76, 132, 255, 0.2);
        i {
          color: #4c84ff;
        }
      }
    }
    i {
      color: #909399;
      position: absolute;
      left: 50%;
      top: 50%;
      font-size: 30px;
      z-index: 10;
      transform: translate(-50%, -50%);
      color: #a9acb1;
    }
  }

  .bottom {
    width: 100%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    div {
      flex: 1;
      cursor: pointer;
      text-align: center;
      background: #e4e7ed;
      i {
        pointer-events: none;
        font-size: 30px;
        color: #595959;
      }
      &:active {
        background: rgba(76, 132, 255, 0.2);
        i {
          color: #4c84ff;
        }
      }
      &:first-child {
        margin-right: 8px;
        border-radius: 36px 0px 0px 36px;
      }
      &:last-child {
        border-radius: 0px 36px 36px 0px;
      }
    }
  }
`;

export function ControlPanel({ top, onHide, activeInfo }) {
  const [step, setStep] = useState(4);
  function onControl(e) {
    const type = e.target.getAttribute("data-role");
    const step = e.target.getAttribute("data-step");
    if (type === "close") {
      return onHide();
    }
    if (!type) return;

    if (!activeInfo.monitoringNodeId) {
      return Toast.show({
        icon: "fail",
        content: "请选择通道",
      });
    }
    if (activeInfo.belongs !== 20000) {
      return Toast.show({
        icon: "fail",
        content: "请选取监控通道",
      });
    }

    // =================
    let nodeId = activeInfo.monitoringNodeId;
    let timer;
    if (type === "-" || type === "+") {
      const operation = type === "-" ? 4 : 1;
      zoomcmd({
        operation,
        nodeId,
        step,
      }).then((res) => {
        if (res.code === 0) {
          timer && clearTimeout(timer);
          timer = setTimeout(() => {
            stopzoomcmd({
              operation,
              nodeId,
              step,
            });
          }, 1000);
        }
      });
    } else {
      getptcmd({ direction: type, nodeId, step }).then(() => {
        timer && clearTimeout(timer);
        timer = setTimeout(() => {
          stopptcmd({
            direction: type,
            nodeId,
            step,
          });
        }, 1000);
      });
    }
  }
  return (
    <ControlPanelRoot
      onClick={onControl}
      top={top}
      className="absolute z-20 left-0 bottom-0 right-0 bg-white flex flex-col"
    >
      <div className="title flex items-center justify-between text-base p-4">
        <div className="font-bold">摄像头控制</div>
        <div
          data-role="close"
          data-step={step}
          className="w-8 h-8 text-center leading-8"
        >
          <CloseOutline
            className="text-[#808080] text-xl pointer-events-none"
            onClick={() => onHide()}
          />
        </div>
      </div>
      <div className="flex-1 p-4 flex flex-col justify-around">
        <div className="top">
          <div data-role="1" data-step={step} className="lt item">
            <i className="iconfont icon-solid-arrow"></i>
          </div>
          <div data-role="4" data-step={step} className="rt item">
            <i className="iconfont icon-solid-arrow"></i>
          </div>
          <div data-role="3" data-step={step} className="lb item">
            <i className="iconfont icon-solid-arrow"></i>
          </div>
          <div data-role="2" data-step={step} className="rb item">
            <i className="iconfont icon-solid-arrow"></i>
          </div>
          <div data-role="zz" data-step={step} className="zz">
            <div className="mask">
              <i className="iconfont iconyuan"></i>
            </div>
          </div>
        </div>
        <div className="bottom">
          <div data-role="-" data-step={step}>
            <i className="iconfont iconsuoxiao1"></i>
          </div>
          <div data-role="+" data-step={step}>
            <i className="iconfont iconfangda"></i>
          </div>
        </div>
        <div className="slider">
          <div className="text-base mb-4">移动速度 {step}</div>
          <Slider
            style={{ "--fill-color": "#007AFF" }}
            value={step}
            min={1}
            max={8}
            onChange={(value) => {
              setStep(value);
            }}
          />
        </div>
      </div>
    </ControlPanelRoot>
  );
}
