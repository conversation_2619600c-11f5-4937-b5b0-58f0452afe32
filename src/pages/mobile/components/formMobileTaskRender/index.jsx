import React from "react";
import styled from "styled-components";
import {
  Input,
  TextArea,
  Radio,
  Space,
  Checkbox,
  Rate,
  Stepper,
} from "antd-mobile";
import { Slider } from "antd";

const Root = styled.div``;

const FormGroup = styled.div`
  margin: 20px 0;
  padding-bottom: 10px;
  word-break: break-all;
  border-bottom: 1px solid #ccc;
  font-size: 16px;
  font-weight: 500;
  color: #000000d9;
`;
const FormItem = styled.div`
  .title {
    word-break: break-all;
  }
  & + & {
    margin-top: 20px;
  }
  .formItemTitle {
    word-break: break-all;
    font-size: 14px;
    font-weight: bold;
    color: #262626;
    margin-bottom: 12px;
  }
`;

function FormTitle({ column }) {
  return (
    <FormItem>
      {column.label && <div className="title">{column.label}</div>}
      {column.value && (
        <div className="title" style={column.styles}>
          {column.value}
        </div>
      )}
    </FormItem>
  );
}

function FormInput({ column, onChange, value }) {
  return (
    <FormItem>
      <div className="formItemTitle">{column.label}</div>
      <Input
        style={{ background: "#F8F8F8", padding: 10 }}
        onChange={(value) => {
          onChange({
            [column.prop]: value,
          });
        }}
        maxLength={column.maxLength}
        value={value}
        placeholder={column.placeholder ? column.placeholder : "请输入"}
      />
    </FormItem>
  );
}
function FormTextarea({ column, onChange, value }) {
  return (
    <FormItem>
      <div className="formItemTitle">{column.label}</div>
      <TextArea
        maxLength={column.maxLength}
        style={{ background: "#F8F8F8", padding: 10 }}
        onChange={(value) => {
          onChange({
            [column.prop]: value,
          });
        }}
        rows={5}
        value={value}
        placeholder={column.placeholder ? column.placeholder : "请输入"}
      />
    </FormItem>
  );
}

function FormRadio({ column, onChange, value }) {
  return (
    <FormItem>
      <div className="formItemTitle">{column.label}</div>
      <Radio.Group
        value={value}
        onChange={(value) => {
          const label = column.dicData.find(
            (item) => item.value === value
          ).label;
          onChange({
            [column.prop]: value,
            ["$" + column.prop]: label,
          });
        }}
      >
        <Space direction="vertical">
          {column.dicData.map((item, index) => (
            <Radio label={item.label} key={index} value={item.value}>
              {item.label}
            </Radio>
          ))}
        </Space>
      </Radio.Group>
    </FormItem>
  );
}

function FormCheckbox({ column, onChange, value }) {
  return (
    <FormItem>
      <div className="formItemTitle">{column.label}</div>
      <Checkbox.Group
        value={value}
        onChange={(values) => {
          const labels = values.map((value) => {
            const target = column.dicData.find((item) => item.value === value);
            return target.label;
          });
          onChange({
            [column.prop]: values,
            ["$" + column.prop]: labels.join("|"),
          });
        }}
      >
        <Space direction="vertical">
          {column.dicData.map((item, index) => {
            return (
              <Checkbox label={item.label} key={index} value={item.value}>
                {item.label}
              </Checkbox>
            );
          })}
        </Space>
      </Checkbox.Group>
    </FormItem>
  );
}

function FormRate({ column, onChange, value }) {
  return (
    <FormItem>
      <div className="formItemTitle">{column.label}</div>
      <Rate
        value={value}
        onChange={(number) => {
          onChange({
            [column.prop]: number,
          });
        }}
        count={column.max}
      />
    </FormItem>
  );
}

function FormNumber({ column, onChange, value }) {
  return (
    <FormItem>
      <div className="formItemTitle">{column.label}</div>
      <div className="flex items-center mb-3">
        <Stepper
          stringMode
          style={{
            "--height": "36px",
          }}
          className="flex-1"
          value={value}
          min={0}
          max={column.maxRows}
          step={0.1}
          digits={1}
          onChange={(number) => {
            onChange({
              [column.prop]: number,
            });
          }}
        />
        <span className="ml-2">满分 {column.maxRows}</span>
      </div>
      <Slider
        step={0.1}
        value={value}
        onChange={(num) => {
          onChange({
            [column.prop]: num,
          });
        }}
        max={column.maxRows}
      />

      {/* <Slider
        onChange={(num) => {
          onChange({
            [column.prop]: num,
          });
        }}
        step={0.1}
        max={column.maxRows}
        defaultValue={value}
        value={value}
      /> */}
    </FormItem>
  );
}

export function FormMobileTaskRender({ formInfo, onChange, data }) {
  const optionJson = JSON.parse(formInfo);
  const column = optionJson.column || [];
  const group = optionJson.group || [];
  const temp = [...column, ...group];

  function FormItemCreator(columns) {
    return columns.map((column, index) => {
      if (column.type === "title") {
        return <FormTitle key={index} column={column} />;
      }
      if (column.type === "input") {
        return (
          <FormInput
            onChange={onChange}
            value={data[column.prop]}
            key={index}
            column={column}
          />
        );
      }
      if (column.type === "textarea") {
        return (
          <FormTextarea
            onChange={onChange}
            value={data[column.prop]}
            key={index}
            column={column}
          />
        );
      }
      if (column.type === "radio") {
        return (
          <FormRadio
            onChange={onChange}
            value={data[column.prop]}
            key={index}
            column={column}
          />
        );
      }
      if (column.type === "checkbox") {
        return (
          <FormCheckbox
            onChange={onChange}
            value={data[column.prop]}
            key={index}
            column={column}
          />
        );
      }
      if (column.type === "rate") {
        return (
          <FormRate
            onChange={onChange}
            value={data[column.prop]}
            key={index}
            column={column}
          />
        );
      }
      if (column.type === "number") {
        return (
          <FormNumber
            onChange={onChange}
            value={data[column.prop]}
            key={index}
            column={column}
          />
        );
      }
      if (!column.type && column.column) {
        return (
          <div key={index}>
            <FormGroup>{column.label}</FormGroup>
            {FormItemCreator(column.column)}
          </div>
        );
      }
      return null;
    });
  }

  return <Root>{FormItemCreator(temp)}</Root>;
}
