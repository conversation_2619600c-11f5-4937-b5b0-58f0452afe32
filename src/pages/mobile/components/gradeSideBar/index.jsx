import { useMemo, useState } from "react";
import styled from "styled-components";
import { CheckCircleFill } from "antd-mobile-icons";
import { YsEmpt } from "@/components";

const Root = styled.div`
  display: flex;
  height: 100%;
  .buildList {
    overflow-y: auto;
    width: 124px;
    background: #f2f2f6;
    .buildItem {
      line-height: 52px;
      height: 52px;
      font-size: 15px;
      color: #1a1a1a;
      padding: 0 20px;
      border-left: 3px solid transparent;
       .buildName{
       overflow: hidden; /* 隐藏溢出的内容 */
      text-overflow: ellipsis; /* 使用省略号表示溢出的内容 */
      white-space: nowrap; /* 防止文本换行 */
      }
    }
    .buildItem.active {
      background: #fff;
      border-left-color: #007aff;
    }
  }
  .roomList {
    background: #fff;
    overflow-y: auto;
    flex: 1;
    .roomItem {
      height: 52px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
      font-size: 16px;
      color: #1a1a1a;
    }
    .roomItem.active {
      background: #e0efff;
    }
  }
`;

export function GradeSideBar({ gradePlace, activeInfo, onChoose }) {
  console.log("activeInfo", activeInfo);
  const [activeKey, setActiveKey] = useState();
  const builds = useMemo(() => {
    return gradePlace.filter((item) => item.type === "grade");
  }, [gradePlace]);

  const rooms = useMemo(() => {
    return gradePlace.filter((item) => item.parentId === activeKey);
  }, [activeKey, gradePlace]);

  if (builds.length === 0) {
    return (
      <Root>
        <YsEmpt msg="暂无教室" />
      </Root>
    );
  }
  return (
    <Root>
      <div className="buildList">
        {builds.map((build) => (
          <div
            onClick={() => setActiveKey(build.id)}
            key={build.id}
            className={`buildItem ${activeKey === build.id ? "active" : ""}`}
          >
            <div className="buildName">{build.name}</div>
          </div>
        ))}
      </div>
      <div className="roomList">
        {rooms.map((room) => (
          <div
            onClick={() => onChoose(room)}
            key={room.id}
            className={`roomItem ${activeInfo.monitoringNodeId === room.roomId ? "active" : ""
              }`}
          >
            <span>{room.name}</span>
            {room.roomId === activeInfo.monitoringNodeId && (
              <CheckCircleFill style={{ color: "#4465F3" }} />
            )}
          </div>
        ))}
      </div>
    </Root>
  );
}
