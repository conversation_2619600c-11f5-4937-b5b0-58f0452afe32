import React, { useEffect, useState } from "react";
import styled from "styled-components";
import { getSetting } from "@/api";
import Tab1 from "@/assets/images/mobile/tab1.png";
import Tab2 from "@/assets/images/mobile/tab2.png";
import Tab3 from "@/assets/images/mobile/tab3.png";
import Tab4 from "@/assets/images/mobile/tab4.png";
import { useIsFreeUser, useIsClassteacher, useRouterGo } from "@/hooks";
const Root = styled.div`
  margin: 16px;
  margin-bottom: 0;
  background: #fff;
  border-radius: 16px;
  .tabItem {
    width: 33%;
    display: inline-block;
    text-align: center;
    padding: 20px 0;
    flex: 1;
    color: #1a1a1a;
    img {
      width: 40px;
      margin-bottom: 10px;
    }
  }
`;

export function IndexTab() {
  const isFree = useIsFreeUser();
  const isClassTeacher = useIsClassteacher();
  const navigate = useRouterGo();

  const [showNav, setShowNav] = useState(false);
  useEffect(() => {
    getSetting().then((res) => {
      if (res.code === 0) {
        setShowNav(
          res.data.patrolSystemSetting.allowViewOwnRecords === 1 ? true : false
        );
      }
    });
  }, []);

  if (isFree) {
    return (
      <Root>
        <div
          onClick={() => {
            navigate("/mobile/freeOnLine");
          }}
          className="tabItem"
        >
          <img src={Tab1} alt="" />
          <div>线上自由巡课</div>
        </div>
        <div
          onClick={() => {
            navigate("/mobile/freeOffLine");
          }}
          className="tabItem"
        >
          <img src={Tab2} alt="" />
          <div>线下自由巡课</div>
        </div>
        {showNav && (
          <div
            onClick={() => {
              navigate("/mobile/reply");
            }}
            className="tabItem"
          >
            <img src={Tab3} alt="" />
            <div>我的巡课评价</div>
          </div>
        )}
        {isClassTeacher && (
          <div
            onClick={() => {
              navigate("/mobile/previewTeacher");
            }}
            className="tabItem"
          >
            <img src={Tab4} alt="" />
            <div>班主任模式</div>
          </div>
        )}
      </Root>
    );
  }
  return (
    <Root>
      {showNav && (
        <div
          onClick={() => {
            navigate("/mobile/reply");
          }}
          className="tabItem"
        >
          <img src={Tab3} alt="" />
          <div>我的巡课评价</div>
        </div>
      )}
      {isClassTeacher && (
        <div
          onClick={() => {
            navigate("/mobile/previewTeacher");
          }}
          className="tabItem"
        >
          <img src={Tab4} alt="" />
          <div>班主任模式</div>
        </div>
      )}
    </Root>
  );
}
