import styled from "styled-components";
import Pen from "@/assets/images/pen.png";
const BarRoot = styled.div`
  z-index: 10;
  position: fixed;
  right: 20px;
  bottom: 140px;
  width: 52px;
  height: 52px;
  border-radius: 50%;
  background: #007aff;
  box-shadow: 0px 4px 16px 4px rgba(1, 31, 64, 0.25);
  img {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
  }
`;
export function PenBar({ onClick }) {
  return (
    <BarRoot onClick={() => onClick()}>
      <img src={Pen} alt="" />
    </BarRoot>
  );
}
