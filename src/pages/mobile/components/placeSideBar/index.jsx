import { useMemo, useState } from "react";
import styled from "styled-components";
import { CheckCircleFill } from "antd-mobile-icons";
import { Collapse } from "antd-mobile";
import { YsEmpt } from "@/components";

const Root = styled.div`
  display: flex;
  height: 100%;
  .buildList {
    overflow-y: auto;
    width: 124px;
    background: #f2f2f6;
    .buildItem {
      line-height: 52px;
      height: 52px;
      font-size: 16px;
      color: #1a1a1a;
      padding: 0 20px;
      border-left: 3px solid transparent;

      .buildName{
       overflow: hidden; /* 隐藏溢出的内容 */
      text-overflow: ellipsis; /* 使用省略号表示溢出的内容 */
      white-space: nowrap; /* 防止文本换行 */
      }
    }
    .buildItem.active {
      background: #fff;
      border-left-color: #007aff;
    }
  }
  .adm-list {
    border-top: none;
    .adm-list-body {
      border-top: none;
    }
  }
  .adm-list-item {
    padding-left: 0;
    .adm-list-item-content {
      padding: 0;
    }
  }
  .adm-collapse-panel-header {
    .adm-list-item-content {
      padding: 0 20px;
    }
  }
  .adm-collapse-panel-content {
    .adm-list-item-content-main {
      padding: 0;
    }
  }
  .roomList {
    background: #fff;
    flex: 1;
    .roomItem {
      height: 52px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
      font-size: 16px;
      color: #1a1a1a;
    }
    .roomItem.active {
      background: #e0efff;
    }
  }
`;

export function PlaceSideBar({ classPlace, activeInfo, onChoose, isFree }) {
  const [activeKey, setActiveKey] = useState();
  const builds = useMemo(() => {
    return classPlace.filter((item) => item.nodeType === "BUILDING");
  }, [classPlace]);

  const floors = useMemo(() => {
    // console.log('isFree', isFree)
    // console.log('activeKey', activeKey)
    // console.log('classPlace', classPlace)
    if (isFree == 1) {
      let arr = classPlace.filter((item) => item.parentId === activeKey);
      // console.log('arr', arr)
      let arr1 = arr.map((item) => {
        return { ...item, title: item.title ? item.title : item.name }
      })
      return arr1
    }
    else {
      if (!activeKey) {
        // console.log('classPlace.filter((item) => item.parentId === activeKey);', classPlace.filter((item) => item.parentId === activeKey))
        return classPlace.filter((item) => item.parentId === activeKey);

      }
      else {
        let arr = (classPlace.filter((item) => item.parentId === activeKey) || []).map((item) => {
          return { ...item, children: [] }
        })
        for (let i of arr) {
          for (let j of classPlace) {
            if (i.id == j.parentId || i.id == j.pid) {
              i.children.push(j)
            }
          }
        }
        let arr1 = arr.map((item) => {
          return { ...item, title: item.title ? item.title : item.name }
        })
        return arr1
      }
    }



  }, [activeKey, classPlace]);

  const [activeFloorKey, setActiveFloorKey] = useState("");
  const handleFloorChange = (key) => {
    setActiveFloorKey(key);
  };

  if (builds.length === 0) {
    return (
      <Root>
        <YsEmpt msg="暂无教室" />
      </Root>
    );
  }
  return (
    <Root>
      <div className="buildList">
        {builds.map((build) => (
          <div
            onClick={() => setActiveKey(build.id)}
            key={build.id}
            className={`buildItem ${activeKey === build.id ? "active" : ""}`}
          >
            <div className="buildName">{build.value}</div>
          </div>
        ))}
      </div>
      {isFree ? (
        <Collapse activeKey={activeFloorKey} accordion={true} className="roomList" onChange={handleFloorChange}>
          {floors.map((floor) => (
            <Collapse.Panel key={floor.id} title={floor.title}>
              {
                floor.children.map((room) => (
                  <div
                    onClick={() => onChoose(room)}
                    key={room.id}
                    className={`roomItem ${activeInfo.monitoringNodeId === room.id ? "active" : ""
                      }`}
                  >
                    <span>{room.title ? room.title : room.name}</span>
                    {room.id === activeInfo.monitoringNodeId && (
                      <CheckCircleFill style={{ color: "#4465F3" }} />
                    )}
                  </div>
                ))
              }
            </Collapse.Panel>
          ))}
        </Collapse>
      ) : (
        <div className="roomList">
          {floors.map((room) => (
            <div
              onClick={() => onChoose(room)}
              key={room.id}
              className={`roomItem ${activeInfo.monitoringNodeId === room.id ? "active" : ""
                }`}
            >
              <span>{room.value}</span>
              {room.id === activeInfo.monitoringNodeId && (
                <CheckCircleFill style={{ color: "#4465F3" }} />
              )}
            </div>
          ))}
        </div>
      )}
    </Root>
  );
}
