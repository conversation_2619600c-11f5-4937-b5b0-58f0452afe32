import React, {useEffect, useState} from "react";
import { YsEmpt, Loading } from "@/components";
import { formGetAllData } from "@/api"
import { Popup } from "antd-mobile"
import styled from "styled-components";
const PopupContainer = styled.div`
  height: 60vh;
  display: flex;
  flex-direction: column;
  .head {
    display: flex;
    height: 56px;
    div {
      line-height: 56px;
      font-size: 16px;
      flex: 1;
      text-align: center;
      border-bottom: 2px solid transparent;
    }
    div.active {
      color: #007AFF;
      border-bottom-color: #007AFF;
    }
  }
  .content {
    position: relative;
    flex: 1;
    overflow-y: auto;
  }
  .temItem {
    font-size: 16px;
    height: 56px;
    line-height: 56px;
    padding: 0 16px;
    color: #1A1A1A;
    border-bottom: 1px solid #EDEDED;
  }
`

export function TemMobileSelector({ onChange, value, show, onHide}) {
  const [active, setActive] = useState(1);
  const [list, setList] = useState([])
  const [loading, setLoading] = useState(false);

  const getList = async (isPubic) => {
    setLoading(true)
    const result = await formGetAllData({isPubic});
    setLoading(false)
    setList(result.data)
  }

  useEffect(() => {
    getList(1);
  }, [])

  const changeTab = (tab) => {
    if(tab === active) return;
    getList(tab)
    setActive(tab)
  }

  return <Popup
    visible={show}
    onMaskClick={onHide}
    bodyStyle={{
      borderTopLeftRadius: '16px',
      borderTopRightRadius: '16px',
        }}
      >
      <PopupContainer>
        <div className="head">
          <div onClick={() => changeTab(1)} className={active === 1 ? 'active': ''}>公共表单</div>
          <div onClick={() => changeTab(2)} className={active === 2 ? 'active': ''}>私有表单</div>
        </div>
        <div className="content">
          {
            loading && <Loading/>
          }
          {
            list.length ? list.map(item => <div onClick={() => {
              onChange(item)
            }} className="temItem truncate" key={item.formTemplateInfoId}>{item.formName}</div>) : <YsEmpt msg="暂无表单"/>
          }
        </div>
      </PopupContainer>
</Popup>
}

const PopFormChangeContainer = styled.div`
  background: #F2F2F2;
  text-align: center;
  div {
    font-size: 16px;
    background: #fff;
    height: 50px;
    line-height: 50px;
    color: #1A1A1A;
  }
`
export function TemMobileChangePop({show, onChange, onHide, mode}) {
  return <Popup
    visible={show}
    onMaskClick={onHide}
    bodyStyle={{
      borderTopLeftRadius: '16px',
      borderTopRightRadius: '16px',
      overflow: 'hidden'
        }}
      >
      <PopFormChangeContainer>
        <div onClick={() => {onChange('edit')}}>编辑</div>
        {
          mode === "all" && <div onClick={() => {onChange('del')}}>移除表单</div>
        }
        <div onClick={onHide} className="mt-3">取消</div>
      </PopFormChangeContainer>
    </Popup>
}
