import React, { useState, useRef, useEffect } from "react";
import { CloseOutline } from "antd-mobile-icons";
import { useRouterGo } from "@/hooks";
import styled from "styled-components";
import Form from "./../common/form";
import icon1 from "@/assets/images/mobile/icon1.png";
import success from "@/assets/images/mobile/success.png";
import {
  addPatrolRecord,
  formsaveAnswer,
  forminfoSaveData,
  getSetting,
} from "@/api";
import { Grid, Button, Toast, Dialog } from "antd-mobile";
import { $fileType } from "@/tools";
import { PicBox, UploadBox } from "../common/picBox";
import MobilePreviewTool from "../common/mobilePreviewTool";
import {
  TemMobileSelector,
  TemMobileChangePop,
  FormMobileTaskRender,
} from "@/pages/mobile/components";

const Root = styled.div`
  position: relative;
  min-height: 100vh;
  padding: 20px 16px 75px;
  background: #f2f2f6;
  .basicInfo {
    background: #fff;
    border-radius: 6px;
    .basicInfoTitle {
      font-size: 16px;
      color: #000;
      font-weight: 500;
      padding: 16px;
      border-bottom: 1px solid #e5e5e5;
    }
  }

  .submit {
    box-shadow: 0px -4px 20px 0px rgba(0, 0, 0, 0.08);
    padding: 12px 16px;
    background: #fff;
    z-index: 10;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
  }
`;
const FormBtn = styled.div`
  height: 42px;
  background: #f8f8f8;
  border-radius: 100px;
`;
const FormMask = styled.div`
  overflow-y: auto;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  z-index: 99;
  padding-bottom: 75px;
  background: #f2f2f6;

  .title {
    height: 60px;
    background: #fff;
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding: 0 16px;
    .name {
      font-weight: bold;
      flex: 1;
      color: #1a1a1a;
      font-size: 16px;
      padding-right: 20px;
    }
  }
  .content {
    margin: 16px;
    background: #fff;
    border-radius: 4px;
  }
`;

function OffLine() {
  const navigate = useRouterGo();
  document.title = "线下自由巡课";

  const formRef = useRef();
  const [attrs, setAttrs] = useState([]);
  const [showTemp, setShowTemp] = useState(false);

  const uploadSuccess = (data) => {
    if ($fileType(data.fileUrl) === "gif") {
      setAttrs([...attrs, { coverUrl: data.fileUrl, fileUrl: data.fileUrl }]);
      return;
    }
    if ($fileType(data.fileUrl) === "mp4") {
      setAttrs([...attrs, { coverUrl: data.screenUrl, fileUrl: data.fileUrl }]);
    } else if ($fileType(data.fileUrl).toLowerCase() === "mov") {
      setAttrs([...attrs, { coverUrl: data.screenUrl, fileUrl: data.fileUrl }]);
    } else {
      setAttrs([...attrs, { coverUrl: data.smallPath, fileUrl: data.fileUrl }]);
    }
  };
  const handleDelete = (index) => {
    const newAttr = [...attrs];
    newAttr.splice(index, 1);
    setAttrs(newAttr);
  };
  // 预览
  const [prevInfo, setPreview] = useState({
    show: false,
    list: [],
    index: -1,
  });
  const handle2Preview = (index) => {
    const list = attrs.map((item) => item.fileUrl);
    setPreview({
      show: true,
      list,
      index,
    });
  };
  const handle2ClosePreview = () => {
    setPreview({
      show: false,
      list: [],
      index: -1,
    });
  };

  const [temp, setTemp] = useState({
    show: false,
    data: {},
    formInfoId: "",
    formInfo: "",
    formName: "",
    formTemplateInfoId: "",
  });
  const [formAnswerId, setFormAnswerId] = useState("");
  const [showFormPop, setShowFormPop] = useState(false);

  const formSave = async () => {
    const saveAnswer = async (formInfoId, data) => {
      const result = await formsaveAnswer(formInfoId, data);
      if (result && result.code === 0) {
        Toast.show({
          icon: "success",
          content: "保存成功",
        });
        setFormAnswerId(result.data);
        setTemp({
          ...temp,
          show: false,
          formInfoId,
        });
      }
    };

    const res = await forminfoSaveData({
      formInfo: temp.formInfo,
      formTemplateInfoId: temp.formTemplateInfoId,
      usedObjId: "250",
      usedObjType: 1,
    });
    saveAnswer(res.data.id, temp.data);
  };

  const [isSubmitting, setIsSubmitting] = useState(false);
  const handleSubmit = () => {
    if (isSubmitting) return;

    formRef.current.onCheck((data) => {
      if (!data) return;
      setIsSubmitting(true);
      const newAttr = attrs.map((item) => ({
        attrPath: item.fileUrl,
        attrPic: item.coverUrl,
        attrType:
          $fileType(item.fileUrl) === "mp4" ||
            $fileType(item.fileUrl).toLowerCase() === "mov"
            ? 2
            : 1,
        patrolRecordId: 0,
      }));
      const params = {
        taskId: null,
        patrolComment: data.remarks,
        recordedUserName: data.userName,
        recordedUserId: data.userId,
        patrolRecordId: 0,
        patrolRecordAttrList: newAttr,
        patrolTime: data.time,

        useTable: formAnswerId ? 1 : 2,
        formTemplateInfoId: temp.formTemplateInfoId,
        formInfoName: temp.formName,
        formInfoId: temp.formInfoId,
        formTemplateAnswerId: formAnswerId,

        classId: data.classInfo.id,
        className: data.classInfo.label,
        gradeId: data.gradeInfo.id,
        gradeName: data.gradeInfo.label,
      };

      addPatrolRecord(params).then((res) => {
        if (res.code === 0) {
          Toast.show({
            icon: "success",
            content: "保存成功",
          });
          setTimeout(() => {
            navigate(-1);
          }, 1000);
        } else {
          setIsSubmitting(false);
        }
      }).catch(() => {
        setIsSubmitting(false);
      });
    });
  };

  const [patrolObject, setPatrolObject] = useState({
    classIsFill: 0,
    classIsShow: 1,
    gradeIsFill: 0,
    gradeIsShow: 1,
    personalIsFill: 0,
    personalIsShow: 1,
  });
  useEffect(() => {
    getSetting().then((res) => {
      if (res.code === 0) {
        setPatrolObject({
          classIsFill: 0,
          gradeIsFill: 0,
          personalIsFill: 0,
          personalIsShow: res.data.patrolFreeSetting?.nameIsFull,
          classIsShow: res.data.patrolFreeSetting?.classIsFull,
          gradeIsShow: res.data.patrolFreeSetting?.gradeIsFull,
        });
      }
    });
    document.title = "线下自由巡课";
    return () => {
      document.title = "在线巡课"; // 或者设置为默认标题
    };
  }, []);
  return (
    <Root>
      <div className="basicInfo">
        <div className="basicInfoTitle">基本信息</div>
        <div className="p-4">
          <Form
            showTime
            patrolObject={patrolObject}
            ref={(c) => (formRef.current = c)}
          />

          <div className="flex items-center mb-5">
            <div className="flex-1 pr-2">
              <UploadBox onSuccess={uploadSuccess} />
            </div>

            <div className="flex-1 pl-2">
              <FormBtn
                onClick={() => {
                  if (formAnswerId) {
                    setShowFormPop(true);
                  } else {
                    setShowTemp(true);
                  }
                }}
                className="flex items-center justify-center"
              >
                <img
                  className="w-5 mr-2"
                  src={formAnswerId ? success : icon1}
                  alt=""
                />
                <span style={{ color: "#1A1A1A" }} className="text-base">
                  量表
                </span>
              </FormBtn>
            </div>
          </div>
          <Grid columns={3} gap={8}>
            {attrs.map((item, index) => (
              <Grid.Item key={index}>
                <PicBox
                  onPreview={() => handle2Preview(index)}
                  onDelete={() => handleDelete(index)}
                  {...item}
                />
              </Grid.Item>
            ))}
          </Grid>
        </div>
      </div>

      <div className="submit">
        <Button
          onClick={isSubmitting ? null : handleSubmit}
          shape="rounded"
          block
          color="primary"
          size="large"
          style={{ opacity: isSubmitting ? 0.6 : 1, pointerEvents: isSubmitting ? 'none' : 'auto' }}
        >
          {isSubmitting ? "提交中..." : "提交"}
        </Button>
      </div>

      {prevInfo.show && (
        <MobilePreviewTool onClose={handle2ClosePreview} {...prevInfo} />
      )}

      <TemMobileChangePop
        mode={"all"}
        onChange={(type) => {
          if (type === "edit") {
            setTemp({
              ...temp,
              show: true,
            });
          }
          if (type === "del") {
            Dialog.confirm({
              content: "移除表单将删除已经提交的内容",
              onConfirm: () => {
                setTemp({
                  show: false,
                  data: {},
                  formInfo: "",
                  formInfoId: "",
                  formName: "",
                  formTemplateInfoId: "",
                });
                setFormAnswerId("");
              },
            });
          }
          setShowFormPop(false);
        }}
        onHide={() => {
          setShowFormPop(false);
        }}
        show={showFormPop}
      />

      <TemMobileSelector
        show={showTemp}
        onChange={({ formInfo, formName, formTemplateInfoId }) => {
          setShowTemp(false);
          setTemp({
            show: true,
            data: {},
            formInfo,
            formName,
            formTemplateInfoId,
          });
        }}
        onHide={() => setShowTemp(false)}
      />
      {temp.show && (
        <FormMask>
          <div className="title">
            <div className="truncate name">{temp.formName}</div>
            <CloseOutline
              onClick={() => {
                Dialog.confirm({
                  content: "表单未保存，确认关闭？",
                  onConfirm: () => {
                    setTemp({
                      ...temp,
                      show: false,
                    });
                  },
                });
              }}
              fontSize={18}
            />
          </div>
          <div className="content p-4">
            <FormMobileTaskRender
              formInfo={temp.formInfo}
              data={temp.data}
              onChange={(value) => {
                setTemp({
                  ...temp,
                  data: {
                    ...temp.data,
                    ...value,
                  },
                });
              }}
            />
          </div>

          <div className="submit">
            <Button
              onClick={formSave}
              shape="rounded"
              block
              color="primary"
              size="large"
            >
              保存
            </Button>
          </div>
        </FormMask>
      )}
    </Root>
  );
}
export default OffLine;
