import React, { useState, useRef, useEffect } from "react";
import styled from "styled-components";
import { CloseOutline } from "antd-mobile-icons";
import { Toast, Grid, Button, Dialog } from "antd-mobile";
import { $fileType } from "@/tools";
import { useCountDown } from "ahooks";
import {
  addPatrolRecord,
  formsaveAnswer,
  forminfoSaveData,
  getSetting,
} from "@/api";
import { getScreenShot, startRecordLive, stopRecordLive } from "@/api/upload";
import { PicBox } from "../common/picBox";
import Form from "./../common/form";
import {
  TemMobileSelector,
  FormMobileTaskRender,
  TemMobileChangePop,
} from "@/pages/mobile/components";

const DisRoot = styled.div`
  position: absolute;
  top: ${(props) => props.top + "px"};
  left: 0;
  bottom: 0;
  right: 0;
  background: #f2f2f6;
  z-index: 20;
  padding: 16px 16px 90px;
  overflow-y: auto;

  > .title {
    background: #fff;
    margin: -16px;
    padding: 16px;
  }

  .control {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    background: #fff;
    border-radius: 6px;
    margin: 12px 0;
    margin-top: 30px;
    padding: 0 20px;
    .left {
      transform: rotate(-90deg);
      display: inline-block;
      font-size: 24px;
      color: #909399;
    }
    .right {
      transform: rotate(90deg);
      display: inline-block;
      font-size: 24px;
      color: #909399;
    }
  }

  .basicInfo {
    background: #fff;
    border-radius: 6px;
    .basicInfoTitle {
      font-size: 16px;
      color: #000;
      font-weight: 500;
      padding: 16px;
      border-bottom: 1px solid #e5e5e5;
    }
  }

  .btns {
    display: flex;
    align-items: center;
    .screenShot {
      display: flex;
      justify-content: center;
      align-items: center;
      flex: 1;
      height: 40px;
      border-radius: 48px;
      background: #f9fafb;
    }
    .tape {
      flex: 1;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      height: 40px;
      border-radius: 48px;
      background: #f9fafb;
      .progress {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        background: rgba(0, 122, 255, 0.08);
        transition: width 1s;
      }
      &.active {
        color: #007aff;
      }
    }
  }

  .submit {
    box-shadow: 0px -4px 20px 0px rgba(0, 0, 0, 0.08);
    padding: 12px 16px;
    background: #fff;
    z-index: 10;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    div {
      text-align: center;
      line-height: 48px;
      color: #fff;
      border-radius: 48px;
      height: 48px;
      background: linear-gradient(90deg, #4a86f6 0%, #4465f3 100%);
    }
  }
`;
const FormMask = styled.div`
  overflow-y: auto;
  position: fixed;
  left: 0;
  top: ${(props) => props.top + "px"};
  bottom: 0;
  right: 0;
  z-index: 99;
  padding-bottom: 75px;
  background: #f2f2f6;

  .formTitle {
    height: 60px;
    background: #fff;
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding: 0 16px;
    .name {
      font-weight: bold;
      flex: 1;
      color: #1a1a1a;
      font-size: 16px;
      padding-right: 20px;
    }
  }
  .content {
    margin: 16px;
    background: #fff;
    border-radius: 4px;
  }
`;
// const patrolObject = {
//   classIsFill: 0,
//   classIsShow: 1,
//   gradeIsFill: 0,
//   gradeIsShow: 1,
//   personalIsFill: 0,
//   personalIsShow: 1,
// };
function Dis({
  top,
  activeInfo,
  playUrl,
  hideDis,
  nextChannel,
  prevChannel,
  setPreview,
}) {
  const taskRef = useRef();
  const formRef = useRef();
  const [attrs, setAttrs] = useState([]);
  const handleScreenShot = () => {
    if (!playUrl) {
      return Toast.show({
        icon: "fail",
        content: "该通道没有播放地址",
      });
    }

    Toast.show({
      icon: "loading",
      content: "截图中...",
      duration: 0,
      maskClickable: false,
    });

    getScreenShot({ flvUrl: playUrl }).then((res) => {
      Toast.clear();
      if (res.code === 0 && res.data) {
        const fileUrl = res.data;
        const coverUrl = res.data.replace(".jpg", "_small.jpg");
        setAttrs([...attrs, { coverUrl, fileUrl }]);
      } else {
        return Toast.show({
          icon: "fail",
          content: "截图失败",
        });
      }
    });
  };
  const handleDelete = (index) => {
    const newAttr = [...attrs];
    newAttr.splice(index, 1);
    setAttrs(newAttr);
  };
  const handlePrevChannel = () => {
    if (activeTap) {
      return Toast.show({
        icon: "fail",
        content: "录制中不能切换",
      });
    } else {
      prevChannel();
    }
  };
  const handleNextChannel = () => {
    if (activeTap) {
      return Toast.show({
        icon: "fail",
        content: "录制中不能切换",
      });
    } else {
      nextChannel();
    }
  };
  // 预览
  const handle2Preview = (index) => {
    const list = attrs.map((item) => item.fileUrl);
    setPreview({
      show: true,
      list,
      index,
    });
  };
  /* 视频录制 */
  const [activeTap, setActiveTap] = useState(false);
  const [targetDate, setTargetDate] = useState();
  const [countdown] = useCountDown({
    targetDate,
    onEnd: () => {
      setActiveTap(false);
      handleEndTap(15);
    },
  });
  const handleStartTap = () => {
    if (activeTap && taskRef.current) {
      const time = Math.round((15000 - countdown) / 1000);
      if (time > 5) {
        setTargetDate(undefined);
        setActiveTap(false);
        handleEndTap(time);
      } else {
        Toast.show({
          icon: "fail",
          content: "录制时间太短",
        });
      }
      return;
    }
    if (!playUrl) {
      return Toast.show({
        icon: "fail",
        content: "该通道没有播放地址",
      });
    }

    startRecordLive({ flvUrl: playUrl }).then((res) => {
      if (res.code === 0 && res.data) {
        taskRef.current = res.data;
        setTargetDate(Date.now() + 15000);
        setActiveTap(true);
      } else {
        return Toast.show({
          icon: "fail",
          content: "录制失败",
        });
      }
    });
  };
  const handleEndTap = (sec) => {
    Toast.clear();
    Toast.show({
      icon: "loading",
      content: "生成中...",
      duration: 0,
      maskClickable: false,
    });
    const apis = [
      getScreenShot({ flvUrl: playUrl }),
      stopRecordLive({ processId: taskRef.current, sec }),
      // getRecordLive({ flvUrl: playUrl, sec }),
    ];
    Promise.all(apis)
      .then((data) => {
        if (!data[0].data || !data[1].data) {
          Toast.clear();
          Toast.show({
            icon: "success",
            content: "录制失败",
          });
          setTargetDate(undefined);
          setActiveTap(false);
          return;
        }
        const params = {
          fileUrl: data[1].data,
          coverUrl: data[0].data,
        };
        setAttrs((c) => [...c, params]);
        Toast.clear();
        Toast.show({
          icon: "success",
          content: "录制成功",
        });

        setTargetDate(undefined);
        setActiveTap(false);
      })
      .catch((error) => {
        Toast.clear();
        Toast.show({
          icon: "success",
          content: "录制失败",
        });
        setTargetDate(undefined);
        setActiveTap(false);
      });
  };

  // 表单选择器
  const [showTemp, setShowTemp] = useState(false);
  // 表单
  const [temp, setTemp] = useState({
    show: false,
    data: {},
    formInfoId: "",
    formInfo: "",
    formName: "",
    formTemplateInfoId: "",
  });
  const [formAnswerId, setFormAnswerId] = useState("");
  const [showFormPop, setShowFormPop] = useState(false);

  const handleFormBtn = async () => {
    // 回答了
    if (formAnswerId) {
      setShowFormPop(true);
      return;
    }
    setShowTemp(true);
  };
  const formSave = async () => {
    const saveAnswer = async (formInfoId, data) => {
      const result = await formsaveAnswer(formInfoId, data);
      if (result && result.code === 0) {
        Toast.show({
          icon: "success",
          content: "保存成功",
        });
        setFormAnswerId(result.data);
        setTemp({
          ...temp,
          show: false,
          formInfoId,
        });
      }
    };
    const res = await forminfoSaveData({
      formInfo: temp.formInfo,
      formTemplateInfoId: temp.formTemplateInfoId,
      usedObjId: "250",
      usedObjType: 1,
    });
    saveAnswer(res.data.id, temp.data);
  };

  const [isSubmitting, setIsSubmitting] = useState(false);
  const submit = () => {
    if (isSubmitting) return;

    formRef.current.onCheck((data) => {
      if (!data) return;
      setIsSubmitting(true);
      const newAttr = attrs.map((item) => ({
        attrPath: item.fileUrl,
        attrPic: item.coverUrl,
        attrType: $fileType(item.fileUrl) === "mp4" ? 2 : 1,
        patrolRecordId: 0,
      }));
      const params = {
        taskId: "",
        patrolComment: data.remarks,
        recordedUserName: data.userName,
        recordedUserId: data.userId,
        patrolRecordId: 0,
        patrolRecordAttrList: newAttr,
        patrolTime: data.time,

        useTable: formAnswerId ? 1 : 2,
        formTemplateInfoId: temp.formTemplateInfoId,
        formInfoName: temp.formName,
        formInfoId: temp.formInfoId,
        formTemplateAnswerId: formAnswerId,

        classId: data.classInfo.id,
        className: data.classInfo.label,
        gradeId: data.gradeInfo.id,
        gradeName: data.gradeInfo.label,
      };

      addPatrolRecord(params).then((res) => {
        if (res.code === 0) {
          Toast.show({
            icon: "success",
            content: "保存成功",
          });
          setTimeout(() => {
            hideDis();
          }, 1000);
        } else {
          setIsSubmitting(false);
        }
      }).catch(() => {
        setIsSubmitting(false);
      });
    });
  };
  const [patrolObject, setPatrolObject] = useState({
    classIsFill: 0,
    classIsShow: 1,
    gradeIsFill: 0,
    gradeIsShow: 1,
    personalIsFill: 0,
    personalIsShow: 1,
  });
  useEffect(() => {
    getSetting().then((res) => {
      if (res.code === 0) {
        setPatrolObject({
          classIsFill: 0,
          gradeIsFill: 0,
          personalIsFill: 0,
          personalIsShow: res.data.patrolFreeSetting?.nameIsFull,
          classIsShow: res.data.patrolFreeSetting?.classIsFull,
          gradeIsShow: res.data.patrolFreeSetting?.gradeIsFull,
        });
      }
    });
  }, []);
  return (
    <DisRoot top={top}>
      <div className="title flex items-center justify-between text-base">
        <div className="font-bold">巡视评价</div>
        <CloseOutline onClick={() => hideDis()} />
      </div>

      <div className="control text-base">
        <i
          onClick={() => handlePrevChannel()}
          style={{
            color: "#B3B3B3",
          }}
          className="iconfont iconjiantou inline-block transform rotate-180 text-3xl"
        ></i>
        <div className="font-bold">{activeInfo.name}</div>
        <i
          onClick={() => handleNextChannel()}
          style={{ color: "#B3B3B3" }}
          className="iconfont iconjiantou text-3xl"
        ></i>
      </div>

      <div className="basicInfo">
        <div className="basicInfoTitle">基本信息</div>
        <div className="p-4">
          <Form
            classRoomId={
              // activeInfo.isClassRoom ? activeInfo.monitoringNodeId : null
              activeInfo.monitoringNodeId
            }
            type={activeInfo.type}
            patrolObject={patrolObject}
            ref={(c) => (formRef.current = c)}
          />
          <div className="btns mb-5">
            <div onClick={handleScreenShot} className="mr-8 screenShot">
              <i className="iconfont iconxiangji mr-1"></i>
              <span>截图</span>
            </div>

            <div
              className={`tape mr-8 ${activeTap ? "active" : ""}`}
              onClick={handleStartTap}
            >
              {activeTap && (
                <div
                  style={{
                    width:
                      countdown === 0
                        ? "0px"
                        : ((15000 - countdown) / 15000) * 100 + "%",
                  }}
                  className="progress"
                ></div>
              )}
              <i className="iconfont iconshipin2 mr-1"></i>
              <span>
                录像
                {activeTap && (
                  <span> {Math.round((15000 - countdown) / 1000)}s</span>
                )}
              </span>
            </div>

            <div onClick={handleFormBtn} className="screenShot">
              {formAnswerId ? (
                <i
                  className="iconfont iconzhengque1 mr-1"
                  style={{ color: "#25C274" }}
                ></i>
              ) : (
                <i className="iconfont icon-docment2 mr-1"></i>
              )}
              <span>量表</span>
            </div>
          </div>
          <Grid columns={3} gap={8}>
            {attrs.map((item, index) => (
              <Grid.Item key={index}>
                <PicBox
                  onPreview={() => handle2Preview(index)}
                  onDelete={() => handleDelete(index)}
                  {...item}
                />
              </Grid.Item>
            ))}
          </Grid>
        </div>
      </div>

      <div className="submit">
        <div
          onClick={isSubmitting ? null : submit}
          style={{ opacity: isSubmitting ? 0.6 : 1, pointerEvents: isSubmitting ? 'none' : 'auto' }}
        >
          {isSubmitting ? "提交中..." : "提交"}
        </div>
      </div>

      <TemMobileChangePop
        mode="all"
        onChange={(type) => {
          if (type === "edit") {
            setTemp({
              ...temp,
              show: true,
            });
          }
          if (type === "del") {
            Dialog.confirm({
              content: "移除表单将删除已经提交的内容",
              onConfirm: () => {
                setTemp({
                  show: false,
                  data: {},
                  formInfo: "",
                  formInfoId: "",
                  formName: "",
                  formTemplateInfoId: "",
                });
                setFormAnswerId("");
              },
            });
          }
          setShowFormPop(false);
        }}
        onHide={() => {
          setShowFormPop(false);
        }}
        show={showFormPop}
      />

      <TemMobileSelector
        show={showTemp}
        onChange={({ formInfo, formName, formTemplateInfoId }) => {
          setShowTemp(false);
          setTemp({
            show: true,
            data: {},
            formInfo,
            formName,
            formTemplateInfoId,
          });
        }}
        onHide={() => setShowTemp(false)}
      />

      {temp.show && (
        <FormMask top={top}>
          <div className="formTitle">
            <div className="truncate name">{temp.formName}</div>
            <CloseOutline
              onClick={() => {
                Dialog.confirm({
                  content: "表单未保存，确认关闭？",
                  onConfirm: () => {
                    setTemp({
                      ...temp,
                      show: false,
                    });
                  },
                });
              }}
              fontSize={18}
            />
          </div>
          <div className="content p-4">
            <FormMobileTaskRender
              formInfo={temp.formInfo}
              data={temp.data}
              onChange={(value) => {
                setTemp({
                  ...temp,
                  data: {
                    ...temp.data,
                    ...value,
                  },
                });
              }}
            />
          </div>

          <div className="submit">
            <Button
              onClick={formSave}
              shape="rounded"
              block
              color="primary"
              size="large"
            >
              保存
            </Button>
          </div>
        </FormMask>
      )}
    </DisRoot>
  );
}

export default Dis;
