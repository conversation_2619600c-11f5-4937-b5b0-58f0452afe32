import React, { useEffect, useState } from "react";
import Hls from "hls.js";
import { $getIsIOS, tryLink, tryPcLink } from "@/tools";
import { useWatermark } from "@/hooks";
import {
  getMobilePlayUrl,
  getFreeUserPassageway,
  selectLivingList,
  getMagicClassRoom,
  getMagicLive,
  getPatrolClassRoom,
  getRoomDeviceById,
  getPatrolLiveByFree,
  getSetting,
  getPatrolClassInfo,
} from "@/api";
import { Toast } from "antd-mobile";
import { CheckCircleFill } from "antd-mobile-icons";
import {
  PlaceSideBar,
  PenBar,
  ControlBtn,
  ControlPanel,
  TalkBar,
  GradeSideBar,
} from "./../components";
import { OrgPopup } from "./../common/orgPopup";
import styled from "styled-components";
import Dis from "./dis";
import Talk from "./talk";
import MobilePreviewTool from "./../common/mobilePreviewTool";
const Root = styled.div`
  position: relative;
  min-height: 100vh;
  .videoContainer {
    position: relative;
    font-size: 0;
    background: #000;
    height: 0;
    padding-top: 56.25%;
    video::-webkit-media-controls-fullscreen-button {
      display: none !important;
    }
    video::-moz-media-controls-fullscreen-button {
      display: none !important;
    }
    .document-container {
      position: absolute !important;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
    }
    .tips {
      z-index: 999;
      color: #fff;
      font-size: 14px;
      left: 10px;
      top: 10px;
      width: 70px;
      .tip-cur {
        height: 32px;
        background: rgba(0, 0, 0, 0.45);
        border-radius: 4px;
      }
      .tip-list {
        margin-top: 4px;
        background: rgba(0, 0, 0, 0.65);
        border-radius: 4px;
      }
      .tip-item {
        text-align: center;
        line-height: 40px;
        height: 40px;
      }
    }
    .cover {
      font-size: 16px;
      background: #303133;
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      right: 0;
      z-index: 999;
      color: #fff;
      span {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
    video {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  .tabOrg {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    padding: 0 16px;
    i {
      font-size: 16px;
    }
    .orgName {
      font-size: 16px;
      font-weight: 600;
      color: #1a1a1a;
    }
  }
  .tabs {
    height: 60px;
    background: #fff;
    border-bottom: 1px solid #e4e7ed;
    span {
      position: relative;
      line-height: 60px;
      font-size: 16px;
      &::after {
        content: "";
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: -8px;
        width: 20px;
        height: 3px;
        background: transparent;
      }
    }
    span.active {
      color: #4465f3;
      &::after {
        background: #4465f3;
      }
    }
  }
`;
function Online() {
  document.title = "线上自由巡课";
  const [active, setActive] = useState(1);
  const [top, setTop] = useState(0);
  useEffect(() => {

    const dom = document.getElementById("videoContainer");
    setTop(dom.clientHeight);
    document.title = "线上自由巡课";
    return () => {
      document.title = "在线巡课"; // 或者设置为默认标题
    };
  }, []);
  const [allTypes, setAllTypes] = useState([]);
  const [type1, setType1] = useState([]);
  const [type2, setType2] = useState([]);
  const [live, setLive] = useState([]);

  // 场所
  const [classPlace, setClassPlace] = useState([]);
  const [classPlaceNum, setClassPlaceNum] = useState(0);
  const [classRoomPlace, setClassRoomPlace] = useState([]);
  // 班级
  const [gradePlace, setGradePlace] = useState([]);
  const [gradePlaceNum, setGradePlaceNum] = useState([]);
  const [activeInfo, setActiveIdfo] = useState({
    belongs: "",
    monitoringNodeId: "",
    name: "",
    type: "",
  });
  const [showUrlList, setUrlList] = useState(false);
  const [urls, setUrls] = useState([]);
  const [playUrl, setPlayUrl] = useState("");

  const urlIndex = urls.findIndex((item) => item.phoneUrl === playUrl);

  const [showDis, setShowDis] = useState(false);
  const [isPlay, setIsPlay] = useState(false);
  const [showControl, setShowControl] = useState(false);

  const [currentOrg, setCurrentOrg] = useState(() => {
    const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
    return {
      showOrgPop: false,
      name: userInfo?.orgName,
      id: userInfo?.orgId,
      ISJD: Number(userInfo?.unitAttr) < 8,
    };
  });

  useEffect(() => {
    getFreeUserPassageway({ bureauId: currentOrg.id }).then((result) => {
      if (result.data.type1 && Array.isArray(result.data.type1)) {
        const arr = [];
        result.data.type1.forEach((item) => {
          if (item.nodeType) {
            arr.push(item);
          }
        });
        setType1(arr);
        setType2([...result.data.type2, ...result.data.type3]);
        setAllTypes([...arr, ...result.data.type2]);
      }
    });
    selectLivingList({
      pageNo: 1,
      pageSize: 999,
      bureauId: currentOrg.id,
    }).then((result) => {
      if (result.code === 0) {
        const data = result.data.map((item) => {
          const urls = item.urls.filter((item) => item.status === "1");
          return {
            ...item,
            urls,
            monitoringNodeId: item.eventInfoId,
            // url: item.urls[0] ? item.urls[0].phoneUrl : "",
            belongs: 30000,
            name: item.eventName,
          };
        });
        setLive(data);
      }
    });

    getPatrolClassRoom({ orgId: currentOrg.id }).then((res) => {
      if (res.code === 0 && res.data && Array.isArray(res.data)) {
        function getClassRoomNumber(targetArray) {
          let num = 0;
          targetArray.forEach((item) => {
            if (item.children && item.children.length > 0) {
              num += getClassRoomNumber(item.children);
            }
            if (item.classroom) {
              num++;
            }
          });
          return num;
        }
        const placeList = [];
        const list = res.data[0].children;
        list.forEach((item) => {
          item.nodeType = "BUILDING";
          item.value = item.name;
          item.parentId = item.pid;

          if (item.children && item.children.length > 0) {
            item.children.forEach((child) => {
              child.parentId = child.pid;
              child.value = child.name;
              placeList.push(child);
            });
          }
          placeList.push(item);
        });
        console.log("placeList", placeList);
        setClassPlace(placeList);
        setClassPlaceNum(getClassRoomNumber(res.data));
      } else {
        setClassPlace([]);
      }
    });
    getPatrolClassInfo({ orgId: currentOrg.id }).then((res) => {
      if (res.code === 0 && res.data && Array.isArray(res.data)) {
        console.log("res.data", res.data);
        function getGradeNumber(targetArray) {
          let num = 0;
          targetArray.forEach((item) => {
            if (item.type == "class") {
              num++;
            }
          });
          return num;
        }
        setGradePlace(res.data);
        setGradePlaceNum(getGradeNumber(res.data));
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentOrg.id]);

  useEffect(() => {
    if (!playUrl) return;
    let hls;
    const videoEle = document.getElementById("videoPlayer");
    if (Hls.isSupported()) {
      tryLink(playUrl).then((data) => {
        hls = new Hls();
        hls.loadSource(playUrl);
        hls.attachMedia(videoEle);
        hls.on(Hls.Events.MANIFEST_PARSED, function () {
          videoEle.play();
          setIsPlay(true);
        });
        videoEle.addEventListener("timeupdate", () => {
          if (videoEle.duration - videoEle.currentTime > 30) {
            videoEle.currentTime = videoEle.duration - 10;
          }
        });
      });
    }
    // if (videoEle.canPlayType("application/vnd.apple.mpegurl") && isIOS) {
    //   tryLink(playUrl).then((data) => {
    //     videoEle.src = playUrl;
    //     videoEle.addEventListener("loadedmetadata", () => {
    //       videoEle.play();
    //       setIsPlay(true);
    //     });
    //   });
    // } else if (Hls.isSupported()) {
    //   tryPcLink(playUrl).then((data) => {
    //     hls = new Hls();
    //     hls.loadSource(playUrl);
    //     hls.attachMedia(videoEle);
    //     videoEle.addEventListener("loadedmetadata", () => {
    //       videoEle.play();
    //       setIsPlay(true);
    //     });
    //   });
    // }
    return () => {
      if (hls) hls.destroy();
      hls = null;
    };
  }, [playUrl]);

  const onChoosePlaceChannel = async ({ id, name }) => {
    setActiveIdfo({
      monitoringNodeId: id,
      belongs: null,
      name,
      type: 3,
    });
    const result = await getRoomDeviceById({
      orgId: currentOrg.id,
      roomId: id,
    });
    if (result.code === 0) {
      let deviceList = result.data.deviceList || [];
      deviceList = deviceList.filter((item) => item.belongs === 10000);
      const magicDeviceList = result.data.magicDeviceList || [];
      if (deviceList.length > 0) {
        const { belongs, monitoringNodeId } = deviceList[0];
        getPatrolLiveByFree({
          belong: belongs,
          nodeId: monitoringNodeId,
          bureauId: currentOrg.id,
        }).then((result) => {
          if (result && result.code === 0) {
            const urlList = result.data.map((item) => item.phoneUrl);
            setUrls(() => {
              const list = urlList.map((item) => ({
                phoneUrl: item,
              }));
              return list;
            });
            setIsPlay(false);
            setPlayUrl(urlList[0]);
          }
        });
        return;
      }
      if (magicDeviceList.length > 0) {
        const result = await getMagicLive({ roomId: id });
        if (result && result.code === 0 && result.data) {
          let urlList = result.data.liveUrls || [];
          if (result.data.success === 500) {
            urlList = urlList.filter((item) => item.status === 1);
          }
          urlList = urlList.map((item) => item.m3u8Url);

          setUrls(() => {
            const list = urlList.map((item) => ({
              phoneUrl: item,
            }));
            return list;
          });
          setIsPlay(false);
          setPlayUrl(urlList[0]);
        }
        return;
      }
    }
  };
  const onChooseGradeChannel = async ({ roomId, name }) => {
    setActiveIdfo({
      monitoringNodeId: roomId,
      belongs: null,
      name: name,
      type: 4,
    });
    const result = await getRoomDeviceById({
      orgId: currentOrg.id,
      roomId: roomId,
    });
    if (result.code === 0) {
      let deviceList = result.data.deviceList || [];
      // deviceList = deviceList.filter((item) => item.belongs === 10000);
      const magicDeviceList = result.data.magicDeviceList || [];
      if (deviceList.length > 0) {
        const { belongs, monitoringNodeId } = deviceList[0];
        getPatrolLiveByFree({
          belong: belongs,
          nodeId: monitoringNodeId,
          bureauId: currentOrg.id,
        }).then((result) => {
          if (result && result.code === 0) {
            const urlList = result.data.map((item) => item.phoneUrl);
            setUrls(() => {
              const list = urlList.map((item) => ({
                phoneUrl: item,
              }));
              return list;
            });
            setIsPlay(false);
            setPlayUrl(urlList[0]);
          }
        });
        return;
      }
      if (magicDeviceList.length > 0) {
        const result = await getMagicLive({ roomId: roomId });
        if (result && result.code === 0 && result.data) {
          let urlList = result.data.liveUrls || [];
          if (result.data.success === 500) {
            urlList = urlList.filter((item) => item.status === 1);
          }
          urlList = urlList.map((item) => item.m3u8Url);

          setUrls(() => {
            const list = urlList.map((item) => ({
              phoneUrl: item,
            }));
            return list;
          });
          setIsPlay(false);
          setPlayUrl(urlList[0]);
        }
        return;
      }
    }
  };
  const onChoosedChannel = ({ monitoringNodeId, belongs, name, urls }) => {
    // const isIOS = $getIsIOS()
    setActiveIdfo({ monitoringNodeId, belongs, name, type: 2 });
    setPlayUrl("");
    setIsPlay(false);
    if (urls) {
      setUrls(urls);
      setPlayUrl(urls[0].phoneUrl);
      return;
    }
    getMobilePlayUrl({
      nodeId: monitoringNodeId,
      belong: belongs,
      type: 3,
    })
      .then((res) => {
        if (res.code === 0) {
          setUrls(res.data);
          let url = res.data[0].phoneUrl;
          if (url) {
            setPlayUrl(url);
          }
        } else {
          setPlayUrl("");
        }
      })
      .catch((error) => {
        setPlayUrl("");
      });
  };
  /* ============评价=========== */
  const nextChannel = () => {
    if (active === 1) {
      const findIndex = classRoomPlace.findIndex(
        (item) => item.id === activeInfo.monitoringNodeId
      );
      if (findIndex === -1) {
        onChoosePlaceChannel(classRoomPlace[0]);
        return;
      }
      if (findIndex + 1 === classRoomPlace.length) {
        return Toast.show("已经是最后一个了");
      } else {
        onChoosePlaceChannel(classRoomPlace[findIndex + 1]);
      }
      return;
    }

    const findIndex = allTypes.findIndex(
      (item) => item.monitoringNodeId === activeInfo.monitoringNodeId
    );
    if (findIndex !== -1) {
      if (findIndex + 1 < allTypes.length) {
        onChoosedChannel(allTypes[findIndex + 1]);
      } else {
        onChoosedChannel(allTypes[0]);
      }
    } else {
      onChoosedChannel(allTypes[0]);
    }
  };
  const prevChannel = () => {
    if (active === 1) {
      const findIndex = classRoomPlace.findIndex(
        (item) => item.id === activeInfo.monitoringNodeId
      );
      if (findIndex === -1) {
        onChoosePlaceChannel(classRoomPlace[0]);
        return;
      }
      if (findIndex !== 0) {
        onChoosePlaceChannel(classRoomPlace[findIndex - 1]);
        return;
      }
      return Toast.show("已经是第一个了");
    }
    const findIndex = allTypes.findIndex(
      (item) => item.monitoringNodeId === activeInfo.monitoringNodeId
    );
    if (findIndex !== -1) {
      if (findIndex !== 0) {
        onChoosedChannel(allTypes[findIndex - 1]);
      } else {
        onChoosedChannel(allTypes[allTypes.length - 1]);
      }
    } else {
      onChoosedChannel(allTypes[0]);
    }
  };
  const maxContentHeight = 60 + 40 + top + "px";
  /* 预览 */
  const [prevInfo, setPreview] = useState({
    show: false,
    list: [],
    index: -1,
  });
  const handle2ClosePreview = () => {
    setPreview({
      show: false,
      list: [],
      index: -1,
    });
  };

  const [tabSwitch, setTabSwitch] = useState({
    roomSwitch: 0,
    monitorSwitch: 0,
    equipmentSwitch: 0,
    livingSwitch: 0,
    classSwitch: 0,
    openWaterMark: 0
  });
  const [showTalk, setShowTalk] = useState(false);
  useEffect(() => {
    getSetting().then((res) => {
      if (res.code === 0) {
        setTabSwitch({
          roomSwitch: res.data.patrolFreeSetting.roomSwitch,
          monitorSwitch: res.data.patrolFreeSetting.monitorSwitch,
          equipmentSwitch: res.data.patrolFreeSetting.equipmentSwitch,
          livingSwitch: res.data.patrolFreeSetting.livingSwitch,
          classSwitch: res.data.patrolFreeSetting.classSwitch,
          openWaterMark: res.data.patrolSystemSetting.openWaterMark
        });
        if (res.data.patrolFreeSetting.roomSwitch === 1) {
          setActive(1);
        } else if (res.data.patrolFreeSetting.monitorSwitch === 1) {
          setActive(2);
        } else if (res.data.patrolFreeSetting.equipmentSwitch === 1) {
          setActive(3);
        } else if (res.data.patrolFreeSetting.livingSwitch === 1) {
          setActive(4);
        } else if (res.data.patrolFreeSetting.classSwitch === 1) {
          setActive(5);
        }
      }
    });
  }, []);

  const watermarkRef = useWatermark(tabSwitch.openWaterMark == 1);
  
  return (
    <Root style={{ background: active === 1 ? "#fff" : "#f4f6f9" }}>
      <div id="videoContainer" className="videoContainer" hideFullscreenButton={tabSwitch.openWaterMark === 1}>
        {!activeInfo.monitoringNodeId && (
          <div className="absolute cover">
            <span>请选择通道</span>
          </div>
        )}

        {!isPlay && activeInfo.monitoringNodeId && (
          <div className="absolute cover">
            <span>正在获取画面...</span>
          </div>
        )}

        {isPlay && urls.length > 1 && (
          <div className="absolute tips">
            <div
              onClick={() => setUrlList(!showUrlList)}
              className="tip-cur flex items-center justify-center"
            >
              <span>画面{urlIndex + 1}</span>
              <i className="iconfont iconqiehuan ml-1"></i>
            </div>
            {showUrlList && (
              <div className="tip-list">
                {urls.map((item, index) => (
                  <div
                    onClick={() => {
                      setPlayUrl(item.phoneUrl);
                      setUrlList(false);
                    }}
                    key={index}
                    className="tip-item"
                  >
                    画面{index + 1}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        <video
          id="videoPlayer"
          playsInline
          muted
          webkitplaysinline="true"
          x5-playsinline="true"
          controls
          x5-video-player-type="h5-page"
        />

        {/* 水印 */}
        <div ref={watermarkRef} className="document-container"></div>
        
      </div>
      {/* 预览 */}
      {prevInfo.show && (
        <MobilePreviewTool onClose={handle2ClosePreview} {...prevInfo} />
      )}
      {/* 预览 */}
      <div
        className="tabOrg"
        onClick={() => {
          if (!currentOrg.ISJD) return;
          setCurrentOrg((pre) => ({
            ...pre,
            showOrgPop: true,
          }));
        }}
      >
        <div className="orgName">{currentOrg.name}</div>
        <i className="iconfont icon-arrow-down"></i>
      </div>
      <div className="tabs flex">
        {tabSwitch.roomSwitch === 1 && (
          <div
            onClick={() => setActive(1)}
            className="flex-1 text-center items-center"
          >
            <span className={active === 1 ? "active" : ""}>
              教室 {classPlaceNum}
            </span>
          </div>
        )}
        {tabSwitch.classSwitch === 1 && (
          <div
            onClick={() => setActive(5)}
            className="flex-1 text-center items-center"
          >
            <span className={active === 5 ? "active" : ""}>
              班级 {gradePlaceNum}
            </span>
          </div>
        )}
        {tabSwitch.monitorSwitch === 1 && (
          <div
            onClick={() => setActive(2)}
            className="flex-1 text-center items-center"
          >
            <span className={active === 2 ? "active" : ""}>
              监控 {type1.length}
            </span>
          </div>
        )}

        {/* {tabSwitch.equipmentSwitch === 1 && (
          <div
            onClick={() => setActive(3)}
            className="flex-1 text-center items-center"
          >
            <span className={active === 3 ? "active" : ""}>
              录播设备 {type2.length}
            </span>
          </div>
        )}

        {tabSwitch.livingSwitch === 1 && (
          <div
            onClick={() => setActive(4)}
            className="flex-1 text-center items-center"
          >
            <span className={active === 4 ? "active" : ""}>
              直播 {live.length}
            </span>
          </div>
        )} */}
      </div>
      <div
        className="overflow-y-auto"
        style={{ height: `calc(100vh - ${maxContentHeight})` }}
      >
        {/* 通道 */}
        {active === 1 && (
          <PlaceSideBar
            classPlace={classPlace}
            activeInfo={activeInfo}
            isFree={1}
            onChoose={onChoosePlaceChannel}
          />
        )}
        {active == 5 && (
          <GradeSideBar
            gradePlace={gradePlace}
            activeInfo={activeInfo}
            onChoose={onChooseGradeChannel}
          />
        )}
        {active === 2 && (
          <MonitorList
            onChoose={onChoosedChannel}
            activeInfo={activeInfo}
            type1={type1}
          />
        )}
        {active === 3 && (
          <EquipList
            onChoose={onChoosedChannel}
            activeInfo={activeInfo}
            type2={type2}
          />
        )}
        {active === 4 && (
          <LiveList
            onChoose={onChoosedChannel}
            activeInfo={activeInfo}
            live={live}
          />
        )}
      </div>
      <PenBar
        onClick={() => {
          if (!activeInfo.monitoringNodeId) {
            return Toast.show({
              icon: "fail",
              content: "请选择通道",
            });
          }
          setShowDis(true);
        }}
      />
      {/* 喊话功能 */}
      <TalkBar
        onClick={() => {
          if (!activeInfo.monitoringNodeId) {
            return Toast.show({
              icon: "fail",
              content: "请选择通道",
            });
          }
          setShowTalk(true);
        }}
      />
      {showTalk && (
        <Talk
          top={top}
          activeId={activeInfo.monitoringNodeId}
          hideTalk={() => {
            setShowTalk(false);
          }}
        ></Talk>
      )}
      {showDis && (
        <Dis
          top={top}
          activeInfo={activeInfo}
          playUrl={playUrl}
          hideDis={() => {
            setShowDis(false);
          }}
          nextChannel={nextChannel}
          prevChannel={prevChannel}
          setPreview={setPreview}
        />
      )}

      <OrgPopup
        onHide={() => {
          setCurrentOrg((pre) => ({
            ...pre,
            showOrgPop: false,
          }));
        }}
        show={currentOrg.showOrgPop}
        onChange={(id, name) => {
          setCurrentOrg((pre) => ({
            ...pre,
            name,
            id,
            showOrgPop: false,
          }));
        }}
      />

      {/* 控制云台 */}
      <ControlBtn
        onClick={() => {
          setShowControl(true);
        }}
      />
      {showControl && (
        <ControlPanel
          top={top}
          activeInfo={activeInfo}
          onHide={() => {
            setShowControl(false);
          }}
        />
      )}
      {/* 控制云台 */}
    </Root>
  );
}

const EquipRoot = styled.div`
  .item {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    border-radius: 6px;
    background: #fff;
    padding: 0 16px;
    border: 2px solid transparent;
    font-size: 16px;
  }
  .item .label {
    position: relative;
    padding-left: 16px;
    &::before {
      content: "";
      width: 10px;
      height: 10px;
      border-radius: 50%;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
    &.online::before {
      background: #41cd85;
    }
    &.unline::before {
      background: #ccc;
    }
  }
  .item.active {
    color: #4465f3;
    border-color: #4465f3;
    background: rgba(68, 101, 243, 0.07);
  }
`;
function EquipList({ activeInfo, type2, onChoose }) {
  return (
    <EquipRoot className="p-5">
      {type2.map((item) => (
        <div
          onClick={() => onChoose(item)}
          key={item.monitoringNodeId}
          className={`item ${item.monitoringNodeId === activeInfo.monitoringNodeId
            ? "active"
            : ""
            }`}
        >
          <div
            className={`label flex-1 truncate ${item.online === 1 ? "online" : "unline"
              }`}
          >
            {item.name}
          </div>
          {item.monitoringNodeId === activeInfo.monitoringNodeId && (
            <CheckCircleFill style={{ color: "#4465F3" }} />
          )}
        </div>
      ))}
    </EquipRoot>
  );
}

const MonitorRoot = styled.div`
  .item {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    border-radius: 6px;
    background: #fff;
    padding: 0 16px;
    border: 2px solid transparent;
    font-size: 16px;
  }
  .item.active {
    color: #4465f3;
    border-color: #4465f3;
    background: rgba(68, 101, 243, 0.07);
  }
`;
function MonitorList({ type1, activeInfo, onChoose }) {
  return (
    <MonitorRoot className="p-5">
      {type1.map((item) => (
        <div
          onClick={() => onChoose(item)}
          key={item.monitoringNodeId}
          className={`item ${item.monitoringNodeId === activeInfo.monitoringNodeId
            ? "active"
            : ""
            }`}
        >
          <div className="label flex-1 truncate">{item.name}</div>
          {item.monitoringNodeId === activeInfo.monitoringNodeId && (
            <CheckCircleFill style={{ color: "#4465F3" }} />
          )}
        </div>
      ))}
    </MonitorRoot>
  );
}
function LiveList({ live, activeInfo, onChoose }) {
  return (
    <MonitorRoot className="p-5">
      {live.map((item) => (
        <div
          onClick={() => onChoose(item)}
          key={item.monitoringNodeId}
          className={`item ${item.monitoringNodeId === activeInfo.monitoringNodeId
            ? "active"
            : ""
            }`}
        >
          <div className="label flex-1 truncate">{item.eventName}</div>
          {item.monitoringNodeId === activeInfo.monitoringNodeId && (
            <CheckCircleFill style={{ color: "#4465F3" }} />
          )}
        </div>
      ))}
    </MonitorRoot>
  );
}

export default Online;
