import React, { useState, useRef, useEffect } from "react";
import styled from "styled-components";
import { CloseOutline } from "antd-mobile-icons";
import { Toast, Grid, Button, Dialog } from "antd-mobile";
import { $fileType } from "@/tools";
import { useCountDown } from "ahooks";
import { CloseOutlined, LoadingOutlined } from "@ant-design/icons";
import TalkBanner from "@/assets/images/mobile/talkBanner.png";
import { Checkbox } from "antd";
import { useMqtt, useAudio } from "@/hooks";
const DisRoot = styled.div`
  position: absolute;
  top: ${(props) => props.top + "px"};
  left: 0;
  bottom: 0;
  right: 0;
  background: #ffffff;
  z-index: 20;
  padding: 16px 16px 90px;
  overflow-y: auto;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title {
      font-size: 16px;
      color: #262626;
      font-weight: bold;
    }
    .anticon {
      font-size: 16px;
      cursor: pointer;
    }
  }
  .content {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    flex-wrap: wrap;
    height: calc(100% - 90px);
    img {
      width: 80%;
    }
    .title1,
    .title2 {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 20px;
      font-size: 15px;
      color: #262626;
      font-weight: bold;
    }
    .title1 {
      .loading {
        color: #007aff;
      }
      span {
        margin-left: 10px;
      }
    }
    .openTalk {
      margin-top: 20px;
    }
    .btn {
      position: absolute;
      bottom: 36px;
      //   margin-top: 20px;
      width: 80%;
      display: flex;
      justify-content: center;
      align-items: center;
      .button {
        width: 100%;
      }
    }
  }
`;
function Talk({ top, activeId, hideTalk }) {
  const mq = useMqtt();
  console.log('mq', mq)
  const {
    connectionState, // 0-未连接, 1-连接中, 2-已连接
    micEnabled,
    startPublish,
    stopPublish,
    toggleMicrophone,
  } = useAudio(activeId, mq.clientId);
  const onMicChange = (e) => {
    toggleMicrophone(e.target.checked);
  };
  return (
    <DisRoot top={top}>
      <div className="header">
        <span className="title">教室喊话</span>
        <CloseOutlined onClick={() => hideTalk()} />
      </div>
      <div className="content">
        <img src={TalkBanner} alt="" />

        {/* 连接中 */}
        {connectionState === 1 && (
          <div className="title1">
            <LoadingOutlined className="loading" />
            <span>喊话通道建立中，请稍等...</span>
          </div>
        )}

        {/* 已连接 */}
        {connectionState === 2 && (
          <>
            <div className="title2">喊话通道已经建立，请喊话</div>
            <div className="openTalk">
              <Checkbox
                className="checkboxCustom"
                checked={micEnabled}
                onChange={onMicChange}
              >
                开启麦克风
              </Checkbox>
            </div>
          </>
        )}

        {connectionState === 0 ? (
          <div className="btn">
            <Button
              className="button"
              onClick={startPublish}
              shape="rounded"
              color="primary"
            >
              开始喊话
            </Button>
          </div>
        ) : (
          <div className="btn">
            <Button
              className="button"
              onClick={stopPublish}
              shape="rounded"
              color="primary"
            >
              结束喊话
            </Button>
          </div>
        )}
      </div>
    </DisRoot>
  );
}
export default Talk;
