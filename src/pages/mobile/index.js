import React, { useState, useEffect } from "react";
import { useRouterGo } from "@/hooks";
import styled from "styled-components";
import { getMyTasksList, getSetting } from "@/api";
import moment from "moment";
import { CloseOutlined, ClockCircleOutlined } from "@ant-design/icons";
import Empt from "@/pages/common/empt";
import { Toast, ProgressBar, Space } from "antd-mobile";
import { IndexTab } from "./components/indexTab";
const Root = styled.div`
  overflow: auto;
  min-height: 100vh;
  background: #ededed;
  .tip {
    color: rgba(250, 173, 20, 1);
    height: 40px;
    padding: 0 16px;
    background: rgba(250, 173, 20, 0.15);
  }
  .item {
    background: #fff;
    border-radius: 8px;
    padding: 18px 12px;
    margin-bottom: 12px;
    .title {
      color: #1a1a1a;
      font-weight: 500;
      font-size: 16px;
    }
    .date {
      color: #808080;
    }
    .btns {
      margin-top: 16px;
      div {
        height: 40px;
        color: #007aff;
        border-radius: 50px;
        line-height: 40px;
        text-align: center;
        background: rgba(0, 122, 255, 0.08);
      }
    }
  }
`;

function checkIsShowTourBtn({ startTime, endTime }) {
  const now = +new Date();
  if (startTime < now && now < endTime + 86400000) {
    return true;
  }
  return false;
}

function Mobile() {
  const navigate = useRouterGo();
  const [show, setShow] = useState(true);
  const [list, setList] = useState([]);

  const [mode, setMode] = useState({
    offlineTour: true,
    videoTour: true,
  });

  useEffect(() => {
    getMyTasksList({
      pageNo: 1,
      pageSize: 999,
    }).then((res) => {
      if (res.code === 0) {
        const data = res.data.map((item) => {
          if (item.usePatrolRule !== 1) return item;
          item.percent = 0;
          if (item.patrolRecordCount > item.patrolRecordAsk) {
            item.percent = 100;
          } else if (item.patrolRecordAsk) {
            item.percent =
              (item.patrolRecordCount / item.patrolRecordAsk) * 100;
          }
          return item;
        });
        setList(data);
      }
    });

    getSetting().then((res) => {
      if (res.code === 0) {
        setMode({
          offlineTour: res.data.patrolSystemSetting.offlineTour === 1,
          videoTour: res.data.patrolSystemSetting.videoTour === 1,
        });
      }
    });
  }, []);

  const handleOffLine = (item) => {
    const startTime = item.startTime;
    if (+new Date() < startTime) {
      Toast.show({
        icon: "fail",
        content: "线下巡课还未开始",
      });
    } else {
      navigate(`/mobile/offLine/${item.taskId}`);
    }
  };

  function modelOption(item) {
    const labeljsx = [];
    if (mode.videoTour && checkIsShowTourBtn(item)) {
      labeljsx.push(
        <div
          className="flex-1"
          onClick={() => navigate(`/mobile/onLine/${item.taskId}`)}
        >
          视频巡课
        </div>
      );
    }
    if (mode.offlineTour) {
      if (labeljsx.length === 1) {
        labeljsx.push(<span className="w-4"></span>);
      }
      labeljsx.push(
        <div className="flex-1" onClick={() => handleOffLine(item)}>
          线下巡课
        </div>
      );
    }
    return labeljsx;
  }

  return (
    <Root>
      {show && (
        <div className="tip flex items-center">
          <span className="flex-1">在线巡课:通过观看视频的方式巡课</span>
          <CloseOutlined onClick={() => setShow(false)} />
        </div>
      )}
      <IndexTab />
      <div className="p-4">
        {list.map((item, index) => (
          <div className="item" key={index}>
            <div className="title truncate">{item.taskName}</div>
            <div className="date flex items-center my-2">
              <ClockCircleOutlined />
              <span className="ml-2">
                {moment(item.startTime).format("YYYY-MM-DD")}
              </span>
              <span className="mx-2">至</span>
              <span>{moment(item.endTime).format("YYYY-MM-DD")}</span>
            </div>
            <div className="btns flex items-center">{modelOption(item)}</div>

            {/* 考核任务 */}
            {item.usePatrolRule === 1 && (
              <div className="mt-4">
                <div className="flex item-cneter justify-between mb-3 text-sm">
                  <div style={{ color: "#808080" }}>考核任务</div>
                  <div>
                    {item.patrolRecordCount}/{item.patrolRecordAsk}
                  </div>
                </div>
                <ProgressBar
                  percent={item.percent}
                  style={{
                    "--track-width": "8px",
                    "--fill-color":
                      item.percent === 100 ? "#17BE6B" : "#007AFF",
                  }}
                />
              </div>
            )}
          </div>
        ))}
        {list.length === 0 && <Empt />}
      </div>
    </Root>
  );
}

export default Mobile;
