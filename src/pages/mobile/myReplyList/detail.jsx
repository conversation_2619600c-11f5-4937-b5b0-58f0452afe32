import React, { useEffect, useMemo, useState } from "react";
import styled from "styled-components";
import { Loading, FormRes } from "@/components";
import moment from "moment";
import { Grid } from "antd-mobile";
import { selectPatrolRecordDetail, getPatrolRecordDetail } from "@/api";
import { useParams, useSearchParams } from "react-router-dom";
import { PicBox } from "@/pages/mobile/common/picBox.js";
import MobilePreviewTool from "./../common/mobilePreviewTool";

const Root = styled.div`
  position: relative;
  min-height: 100vh;
  background: #ededed;
  .banner {
    height: 36px;
    background: linear-gradient(90deg, #007aff 0%, #007aff 65%);
    .objectRecord {
      padding: 16px;
      color: #ffffff;
      font-size: 15px;
      font-weight: 500;
    }
  }
  .banner1 {
    height: 75px;
    background: linear-gradient(90deg, #007aff 0%, #007aff 65%);
    .objectRecord {
      padding: 16px;
      color: #ffffff;
      font-size: 15px;
      font-weight: 500;
    }
  }
  .content {
    margin-top: -36px;
    padding: 12px;
  }
  .content1 {
    margin-top: -36px;
    padding: 12px;
  }
  .detail {
    padding: 16px 40px 16px 16px;
    background: #fff;
    border-radius: 8px;
    .time {
      color: #808080;
      font-size: 14px;
    }
    .comment {
      word-break: break-all;
      color: #1a1a1a;
      font-size: 14px;
    }
  }
`;
const Title = styled.div`
  color: #000;
  font-size: 16px;
  padding-left: 10px;
  position: relative;
  &::before {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    content: "";
    width: 4px;
    height: 13px;
    background: #4465f3;
    border-radius: 50px;
  }
`;

export default function ReplyDetail() {
  const { id } = useParams();
  const [info, setInfo] = useState(null);
  const [searchParams] = useSearchParams();
  const type = searchParams.get("type");
  useEffect(() => {
    if (type == 1) {
      selectPatrolRecordDetail({ patrolRecordId: id }).then((res) => {
        console.log("type", type);
        setInfo(res.data);
      });
    } else {
      getPatrolRecordDetail({ patrolRecordId: id }).then((res) => {
        console.log("res", res.data);
        setInfo(res.data);
      });
    }
  }, [id]);

  const attr = useMemo(() => {
    if (!info) return [];
    return info.patrolRecordAttrList.map((item) => {
      if (item.attrType === 1) {
        return { coverUrl: item.attrPath, fileUrl: item.attrPath };
      }
      return { coverUrl: item.attrPic, fileUrl: item.attrPath };
    });
  }, [info]);

  const [prevInfo, setPreview] = useState({
    show: false,
    list: [],
    index: -1,
  });
  const handle2Preview = (index) => {
    const list = attr.map((item) => item.fileUrl);
    setPreview({
      show: true,
      list,
      index,
    });
  };

  const hasTable = useMemo(() => {
    if (!info) return false;
    if (info.useTable === 1 && info.formTemplateAnswerId) {
      return true;
    }
    return false;
  }, [info]);

  if (!info) {
    return <Loading />;
  }
  return (
    <Root>
      <div className={type == 1 ? "banner" : "banner1"}>
        {type == 2 && (
          <div className="objectRecord">
            <i className="iconfont iconyonghu11"></i>
            <span>评价对象：{info.patrolCommentObject || "暂无评价对象"}</span>
          </div>
        )}
      </div>
      <div className={type == 1 ? "content" : "content1"}>
        <div className="detail">
          <Title>评价详情</Title>
          <div className="time my-2">
            {moment(info.patrolTime).format("YY-MM-DD HH:mm")}
          </div>
          <div className="comment">{info.patrolComment}</div>
          {attr.length > 0 && (
            <div className="mt-4">
              <Grid columns={3} gap={8}>
                {attr.map((item, index) => (
                  <Grid.Item key={index}>
                    <PicBox
                      mode="look"
                      onPreview={() => handle2Preview(index)}
                      {...item}
                    />
                  </Grid.Item>
                ))}
              </Grid>
            </div>
          )}
        </div>

        {hasTable && (
          <div className="detail mt-3">
            <Title className="mb-4">量表</Title>
            <FormRes
              formInfoId={info.formInfoId}
              formTemplateInfoId={info.formTemplateInfoId}
              formTemplateAnswerId={info.formTemplateAnswerId}
            />
          </div>
        )}
      </div>
      {prevInfo.show && (
        <MobilePreviewTool
          onClose={() => {
            setPreview({
              show: false,
              list: [],
              index: -1,
            });
          }}
          {...prevInfo}
        />
      )}
    </Root>
  );
}
