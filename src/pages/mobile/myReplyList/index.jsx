import React, { useEffect, useState } from "react";
import styled from "styled-components";
import { useRouterGo } from "@/hooks";
import {
  selectRecordedMeByPage,
  appraiseStatistics,
  getRecordList,
} from "@/api";
import moment from "moment";
import { CloseOutlined } from "@ant-design/icons";
import { CalendarPicker } from "antd-mobile";
import { Input } from "antd";
import iconRecord from "@/assets/images/mobile/iconRecord.png";
const Root = styled.div`
  min-height: 100vh;
  background: #f2f2f6;
  padding: 12px;
  .total {
    font-size: 16px;
    color: #1a1a1a;
  }
  .headerTab {
    height: 44px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .header_left {
      // width: 80%;
      background: #ffffff;
      height: 44px;
      border-radius: 6px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px;
      .accordRecord,
      .passivity {
        padding: 7px 12px;
        font-size: 15px;
        color: #262626;
      }
      .activeRecord {
        background: #f5f5f5;
      }
    }
    .header_right {
      width: 44px;
      height: 44px;
      background: #ffffff;
      border-radius: 6px;
      display: flex;
      justify-content: center;
      align-items: center;
      .iconrili1 {
        font-size: 20px;
        color: #262626;
      }
    }
  }

  .time1 {
    margin-top: 16px;
    font-size: 15px;
    color: #262626;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 8px;
  }
  .filter {
    margin-top: 16px;
  }
`;

const ReplyItemRoot = styled.div`
  margin-top: 12px;
  background: #fff;
  border-radius: 6px;
  padding: 16px;
  display: flex;
  .mask {
    text-align: center;
    line-height: 40px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(68, 101, 243, 0.12);
    i {
      font-size: 24px;
    }
  }
  .title {
    word-break: break-all;
    /* word-wrap: break-word; */
    font-size: 16px;
    font-weight: 500;
    color: #1a1a1a;

    text-overflow: -o-ellipsis-lastline;
    overflow: hidden; //溢出内容隐藏
    text-overflow: ellipsis; //文本溢出部分用省略号表示
    display: -webkit-box; //特别显示模式
    -webkit-line-clamp: 2; //行数
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .time {
    color: #808080;
  }
  .count {
    font-size: 13px;
    color: #808080;
    i,
    span {
      vertical-align: middle;
    }
  }
`;
const PassivityItemRoot = styled.div`
  margin-top: 12px;
  background: #fff;
  border-radius: 6px;
  padding: 16px;
  display: flex;
  .mask {
    text-align: center;
    line-height: 40px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #ebfff1;
    img {
      width: 24px;
      height: 24px;
    }
  }
  .title {
    word-break: break-all;
    /* word-wrap: break-word; */
    font-size: 16px;
    font-weight: 500;
    color: #1a1a1a;

    text-overflow: -o-ellipsis-lastline;
    overflow: hidden; //溢出内容隐藏
    text-overflow: ellipsis; //文本溢出部分用省略号表示
    display: -webkit-box; //特别显示模式
    -webkit-line-clamp: 2; //行数
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .time {
    color: #808080;
  }
  .count {
    font-size: 13px;
    color: #808080;
    i,
    span {
      vertical-align: middle;
    }
  }
`;
function ReplyItem({
  patrolComment,
  patrolTime,
  picCount,
  videoCount,
  useTable,
  patrolRecordId,
  formTemplateAnswerId,
}) {
  const navigate = useRouterGo();
  return (
    <ReplyItemRoot
      onClick={() => {
        navigate(`/mobile/reply/${patrolRecordId}?type=1`);
      }}
    >
      <div className="mask">
        <i
          className="iconfont icon-docment2"
          style={{ color: "RGBA(68, 101, 243, 1)" }}
        ></i>
      </div>
      <div className="info ml-3 flex-1">
        <div className="title">{patrolComment}</div>
        <div className="flex items-center justify-between mt-4">
          <div className="time">
            {moment(patrolTime).format("YY-MM-DD HH:mm")}
          </div>
          <div className="count flex items-center">
            {useTable === 1 && formTemplateAnswerId && (
              <i className="iconfont icon-docment2"></i>
            )}
            <div className="ml-4">
              <i className="iconfont icontupian"></i>
              <span className="ml-1">{picCount}</span>
            </div>

            <div className="ml-4">
              <i className="iconfont iconbofang"></i>
              <span className="ml-1">{videoCount}</span>
            </div>
          </div>
        </div>
      </div>
    </ReplyItemRoot>
  );
}
function PassivityItem({
  patrolComment,
  patrolTime,
  picCount,
  videoCount,
  useTable,
  patrolRecordId,
  formTemplateAnswerId,
  patrolCommentObject,
}) {
  const navigate = useRouterGo();
  return (
    <PassivityItemRoot
      onClick={() => {
        navigate(`/mobile/reply/${patrolRecordId}?type=2`);
      }}
    >
      <div className="mask">
        {/* <i
          className="iconfont icon-docment2"
          style={{ color: "RGBA(68, 101, 243, 1)" }}
        ></i> */}
        <img src={iconRecord} alt="" />
      </div>
      <div className="info ml-3 flex-1">
        <div className="title">
          {patrolCommentObject ? patrolCommentObject : "未设置评价对象"}
        </div>
        <div className="flex items-center justify-between mt-4">
          <div className="time">
            {moment(patrolTime).format("YY-MM-DD HH:mm")}
          </div>
          <div className="count flex items-center">
            {useTable === 1 && formTemplateAnswerId && (
              <i className="iconfont icon-docment2"></i>
            )}
            <div className="ml-4">
              <i className="iconfont icontupian"></i>
              <span className="ml-1">{picCount}</span>
            </div>

            <div className="ml-4">
              <i className="iconfont iconbofang"></i>
              <span className="ml-1">{videoCount}</span>
            </div>
          </div>
        </div>
      </div>
    </PassivityItemRoot>
  );
}
export default function ReplyList() {
  document.title = "我的巡课评价";
  useEffect(() => {
    document.title = "我的巡课评价";
    return () => {
      document.title = "在线巡课"; // 或者设置为默认标题
    };
  }, []);
  const [list, setList] = useState([]);
  const [active, setActive] = useState(1);
  const [timeValue, setTime] = useState(null);
  const [timeVisible, setTimeVisible] = useState(false);
  const [targetName, setTargetName] = useState(null);
  const [data, setDataNum] = useState({
    myCount: 0 /* 我的评价数量 */,
    recordedCount: 0 /* 我的被评价数量 */,
  });
  const defaultRange: [Date, Date] = [
    moment().toDate(),
    moment().add(2, "day").toDate(),
  ];
  useEffect(() => {
    appraiseStatistics({
      startTime: timeValue ? timeValue.split(' ~ ')[0] : null,
      endTime: timeValue ? timeValue.split(' ~ ')[1] : null,
    }).then((res) => {
      if (res.code == 0) {
        setDataNum(res.data);
      }
    });
  }, [timeValue]);
  // useEffect(() => {
  //   selectRecordedMeByPage({
  //     pageNo: 1,
  //     pageSize: 9999,
  //     month: "",
  //   }).then((res) => {
  //     if (res.code === 0) {
  //       setList(res.data);
  //     }
  //   });
  // }, []);
  useEffect(() => {
    console.log('timeValue', timeValue)
    getRecordList({
      pageNo: 1,
      pageSize: 9999,
      startTime: timeValue ? timeValue.split(' ~ ')[0] : null,
      endTime: timeValue ? timeValue.split(' ~ ')[1] : null,
      type: active,
      targetName: active == 2 ? targetName : null,
    }).then((res) => {
      if (res.code === 0) {
        console.log("res.data", res.data);
        setList(res.data);
      }
    });
  }, [timeValue, active, targetName]);
  const handleClearTime = () => {
    console.log("清除时间");
    setTime("");
  };
  const handleChangeTime = (val) => {
    console.log("val", val);
    let start = moment(val[0]).format("YYYY-MM-DD");
    let end = moment(val[1]).format("YYYY-MM-DD");
    console.log('start', start)
    console.log('end', end)
    setTime(`${start} ~ ${end}`);
    console.log(timeValue)
  };
  return (
    <Root>
      <div className="headerTab">
        <div className="header_left">
          <div
            className={`accordRecord  ${active === 1 ? "activeRecord" : ""}`}
            onClick={() => setActive(1)}
          >
            <span className="title">我的被评记录</span>
            <span className="num">{data.recordedCount}</span>
          </div>
          <div
            className={`passivity  ${active === 2 ? "activeRecord" : ""}`}
            onClick={() => setActive(2)}
          >
            <span className="title">我的评价记录</span>
            <span className="num">{data.myCount}</span>
          </div>
        </div>
        <div className="header_right" onClick={() => setTimeVisible(true)}>
          <i className="iconfont iconrili1"></i>
        </div>
      </div>
      {timeValue && (
        <div className="time1">
          <div className="left">{timeValue}</div>
          <div className="right" onClick={() => handleClearTime()}>
            <CloseOutlined />
          </div>
        </div>
      )}
      <div className="filter">
        {active == 2 && (
          <Input.Search
            allowClear
            onSearch={(value) => {
              setTargetName(value);
            }}
            placeholder="请输入评价对象"
          />
        )}
      </div>

      {active == 1 &&
        list.map((item, index) => (
          <ReplyItem {...item} key={index}></ReplyItem>
        ))}
      {active == 2 &&
        list.map((item, index) => (
          <PassivityItem {...item} key={index}></PassivityItem>
        ))}
      <CalendarPicker
        confirmText="确定"
        visible={timeVisible}
        defaultValue={defaultRange}
        selectionMode="range"
        onClose={() => setTimeVisible(false)}
        onMaskClick={() => setTimeVisible(false)}
        onConfirm={(val) => handleChangeTime(val)}
      />
      {/* <div className="total">共{list.length}条评价</div>
    {
      list.map((item,index) =>  <ReplyItem {...item} key={index}></ReplyItem>)
    } */}
    </Root>
  );
}
