import React, { useState, useRef, useEffect, useMemo } from "react";
import { useParams } from "react-router-dom";
import { useRouterGo } from "@/hooks";
import styled from "styled-components";
import Form from "./../common/form";
import {
  addPatrolRecord,
  getMyTasksDetail,
  formGetOneData,
  formsaveAnswer,
} from "@/api";
import { Grid, Button, Toast } from "antd-mobile";
import { $fileType } from "@/tools";
import { PicBox, UploadBox2 } from "../common/picBox";
import MobilePreviewTool from "../common/mobilePreviewTool";
import { FormMobileTaskRender } from "@/pages/mobile/components";
const Root = styled.div`
  position: relative;
  min-height: 100vh;
  padding: 20px 16px 85px;
  background: #f2f2f6;
  .basicInfo {
    background: #fff;
    border-radius: 6px;
    .basicInfoTitle {
      font-size: 16px;
      color: #000;
      font-weight: 500;
      padding: 16px;
      border-bottom: 1px solid #e5e5e5;
    }
  }
  .formContainer {
    margin-top: 12px;
    background: #fff;
    border-radius: 6px;
    .formTitle {
      font-size: 16px;
      color: #000;
      font-weight: 500;
      padding: 0 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 52px;
      border-bottom: 1px solid #e5e5e5;
    }
  }

  .submit {
    box-shadow: 0px -4px 20px 0px rgba(0, 0, 0, 0.08);
    padding: 12px 16px;
    background: #fff;
    z-index: 10;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
  }
`;

function OffLine() {
  const [taskInfo, setTaskInfo] = useState({
    patrolObject: {
      classIsFill: 0,
      classIsShow: 0,
      gradeIsFill: 0,
      gradeIsShow: 0,
      personalIsFill: 0,
      personalIsShow: 0,
    },
  });
  const navigate = useRouterGo();
  const { id } = useParams();
  document.title = "线下巡课";
  useEffect(() => {
    document.title = "线下巡课";
    return () => {
      document.title = "在线巡课"; // 或者设置为默认标题
    };
  }, []);
  const formRef = useRef();
  const [attrs, setAttrs] = useState([]);

  const [temp, setTemp] = useState({
    show: false,
    data: {},
    formInfo: "",
  });

  useEffect(() => {
    getMyTasksDetail({ taskId: id }).then((res) => {
      if (res.code === 0) {
        setTaskInfo(res.data);
        if (res.data.useTable === 1) {
          formGetOneData({
            formId: res.data.formTemplateInfoId,
          }).then((result) => {
            if (!result || !result.data) return;
            const { formInfo } = result.data;
            setTemp({
              show: true,
              data: {},
              formInfo,
            });
          });
        }
      }
    });
  }, [id]);

  const uploadSuccess = (data) => {
    if ($fileType(data.fileUrl) === "gif") {
      setAttrs([...attrs, { coverUrl: data.fileUrl, fileUrl: data.fileUrl }]);
      return;
    }
    if ($fileType(data.fileUrl) === "mp4") {
      setAttrs([...attrs, { coverUrl: data.screenUrl, fileUrl: data.fileUrl }]);
    } else if ($fileType(data.fileUrl).toLowerCase() === "mov") {
      setAttrs([...attrs, { coverUrl: data.screenUrl, fileUrl: data.fileUrl }]);
    } else {
      setAttrs([...attrs, { coverUrl: data.smallPath, fileUrl: data.fileUrl }]);
    }
  };
  const handleDelete = (index) => {
    const newAttr = [...attrs];
    newAttr.splice(index, 1);
    setAttrs(newAttr);
  };
  // 预览
  const [prevInfo, setPreview] = useState({
    show: false,
    list: [],
    index: -1,
  });
  const handle2Preview = (index) => {
    const list = attrs.map((item) => item.fileUrl);
    setPreview({
      show: true,
      list,
      index,
    });
  };
  const handle2ClosePreview = () => {
    setPreview({
      show: false,
      list: [],
      index: -1,
    });
  };

  const [isSubmitting, setIsSubmitting] = useState(false);
  const handleSubmit = () => {
    if (isSubmitting) return; // 防止重复提交

    formRef.current.onCheck(async (data) => {
      if (!data) return;
      setIsSubmitting(true);

      /* 保存表单 */
      let result;
      if (hasFormInfo) {
        if (Object.keys(temp.data).length === 0) {
          setIsSubmitting(false);
          return Toast.show({
            icon: "fail",
            content: "请填写表单",
          });
        }
        result = await formsaveAnswer(taskInfo.formInfoId, temp.data);
        if (result.code !== 0) {
          setIsSubmitting(false);
          return Toast.show({
            icon: "fail",
            content: "表单保存失败",
          });
        }
      }

      /* 保存表单 */

      const newAttr = attrs.map((item) => ({
        attrPath: item.fileUrl,
        attrPic: item.coverUrl,
        attrType:
          $fileType(item.fileUrl) === "mp4" ||
            $fileType(item.fileUrl).toLowerCase() === "mov"
            ? 2
            : 1,
        patrolRecordId: 0,
      }));
      const params = {
        taskId: id,
        patrolComment: data.remarks,
        recordedUserName: data.userName,
        recordedUserId: data.userId,
        patrolRecordId: 0,
        patrolRecordAttrList: newAttr,
        patrolTime: data.time,

        useTable: taskInfo.useTable,
        formTemplateInfoId: taskInfo.formTemplateInfoId,
        formInfoName: taskInfo.formName,
        formInfoId: taskInfo.formInfoId,
        formTemplateAnswerId: result?.data,

        classId: data.classInfo.id,
        className: data.classInfo.label,
        gradeId: data.gradeInfo.id,
        gradeName: data.gradeInfo.label,
      };
      /* 判断是否可以提交 */
      const time = +new Date(data.time.replaceAll("-", "/"));
      const { startTime, endTime } = taskInfo;
      if (time > startTime && time < endTime) {
      } else {
        setIsSubmitting(false);
        Toast.show({
          icon: "fail",
          content: "请在规定的时间内提交",
        });
        return;
      }

      addPatrolRecord(params).then((res) => {
        if (res.code === 0) {
          Toast.show({
            icon: "success",
            content: "保存成功",
          });
          setTimeout(() => {
            navigate(-1);
          }, 1000);
        } else {
          setIsSubmitting(false);
        }
      }).catch(() => {
        setIsSubmitting(false);
      });
    });
  };

  const hasFormInfo = useMemo(() => {
    return taskInfo?.useTable === 1;
  }, [taskInfo]);

  return (
    <Root>
      <div className="basicInfo">
        <div className="basicInfoTitle">基本信息</div>
        <div className="p-4">
          <Form
            showTime
            patrolObject={taskInfo?.patrolObject}
            ref={(c) => (formRef.current = c)}
          />
          <div style={{ color: "#4D4D4D" }} className="text-base mb-4">
            上传
          </div>
          <Grid columns={3} gap={8}>
            <Grid.Item>
              <UploadBox2 onSuccess={uploadSuccess} />
            </Grid.Item>
            {attrs.map((item, index) => (
              <Grid.Item key={index}>
                <PicBox
                  onPreview={() => handle2Preview(index)}
                  onDelete={() => handleDelete(index)}
                  {...item}
                />
              </Grid.Item>
            ))}
          </Grid>
        </div>
      </div>

      {hasFormInfo && (
        <div className="formContainer">
          <div
            onClick={() =>
              setTemp((pre) => ({
                ...pre,
                show: !temp.show,
              }))
            }
            className="formTitle"
          >
            <span>{taskInfo.formInfoName}</span>
            <i
              style={{ color: "#808080" }}
              className={`iconfont iconxiajiantou inline-block transition-all transform ${temp.show ? "rotate-0" : "rotate-180"
                }`}
            ></i>
          </div>
          {temp.show && (
            <div className="p-4">
              <FormMobileTaskRender
                formInfo={temp.formInfo}
                data={temp.data}
                onChange={(value) => {
                  setTemp({
                    ...temp,
                    data: {
                      ...temp.data,
                      ...value,
                    },
                  });
                }}
              />
            </div>
          )}
        </div>
      )}

      <div className="submit">
        <Button
          onClick={isSubmitting ? null : handleSubmit}
          shape="rounded"
          block
          color="primary"
          size="large"
          style={{ opacity: isSubmitting ? 0.6 : 1, pointerEvents: isSubmitting ? 'none' : 'auto' }}
        >
          {isSubmitting ? "提交中..." : "提交"}
        </Button>
      </div>

      {prevInfo.show && (
        <MobilePreviewTool onClose={handle2ClosePreview} {...prevInfo} />
      )}
    </Root>
  );
}
export default OffLine;
