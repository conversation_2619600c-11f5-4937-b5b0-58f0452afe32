import React, { useState, useRef, useMemo, useEffect } from "react";
import { useParams } from "react-router-dom";
import styled from "styled-components";
import { CloseOutline } from "antd-mobile-icons";
import { Toast, Grid } from "antd-mobile";
import { $fileType } from "@/tools";
import { useCountDown } from "ahooks";
import { addPatrolRecord, formGetOneData, formsaveAnswer } from "@/api";
import { getScreenShot, startRecordLive, stopRecordLive } from "@/api/upload";
import { PicBox } from "../common/picBox";
import Form from "./../common/form";
import { FormMobileTaskRender } from "@/pages/mobile/components";

const DisRoot = styled.div`
  position: absolute;
  top: ${(props) => props.top + "px"};
  left: 0;
  bottom: 0;
  right: 0;
  background: #f2f2f6;
  z-index: 20;
  padding: 16px 16px 90px;
  overflow-y: auto;
  > .title {
    background: #fff;
    margin: -16px;
    padding: 16px;
  }
  .control {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    background: #fff;
    border-radius: 6px;
    margin: 12px 0;
    margin-top: 30px;
    padding: 0 20px;
    .left {
      transform: rotate(-90deg);
      display: inline-block;
      font-size: 24px;
      color: #909399;
    }
    .right {
      transform: rotate(90deg);
      display: inline-block;
      font-size: 24px;
      color: #909399;
    }
  }

  .basicInfo {
    background: #fff;
    border-radius: 6px;
    .basicInfoTitle {
      font-size: 16px;
      color: #000;
      font-weight: 500;
      padding: 16px;
      border-bottom: 1px solid #e5e5e5;
    }
  }

  .formContainer {
    margin-top: 12px;
    background: #fff;
    border-radius: 6px;
    .formTitle {
      font-size: 16px;
      color: #000;
      font-weight: 500;
      padding: 0 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 52px;
      border-bottom: 1px solid #e5e5e5;
    }
  }

  .btns {
    display: flex;
    align-items: center;
    .screenShot {
      display: flex;
      justify-content: center;
      align-items: center;
      flex: 1;
      height: 40px;
      border-radius: 48px;
      background: #f9fafb;
    }
    .tape {
      flex: 1;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      height: 40px;
      border-radius: 48px;
      background: #f9fafb;
      .progress {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        background: rgba(0, 122, 255, 0.08);
        transition: width 1s;
      }
      &.active {
        color: #007aff;
      }
    }
  }

  .submit {
    box-shadow: 0px -4px 20px 0px rgba(0, 0, 0, 0.08);
    padding: 12px 16px;
    background: #fff;
    z-index: 10;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    div {
      text-align: center;
      line-height: 48px;
      color: #fff;
      border-radius: 48px;
      height: 48px;
      background: linear-gradient(90deg, #4a86f6 0%, #4465f3 100%);
    }
  }

  /* .submit {
    margin-top: 20px;
    div {
      text-align: center;
      line-height: 48px;
      color: #fff;
      border-radius: 48px;
      height: 48px;
      background: linear-gradient(90deg, #4a86f6 0%, #4465f3 100%);
    }
  } */
`;

function Dis({
  top,
  activeInfo,
  taskInfo,
  playUrl,
  hideDis,
  nextChannel,
  prevChannel,
  setPreview,
}) {
  const taskRef = useRef();
  const formRef = useRef();
  const { id } = useParams();
  const [attrs, setAttrs] = useState([]);
  const handleScreenShot = () => {
    if (!playUrl) {
      return Toast.show({
        icon: "fail",
        content: "该通道没有播放地址",
      });
    }

    Toast.show({
      icon: "loading",
      content: "截图中...",
      duration: 0,
      maskClickable: false,
    });

    getScreenShot({ flvUrl: playUrl }).then((res) => {
      Toast.clear();
      if (res.code === 0 && res.data) {
        const fileUrl = res.data;
        const coverUrl = res.data.replace(".jpg", "_small.jpg");
        setAttrs([...attrs, { coverUrl, fileUrl }]);
      } else {
        return Toast.show({
          icon: "fail",
          content: "截图失败",
        });
      }
    });
  };
  const handleDelete = (index) => {
    const newAttr = [...attrs];
    newAttr.splice(index, 1);
    setAttrs(newAttr);
  };
  const handlePrevChannel = () => {
    if (activeTap) {
      return Toast.show({
        icon: "fail",
        content: "录制中不能切换",
      });
    } else {
      prevChannel();
    }
  };
  const handleNextChannel = () => {
    if (activeTap) {
      return Toast.show({
        icon: "fail",
        content: "录制中不能切换",
      });
    } else {
      nextChannel();
    }
  };
  // 预览
  const handle2Preview = (index) => {
    const list = attrs.map((item) => item.fileUrl);
    setPreview({
      show: true,
      list,
      index,
    });
  };
  /* 视频录制 */
  const [activeTap, setActiveTap] = useState(false);
  const [targetDate, setTargetDate] = useState();
  const [countdown] = useCountDown({
    targetDate,
    onEnd: () => {
      setActiveTap(false);
      handleEndTap(15);
    },
  });
  const handleStartTap = () => {
    if (activeTap && taskRef.current) {
      const time = Math.round((15000 - countdown) / 1000);
      if (time > 5) {
        setTargetDate(undefined);
        setActiveTap(false);
        handleEndTap(time);
      } else {
        Toast.show({
          icon: "fail",
          content: "录制时间太短",
        });
      }
      return;
    }
    if (!playUrl) {
      return Toast.show({
        icon: "fail",
        content: "该通道没有播放地址",
      });
    }

    startRecordLive({ flvUrl: playUrl }).then((res) => {
      if (res.code === 0 && res.data) {
        taskRef.current = res.data;
        setTargetDate(Date.now() + 15000);
        setActiveTap(true);
      } else {
        return Toast.show({
          icon: "fail",
          content: "录制失败",
        });
      }
    });
  };
  const handleEndTap = (sec) => {
    Toast.clear();
    Toast.show({
      icon: "loading",
      content: "生成中...",
      duration: 0,
      maskClickable: false,
    });
    const apis = [
      getScreenShot({ flvUrl: playUrl }),
      stopRecordLive({ processId: taskRef.current, sec }),
      // getRecordLive({ flvUrl: playUrl, sec }),
    ];
    Promise.all(apis)
      .then((data) => {
        if (!data[0].data || !data[1].data) {
          Toast.clear();
          Toast.show({
            icon: "success",
            content: "录制失败",
          });
          setTargetDate(undefined);
          setActiveTap(false);
          return;
        }
        const params = {
          fileUrl: data[1].data,
          coverUrl: data[0].data,
        };
        setAttrs((c) => [...c, params]);
        Toast.clear();
        Toast.show({
          icon: "success",
          content: "录制成功",
        });

        setTargetDate(undefined);
        setActiveTap(false);
      })
      .catch((error) => {
        Toast.clear();
        Toast.show({
          icon: "success",
          content: "录制失败",
        });
        setTargetDate(undefined);
        setActiveTap(false);
      });
  };

  // 表单
  const [temp, setTemp] = useState({
    show: false,
    data: {},
    formInfo: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const submit = () => {
    if (isSubmitting) return; // 防止重复提交

    formRef.current.onCheck(async (data) => {
      if (!data) return;
      
      setIsSubmitting(true);
      
      const newAttr = attrs.map((item) => ({
        attrPath: item.fileUrl,
        attrPic: item.coverUrl,
        attrType: $fileType(item.fileUrl) === "mp4" ? 2 : 1,
        patrolRecordId: 0,
      }));
      /* 保存表单 */
      let result;
      if (hasFormInfo) {
        if (Object.keys(temp.data).length === 0) {
          setIsSubmitting(false);
          return Toast.show({
            icon: "fail",
            content: "请填写表单",
          });
        }
        result = await formsaveAnswer(taskInfo.formInfoId, temp.data);
        if (result.code !== 0) {
          setIsSubmitting(false);
          return Toast.show({
            icon: "fail",
            content: "表单保存失败",
          });
        }
      }

      /* 保存表单 */
      const params = {
        taskId: id,
        patrolComment: data.remarks,
        recordedUserName: data.userName,
        recordedUserId: data.userId,
        patrolRecordId: 0,
        patrolRecordAttrList: newAttr,
        patrolTime: data.time,

        useTable: taskInfo.useTable,
        formTemplateInfoId: taskInfo.formTemplateInfoId,
        formInfoName: taskInfo.formName,
        formInfoId: taskInfo.formInfoId,
        formTemplateAnswerId: result?.data,

        classId: data.classInfo.id,
        className: data.classInfo.label,
        gradeId: data.gradeInfo.id,
        gradeName: data.gradeInfo.label,
      };

      addPatrolRecord(params).then((res) => {
        if (res.code === 0) {
          Toast.show({
            icon: "success",
            content: "保存成功",
          });
          setTimeout(() => {
            hideDis();
          }, 1000);
        } else {
          setIsSubmitting(false);
        }
      }).catch(() => {
        setIsSubmitting(false);
      });
    });
  };

  const hasFormInfo = useMemo(() => {
    return taskInfo?.useTable === 1;
  }, [taskInfo]);

  useEffect(() => {
    if (taskInfo?.useTable !== 1) return;
    formGetOneData({
      formId: taskInfo.formTemplateInfoId,
    }).then((result) => {
      if (!result || !result.data) return;
      const { formInfo } = result.data;
      setTemp({
        show: true,
        data: {},
        formInfo,
      });
    });
  }, [taskInfo]);
  return (
    <DisRoot top={top}>
      <div className="title flex items-center justify-between text-base">
        <div className="font-bold">巡视评价</div>
        <CloseOutline onClick={() => hideDis()} />
      </div>

      <div className="control text-base">
        <i
          onClick={() => handlePrevChannel()}
          style={{ color: "#B3B3B3" }}
          className="iconfont iconjiantou inline-block transform rotate-180 text-3xl"
        ></i>
        <div className="font-bold">{activeInfo.name}</div>
        <i
          onClick={() => handleNextChannel()}
          style={{ color: "#B3B3B3" }}
          className="iconfont iconjiantou text-3xl"
        ></i>
      </div>

      <div className="basicInfo">
        <div className="basicInfoTitle">基本信息</div>
        <div className="p-4">
          <Form
            classRoomId={activeInfo.monitoringNodeId}
            type={activeInfo.type}
            patrolObject={taskInfo.patrolObject}
            ref={(c) => (formRef.current = c)}
          />
          <div className="btns">
            <div onClick={handleScreenShot} className="mr-4 screenShot">
              <i className="iconfont iconxiangji mr-1"></i>
              <span>截图</span>
            </div>

            <div
              className={`tape ml-4  ${activeTap ? "active" : ""}`}
              onClick={handleStartTap}
            >
              {activeTap && (
                <div
                  style={{
                    width:
                      countdown === 0
                        ? "0px"
                        : ((15000 - countdown) / 15000) * 100 + "%",
                  }}
                  className="progress"
                ></div>
              )}
              <i className="iconfont iconshipin2 mr-1"></i>
              <span>
                录像
                {activeTap && (
                  <span> {Math.round((15000 - countdown) / 1000)}s</span>
                )}
              </span>
            </div>

            {/* {isShowBtn && (
              <div
                onClick={handleFormBtn}
                className="screenShot"
              >
                {formAnswerId ? (
                  <i
                    className="iconfont iconzhengque1 mr-1"
                    style={{ color: "#25C274" }}
                  ></i>
                ) : (
                  <i className="iconfont icon-docment2 mr-1"></i>
                )}
                <span>量表</span>
              </div>
            )} */}
          </div>
          <Grid className="mt-5" columns={3} gap={8}>
            {attrs.map((item, index) => (
              <Grid.Item key={index}>
                <PicBox
                  onPreview={() => handle2Preview(index)}
                  onDelete={() => handleDelete(index)}
                  {...item}
                />
              </Grid.Item>
            ))}
          </Grid>
        </div>
      </div>

      {hasFormInfo && (
        <div className="formContainer">
          <div
            onClick={() =>
              setTemp((pre) => ({
                ...pre,
                show: !temp.show,
              }))
            }
            className="formTitle"
          >
            <span>{taskInfo.formInfoName}</span>
            <i
              style={{ color: "#808080" }}
              className={`iconfont iconxiajiantou inline-block transition-all transform ${
                temp.show ? "rotate-0" : "rotate-180"
              }`}
            ></i>
          </div>
          {temp.show && (
            <div className="p-4">
              <FormMobileTaskRender
                formInfo={temp.formInfo}
                data={temp.data}
                onChange={(value) => {
                  setTemp({
                    ...temp,
                    data: {
                      ...temp.data,
                      ...value,
                    },
                  });
                }}
              />
            </div>
          )}
        </div>
      )}

      <div className="submit">
        <div
          onClick={isSubmitting ? null : submit}
          style={{ opacity: isSubmitting ? 0.6 : 1, pointerEvents: isSubmitting ? 'none' : 'auto' }}
        >
          {isSubmitting ? "提交中..." : "提交"}
        </div>
      </div>
    </DisRoot>
  );
}

export default Dis;
