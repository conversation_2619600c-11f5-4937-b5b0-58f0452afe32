import React, { useEffect, useState, useCallback } from "react";
import { useParams } from "react-router-dom";
import Hls from "hls.js";
import { $getIsIOS, tryLink, tryPcLink, getUrlQuery } from "@/tools";
import { useWatermark } from "@/hooks";
import { YsEmpt } from "@/components";
import {
  getMyTasksDetail,
  getMobilePlayUrl,
  getMagicLive,
  getPatrolLiveByFree,
  getRoomDeviceById,
  getSetting
} from "@/api";
import { Toast } from "antd-mobile";
import { CheckCircleFill, ExclamationCircleOutline } from "antd-mobile-icons";
import {
  PlaceSideBar,
  PenBar,
  ControlBtn,
  ControlPanel,
  TalkBar,
} from "./../components";
import moment from "moment";
import styled from "styled-components";
import Dis from "./dis";
import Talk from "./talk";
import MobilePreviewTool from "./../common/mobilePreviewTool";
const Root = styled.div`
  position: relative;
  min-height: 100vh;
  .videoContainer {
    position: relative;
    font-size: 0;
    background: #000;
    height: 0;
    padding-top: 56.25%;
    video::-webkit-media-controls-fullscreen-button {
      display: none !important;
    }
    
    video::-moz-media-controls-fullscreen-button {
      display: none !important;
    }
    .document-container {
      position: absolute !important;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
    }
    .tips {
      z-index: 999;
      color: #fff;
      font-size: 14px;
      left: 10px;
      top: 10px;
      width: 70px;
      .tip-cur {
        height: 32px;
        background: rgba(0, 0, 0, 0.45);
        border-radius: 4px;
      }
      .tip-list {
        margin-top: 4px;
        background: rgba(0, 0, 0, 0.65);
        border-radius: 4px;
      }
      .tip-item {
        text-align: center;
        line-height: 40px;
        height: 40px;
      }
    }
    .cover {
      font-size: 16px;
      background: #303133;
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      right: 0;
      z-index: 999;
      color: #fff;
      span {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
    video {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  .tabs {
    height: 60px;
    background: #fff;
    border-bottom: 1px solid #e4e7ed;
    span {
      position: relative;
      line-height: 60px;
      font-size: 16px;
      &::after {
        content: "";
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: -8px;
        width: 20px;
        height: 3px;
        background: transparent;
      }
    }
    span.active {
      color: #4465f3;
      &::after {
        background: #4465f3;
      }
    }
  }
`;
function Online() {
  const { id } = useParams();
  document.title = "视频巡课";
  useEffect(() => {
    document.title = "视频巡课";
    return () => {
      document.title = "在线巡课"; // 或者设置为默认标题
    };
  }, []);
  const [active, setActive] = useState(1);
  const [top, setTop] = useState(0);
  useEffect(() => {
    const dom = document.getElementById("videoContainer");
    setTop(dom.clientHeight);
  }, []);
  const [taskInfo, setTaskInfo] = useState(null);
  const [allTypes, setAllTypes] = useState([]);
  const [type1, setType1] = useState([]);
  const [type2, setType2] = useState([]);
  // 纯教室的数据
  const [classRoomPlace, setClassRoomPlace] = useState([]);

  // 场所
  // 教室+建筑的数据
  const [classPlace, setClassPlace] = useState([]);
  const [classPlaceNum, setClassPlaceNum] = useState(0);
  const [showTalk, setShowTalk] = useState(false);
  const [activeInfo, setActiveIdfo] = useState({
    belongs: "",
    monitoringNodeId: "",
    name: "",
  });
  const [showUrlList, setUrlList] = useState(false);
  const [urls, setUrls] = useState([]);
  const [playUrl, setPlayUrl] = useState("");

  const urlIndex = urls.findIndex((item) => item.phoneUrl === playUrl);

  const [showDis, setShowDis] = useState(false);
  const [isPlay, setIsPlay] = useState(false);
  const [showControl, setShowControl] = useState(false);

  useEffect(() => {
    let timer;
    getMyTasksDetail({ taskId: id }).then((res) => {
      if (res.code === 0) {
        setTaskInfo(res.data);
        const type1 = [];
        res.data.resultMap.type1.forEach((item) => {
          if (item.nodeType && Number(item.nodeType)) {
            type1.push(item);
          }
        });
        setType1(type1);
        const thirdEquip = res.data.resultMap.type3.map((item) => ({
          ...item,
          monitoringNodeId: item.monitoringRecordId,
          belongs: 30000,
          online: item.online === 1 ? 1 : 0,
        }));
        setType2([...res.data.resultMap.type2, ...thirdEquip]);
        // setType2(res.data.resultMap.type2);
        setAllTypes([...type1, ...res.data.resultMap.type2]);
        // 教室
        if (
          res.data.resultMap.type4 &&
          Array.isArray(res.data.resultMap.type4)
        ) {
          let placeNum = 0;
          const classRoomType = [];
          const placeList = res.data.resultMap.type4
            .map((item) => {
              if (item.nodeType === "ROOM") {
                placeNum++;
                classRoomType.push(item);
              }
              return {
                ...item,
                monitoringNodeId: item.id,
                key: item.id,
                title: item.value,
                parentId: item.parentId,
              };
            })
            .filter((node) => node.nodeType !== "UNIT");
          setClassPlace(placeList);
          setClassPlaceNum(placeNum);
          setClassRoomPlace(classRoomType);
        }
        // 教室
        /* 开启时间限制 */
        if (res.data.limitType === 2) {
          if (!checkTime(res.data)) {
            Toast.show({
              icon: <ExclamationCircleOutline />,
              content: "该时段不允许查看视频监控",
            });
          }
          timer = setInterval(() => {
            if (!checkTime(res.data)) {
              setActiveIdfo({
                belongs: "",
                monitoringNodeId: "",
                name: "",
              });
              setShowDis(false);
            }
          }, 5 * 1000);
        }
      }
    });

    return () => {
      timer && clearInterval(timer);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  useEffect(() => {
    if (!playUrl) return;
    let hls;
    const videoEle = document.getElementById("videoPlayer");
    if (Hls.isSupported()) {
      tryLink(playUrl).then((data) => {
        hls = new Hls();
        hls.loadSource(playUrl);
        hls.attachMedia(videoEle);
        hls.on(Hls.Events.MANIFEST_PARSED, function () {
          videoEle.play();
          setIsPlay(true);
        });
        videoEle.addEventListener("timeupdate", () => {
          if (videoEle.duration - videoEle.currentTime > 30) {
            videoEle.currentTime = videoEle.duration - 10;
          }
        });
      });
    }
    // if (videoEle.canPlayType("application/vnd.apple.mpegurl") && isIOS) {
    //   tryLink(playUrl).then((data) => {
    //     videoEle.src = playUrl;
    //     videoEle.addEventListener("loadedmetadata", () => {
    //       videoEle.play();
    //       setIsPlay(true);
    //     });
    //   });
    // } else if (Hls.isSupported()) {
    //   tryPcLink(playUrl).then((data) => {
    //     hls = new Hls();
    //     hls.loadSource(playUrl);
    //     hls.attachMedia(videoEle);
    //     videoEle.addEventListener("loadedmetadata", () => {
    //       videoEle.play();
    //       setIsPlay(true);
    //     });
    //   });
    // }
    return () => {
      if (hls) hls.destroy();
      hls = null;
    };
  }, [playUrl]);

  const onChoosePlaceChannel = async ({ id, value }) => {
    if (!checkTime(taskInfo)) {
      return Toast.show({
        icon: <ExclamationCircleOutline />,
        content: "该时段不允许查看",
      });
    }
    setActiveIdfo({
      monitoringNodeId: id,
      belongs: null,
      name: value,
      type: 3,
    });

    const result = await getRoomDeviceById({
      orgId: getUrlQuery("bureauId"),
      roomId: id,
    });
    if (result.code === 0) {
      let deviceList = result.data.deviceList || [];
      deviceList = deviceList.filter((item) => item.belongs === 20000 || item.belongs === 10000);
      const magicDeviceList = result.data.magicDeviceList || [];
      if (deviceList.length > 0) {
        const { belongs, monitoringNodeId } = deviceList[0];
        getPatrolLiveByFree({
          belong: belongs,
          nodeId: monitoringNodeId,
          bureauId: getUrlQuery("bureauId"),
        }).then((result) => {
          if (result && result.code === 0) {
            const urlList = result.data.map((item) => item.phoneUrl);
            setUrls(() => {
              const list = urlList.map((item) => ({
                phoneUrl: item,
              }));
              return list;
            });
            setIsPlay(false);
            setPlayUrl(urlList[0]);
          }
        });
        return;
      }
      if (magicDeviceList.length > 0) {
        const result = await getMagicLive({ roomId: id });
        if (result && result.code === 0 && result.data) {
          let urlList = result.data.liveUrls || [];
          if (result.data.success === 500) {
            urlList = urlList.filter((item) => item.status === 1);
          }
          urlList = urlList.map((item) => item.m3u8Url);

          setUrls(() => {
            const list = urlList.map((item) => ({
              phoneUrl: item,
            }));
            return list;
          });
          setIsPlay(false);
          setPlayUrl(urlList[0]);
        }
        return;
      }
    }

    // const result = await getMagicLive({ roomId: id });
    // if (result && result.code === 0 && result.data) {
    //   let urlList = result.data.liveUrls || [];
    //   if (result.data.success === 500) {
    //     urlList = urlList.filter((item) => item.status === 1);
    //   }
    //   urlList = urlList.map((item) => item.m3u8Url);

    //   setUrls(() => {
    //     const list = urlList.map((item) => ({
    //       phoneUrl: item,
    //     }));
    //     return list;
    //   });
    //   setPlayUrl("");
    //   setIsPlay(false);

    //   setPlayUrl(urlList[0]);
    // }
  };

  const onChoosedChannel = ({ monitoringNodeId, belongs, name, urls }) => {
    if (id && !checkTime(taskInfo)) {
      return Toast.show({
        icon: <ExclamationCircleOutline />,
        content: "该时段不允许查看",
      });
    }
    // const isIOS = $getIsIOS()
    setActiveIdfo({
      monitoringNodeId,
      belongs,
      name,
      type: active === 2 ? 2 : 1,
    });
    setPlayUrl("");
    setIsPlay(false);
    if (urls) {
      setPlayUrl(urls[0].phoneUrl);
      return;
    }
    getMobilePlayUrl({
      nodeId: monitoringNodeId,
      belong: belongs,
      type: 3,
    })
      .then((res) => {
        if (res.code === 0) {
          let url = res.data[0].phoneUrl;
          if (url) {
            setPlayUrl(url);
          }
        } else {
          setPlayUrl("");
        }
      })
      .catch((error) => {
        setPlayUrl("");
      });
  };
  /* 时间控制 */

  const checkTime = useCallback((taskInfo) => {
    if (!taskInfo) return;
    if (taskInfo.limitType === 1) {
      return true;
    }

    let startTime = taskInfo.startTime;
    let endTime = taskInfo.endTime;
    let customs = taskInfo.patrolTaskCustomTimes;

    let flag = true;
    // 具体日期
    if (startTime < +new Date() && +new Date() < endTime) {
      let day = moment(+new Date()).format("YYYY-MM-DD");
      flag = customs.some((item) => {
        let start = day + ` ${item.startTime}:00`;
        let end = day + ` ${item.endTime}:00`;
        if ($getIsIOS()) {
          start = start.replaceAll("-", "/");
          end = end.replaceAll("-", "/");
        }
        return +new Date(start) < +new Date() && +new Date(end) > +new Date();
      });
    } else {
      flag = false;
    }
    return flag;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /* 时间控制 */
  /* ============评价=========== */
  const nextChannel = () => {
    if (active === 4) {
      const findIndex = classRoomPlace.findIndex(
        (item) => item.id === activeInfo.monitoringNodeId
      );

      if (findIndex + 1 === classRoomPlace.length) {
        return Toast.show("已经是最后一个了");
      } else {
        onChoosePlaceChannel(classRoomPlace[findIndex + 1]);
      }
      return;
    }
    const findIndex = allTypes.findIndex(
      (item) => item.monitoringNodeId === activeInfo.monitoringNodeId
    );
    if (findIndex !== -1) {
      if (findIndex + 1 < allTypes.length) {
        onChoosedChannel(allTypes[findIndex + 1]);
      } else {
        onChoosedChannel(allTypes[0]);
      }
    } else {
      onChoosedChannel(allTypes[0]);
    }
  };
  const prevChannel = () => {
    if (active === 4) {
      const findIndex = classRoomPlace.findIndex(
        (item) => item.id === activeInfo.monitoringNodeId
      );
      if (findIndex !== 0) {
        onChoosePlaceChannel(classRoomPlace[findIndex - 1]);
        return;
      }
      return Toast.show("已经是第一个了");
    }
    const findIndex = allTypes.findIndex(
      (item) => item.monitoringNodeId === activeInfo.monitoringNodeId
    );
    if (findIndex !== -1) {
      if (findIndex !== 0) {
        onChoosedChannel(allTypes[findIndex - 1]);
      } else {
        onChoosedChannel(allTypes[allTypes.length - 1]);
      }
    } else {
      onChoosedChannel(allTypes[0]);
    }
  };
  const maxContentHeight = 60 + top + "px";
  /* 预览 */
  const [prevInfo, setPreview] = useState({
    show: false,
    list: [],
    index: -1,
  });
  const handle2ClosePreview = () => {
    setPreview({
      show: false,
      list: [],
      index: -1,
    });
  };

  const [openWaterMark, setOpenWaterMark] = useState(0)
  useEffect(() => {
    getSetting().then((res) => {
      if (res.code === 0) {
        setOpenWaterMark(res.data.patrolSystemSetting.openWaterMark);
      }
    });
  }, []);
  const watermarkRef = useWatermark(openWaterMark == 1);

  return (
    <Root style={{ background: active === 1 ? "#fff" : "#f4f6f9" }}>
      <div id="videoContainer" className="videoContainer" hideFullscreenButton={openWaterMark === 1}>
        {!activeInfo.monitoringNodeId && (
          <div className="absolute cover">
            <span>请选择通道</span>
          </div>
        )}

        {!isPlay && activeInfo.monitoringNodeId && (
          <div className="absolute cover">
            <span>正在获取画面...</span>
          </div>
        )}

        {isPlay && urls.length > 1 && (
          <div className="absolute tips">
            <div
              onClick={() => setUrlList(!showUrlList)}
              className="tip-cur flex items-center justify-center"
            >
              <span>画面{urlIndex + 1}</span>
              <i className="iconfont iconqiehuan ml-1"></i>
            </div>
            {showUrlList && (
              <div className="tip-list">
                {urls.map((item, index) => (
                  <div
                    onClick={() => {
                      setPlayUrl(item.phoneUrl);
                      setUrlList(false);
                    }}
                    key={index}
                    className="tip-item"
                  >
                    画面{index + 1}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        <video
          id="videoPlayer"
          playsInline
          muted
          webkitplaysinline="true"
          x5-playsinline="true"
          controls
          x5-video-player-type="h5-page"
        />
        {/* 水印 */}
        <div ref={watermarkRef} className="document-container"></div>

      </div>
      {/* 预览 */}
      {prevInfo.show && (
        <MobilePreviewTool onClose={handle2ClosePreview} {...prevInfo} />
      )}
      {/* 预览 */}
      <div className="tabs flex">
        <div
          onClick={() => setActive(1)}
          className="flex-1 text-center items-center"
        >
          <span className={active === 1 ? "active" : ""}>任务详情</span>
        </div>
        {classPlaceNum > 0 && (
          <div
            onClick={() => setActive(4)}
            className="flex-1 text-center items-center"
          >
            <span className={active === 4 ? "active" : ""}>
              教室 {classPlaceNum}
            </span>
          </div>
        )}
        {type1.length > 0 && (
          <div
            onClick={() => setActive(2)}
            className="flex-1 text-center items-center"
          >
            <span className={active === 2 ? "active" : ""}>
              监控 {type1.length}
            </span>
          </div>
        )}
        {type2.length > 0 && (
          <div
            onClick={() => setActive(3)}
            className="flex-1 text-center items-center"
          >
            <span className={active === 3 ? "active" : ""}>
              录播设备 {type2.length}
            </span>
          </div>
        )}
      </div>
      <div
        className="overflow-y-auto"
        style={{ height: `calc(100vh - ${maxContentHeight})` }}
      >
        {active === 1 && <TaskDetail taskInfo={taskInfo} />}
        {/* 通道 */}
        {active === 2 && (
          <MonitorList
            onChoose={onChoosedChannel}
            activeInfo={activeInfo}
            type1={type1}
          />
        )}
        {active === 3 && (
          <EquipList
            onChoose={onChoosedChannel}
            activeInfo={activeInfo}
            type2={type2}
          />
        )}
        {active === 4 && (
          <PlaceSideBar
            isFree={2}
            classPlace={classPlace}
            activeInfo={activeInfo}
            onChoose={onChoosePlaceChannel}
          />
        )}
      </div>
      <PenBar
        onClick={() => {
          if (!activeInfo.monitoringNodeId) {
            return Toast.show({
              icon: "fail",
              content: "请选择通道",
            });
          }
          setShowDis(true);
        }}
      />
      <TalkBar
        onClick={() => {
          if (!activeInfo.monitoringNodeId) {
            return Toast.show({
              icon: "fail",
              content: "请选择通道",
            });
          }
          setShowTalk(true);
        }}
      />
      {showTalk && (
        <Talk
          top={top}
          activeId={activeInfo.monitoringNodeId}
          hideTalk={() => {
            setShowTalk(false);
          }}
        ></Talk>
      )}
      {showDis && (
        <Dis
          top={top}
          activeInfo={activeInfo}
          taskInfo={taskInfo}
          playUrl={playUrl}
          hideDis={() => {
            setShowDis(false);
          }}
          nextChannel={nextChannel}
          prevChannel={prevChannel}
          setPreview={setPreview}
        />
      )}
      {taskInfo?.allowControl === 1 && (
        <ControlBtn
          onClick={() => {
            setShowControl(true);
          }}
        />
      )}

      {showControl && (
        <ControlPanel
          top={top}
          activeInfo={activeInfo}
          onHide={() => {
            setShowControl(false);
          }}
        />
      )}
    </Root>
  );
}

const TaskRoot = styled.div`
  .title {
    font-size: 18px;
    color: #303133;
    font-weight: 500;
  }
  .time {
    margin-top: 12px;
    padding: 0 12px;
    line-height: 45px;
    background: #f9fafb;
    border-radius: 6px;
  }
  .remarks {
    margin-top: 20px;
    color: #c0c4cc;
  }
`;
function TaskDetail({ taskInfo }) {
  if (!taskInfo) return null;
  return (
    <TaskRoot className="p-5">
      <div className="title truncate">{taskInfo.taskName}</div>
      <div className="text-base text-gray-400 mt-2">
        <span>{moment(taskInfo.startTime).format("YYYY-MM-DD")}</span>
        <span className="mx-2">至</span>
        <span>{moment(taskInfo.endTime).format("YYYY-MM-DD")}</span>
      </div>
      <div style={{ color: "#303133" }} className="text-base mt-5">
        允许查看视频监控时间段
      </div>
      {taskInfo.limitType === 1 && <div className="time">无限制</div>}
      {taskInfo.limitType === 2 &&
        taskInfo.patrolTaskCustomTimes.map((item, index) => (
          <div key={index} className="time">
            {item.startTime} - {item.endTime}
          </div>
        ))}
      <div className="remarks break-all">{taskInfo.remarks}</div>
    </TaskRoot>
  );
}

const EquipRoot = styled.div`
  .item {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    border-radius: 6px;
    background: #fff;
    padding: 0 16px;
    border: 2px solid transparent;
    font-size: 16px;
  }
  .item .label {
    position: relative;
    padding-left: 16px;
    &::before {
      content: "";
      width: 10px;
      height: 10px;
      border-radius: 50%;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
    &.online::before {
      background: #41cd85;
    }
    &.unline::before {
      background: #ccc;
    }
  }
  .item.active {
    color: #4465f3;
    border-color: #4465f3;
    background: rgba(68, 101, 243, 0.07);
  }
`;
function EquipList({ activeInfo, type2, onChoose }) {
  return (
    <EquipRoot className="p-5">
      {type2.map((item) => (
        <div
          onClick={() => onChoose(item)}
          key={item.monitoringNodeId}
          className={`item ${item.monitoringNodeId === activeInfo.monitoringNodeId
            ? "active"
            : ""
            }`}
        >
          <div
            className={`label flex-1 truncate ${item.online === 0 ? "unline" : "online"
              }`}
          >
            {item.name}
          </div>
          {item.monitoringNodeId === activeInfo.monitoringNodeId && (
            <CheckCircleFill style={{ color: "#4465F3" }} />
          )}
        </div>
      ))}
      {type2.length === 0 && <YsEmpt />}
    </EquipRoot>
  );
}

const MonitorRoot = styled.div`
  .item {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    border-radius: 6px;
    background: #fff;
    padding: 0 16px;
    border: 2px solid transparent;
    font-size: 16px;
  }
  .item.active {
    color: #4465f3;
    border-color: #4465f3;
    background: rgba(68, 101, 243, 0.07);
  }
`;
function MonitorList({ type1, activeInfo, onChoose }) {
  return (
    <MonitorRoot className="p-5">
      {type1.map((item) => (
        <div
          onClick={() => onChoose(item)}
          key={item.monitoringNodeId}
          className={`item ${item.monitoringNodeId === activeInfo.monitoringNodeId
            ? "active"
            : ""
            }`}
        >
          <div className="label flex-1 truncate">{item.name}</div>
          {item.monitoringNodeId === activeInfo.monitoringNodeId && (
            <CheckCircleFill style={{ color: "#4465F3" }} />
          )}
        </div>
      ))}
      {type1.length === 0 && <YsEmpt />}
    </MonitorRoot>
  );
}

export default Online;
