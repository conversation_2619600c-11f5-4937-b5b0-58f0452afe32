export { $isCdn } from "./isCdn";
export { $upload } from "./upload";
export { $message } from "./message";
export { $showLoading, $hideLoading } from "./loading";
export { webrtcPlayer } from "./webrtcPlayer"
export { default as mqtt } from "./mq";
export function $hidePhone(phone) {
  if (phone && phone.length === 11) {
    return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
  }
  return phone;
}
export function $getIsIOS() {
  return /iphone|ipad|ipod/.test(navigator.userAgent.toLowerCase());
}
export function $getIsAndroid() {
  const u = navigator.userAgent;
  const isAndroid = u.indexOf("Android") > -1 || u.indexOf("Adr") > -1;
  return isAndroid;
}

export function $getTree(list, pId) {
  let re = [];
  for (let i = 0; i < list.length; i++) {
    let parentId = list[i].parentId;
    if (parentId === pId) {
      let child = $getTree(list, list[i].id);
      if (child.length > 0) {
        list[i].children = child;
      }
      re.push(list[i]);
    }
  }
  return re;
}
export function data2Tree(data, pid) {
  if (!Array.isArray(data)) {
    throw Error("data2Tree function need array parmas");
  }
  const _data = JSON.parse(JSON.stringify(data));
  return _data.filter((p) => {
    const _childrens = _data.filter((c) => c.pid === p.id);
    _childrens.length && (p.children = _childrens);
    return p.pid === pid;
  });
}
// export function listToTree(list, parentId = null) {
//   const tree = []

//   list.forEach(item => {
//     if (item.parentId === parentId) {
//       const children = listToTree(list, item.id)
//       if (children.length > 0) {
//         item.children = children
//       }
//       tree.push(item)
//     }
//   })

//   return tree
// }
export function $fileType(path) {
  const array = path.split(".");
  if (array.length === 0) {
    return "";
  }
  return array[array.length - 1];
}
export function getUrlQuery(key) {
  const url = window.location.href;
  if (url.indexOf("?") !== -1) {
    const baseStr = url.split("?")[1];
    const reArr = baseStr.split("&");
    const map = {};
    reArr.forEach((item) => {
      const [key, value] = item.split("=");
      map[key] = value;
    });
    return map[key];
  }
  return "";
}

export function getCookie(key) {
  const cookies = document.cookie;
  const cookieList = cookies.split(";");
  for (let i = 0; i < cookieList.length; i++) {
    const arr = cookieList[i].split("=");
    if (key === arr[0].trim()) {
      return arr[1];
    }
  }
  return "";
}
export function getTokenFromCookie() {
  const cookieToken = getCookie("accessToken");
  if (cookieToken) {
    return "Bearer " + cookieToken;
  }
  return "";
}

export function tryLink(url) {
  let maxCount = 5;
  return new Promise((resolve, reject) => {
    const fecther = () => {
      maxCount--;
      if (maxCount < 0) {
        resolve(url);
        return;
      }
      fetch(url).then((data) => {
        setTimeout(() => {
          fecther();
        }, 1000);
      });
    };
    fecther();
  });
}
export function tryPcLink(url) {
  let maxCount = 10;
  return new Promise((resolve, reject) => {
    const fecther = () => {
      maxCount--;
      if (maxCount < 0) {
        resolve(url);
        return;
      }
      fetch(url).then((data) => {
        if (data && data.status === 200) {
          resolve(url);
        } else {
          setTimeout(() => {
            fecther();
          }, 1000);
        }
      });
    };
    fecther();
  });
}

export function inIframe() {
  return window.self !== window.top;
}

export function findItemRecursive(tree, itemId, path = []) {
  for (const item of tree) {
    if (item.id === itemId) {
      // 找到目标项
      return [...path, item];
    }

    if (item.children && item.children.length > 0) {
      const result = findItemRecursive(item.children, itemId, [...path, item]);
      if (result) {
        // 子树中找到目标项
        return result;
      }
    }
  }

  // 未找到目标项
  return null;
}
