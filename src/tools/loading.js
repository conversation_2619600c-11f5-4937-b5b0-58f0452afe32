import ReactDOM from "react-dom"
import { Spin } from "antd"
import { LoadingOutlined } from "@ant-design/icons"
const antIcon = <LoadingOutlined style={{ fontSize: 50 }} spin />
export const $showLoading = (tip = "loading...") => {
  const dom = document.createElement("div")
  dom.setAttribute("id", "loading")
  document.body.appendChild(dom)
  ReactDOM.render(<Spin indicator={antIcon} tip={tip} size="large" />, dom)
}
export const $hideLoading = () => {
  document.body.removeChild(document.getElementById("loading"))
}
