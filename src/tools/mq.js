import mqtt_module from "mqtt";

const mqtt_sdk = class Mqtt {
    constructor(options) {
        console.log("options==>", options);

        this.connected = false;
        this.clientId = options.clientId;
        this.handlerFunc = [];
        this.reconnectNum = 0;
        if (options.theme && options.theme.length) {
            options.theme.forEach((item, index) => {
                if (item.type === "ys/web") {
                    options.theme.splice(index, 1);
                }
            });
            options.theme = options.theme.concat({
                type: "ys/web",
            });
        } else {
            options.theme = [
                {
                    type: "ys/web",
                },
            ];
        }
        this.options = options;
        this.mqtt = mqtt_module;
        this.client = this.mqtt.connect(this.options.url, {
            connectTimeout: this.options.connectTimeout || 4000,
            clientId: this.options.clientId,
            username: this.options.username || "web_user",
            password: this.options.token,
            clean: this.options.clean || true,
            reconnect: this.options.reconnect || true,
            keepalive: this.options.keepalive || 30,
        });
        this.client.on("packetsend", (data) => {
            console.log("packetsend心跳", data);
        });
        this.client.on("connect", () => {
            this.options.theme.forEach((item) => {
                let type = `${item.type}/${this.options.clientId}`;
                console.log("订阅主题", type);
                this.client.subscribe(type, { qos: 2 }, (error) => {
                    if (!error) {
                        this.connected = true;
                        this.trigger(
                            "connect",
                            Object.assign(item, {
                                clientId: this.options.clientId,
                            })
                        );
                    } else {
                        this.connected = false;
                        this.trigger("error", {
                            msg: error,
                        });
                    }
                });
            });
            this.client.on("message", (topic, message) => {
                let data = eval("(" + message.toString() + ")");
                this.trigger(
                    "message",
                    Object.assign(data, {
                        topic,
                    })
                );
            });
            this.client.on("error", (error) => {
                this.trigger("error", error);
            });
            this.client.on("reconnect", () => {
                this.reconnectNum++;
                this.trigger("reconnecting", this.reconnectNum);
            });
        });
    }

    //发送消息方法
    /**
     * topic  消息主题
     * message
     * qos  消息质量等级，默认为0，可选值为0、1或2。
     * */
    sendMessage(topic, message, qos = 0) {
        try {
            this.client.publish(
                topic,
                JSON.stringify(message),
                { qos: qos },
                (err) => {
                    // 如果err存在，即消息发送失败
                    if (err) {
                        console.log("发送消息失败", err);
                        this.trigger("error", {
                            topic: topic,
                            message: message,
                        });
                    } else {
                        console.log("发送消息成功:", topic, message);
                    }
                }
            );
        } catch (error) {
            this.trigger("error", {
                msg: "发送消息异常: " + error.toString(),
                topic: topic,
                message: message,
            });
        }
    }

    on(eName, callback) {
        if (!this.handlerFunc[eName]) {
            this.handlerFunc[eName] = [];
        }
        this.handlerFunc[eName].push(callback);
    }

    trigger(eName, params) {
        if (this.handlerFunc[eName]) {
            this.handlerFunc[eName].forEach((func) => {
                func.call(this, params);
            });
        }
    }

    closeMqtt(callback) {
        this.client.end(true, [], () => {
            if (callback) {
                callback();
            }
        });
    }

    reconnect() {
        if (this.connected) {
            return;
        }
        this.client.reconnect();
    }
};

export default mqtt_sdk;
