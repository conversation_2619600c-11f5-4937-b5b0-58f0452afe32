import { getSign, upload, attrCallBack, localUpload } from "@/api/upload"
export async function $upload(e, onUploadProgress, executor) {
  const { isopen, uploadUrls } = window.yskjConfig
  const file = e.target.files[0]
  /* 重置value值、触发相同文件fileChange */
  e.target.value = ""
  if (!file) {
    return
  }
  let result = null
  const form = new FormData()
  if (isopen) {
    const { data } = await getSign({ oldFilename: file.name })
    form.append("name", file.name)
    form.append("key", data.key)
    form.append("policy", data.policy)
    form.append("OSSAccessKeyId", data.accessid)
    form.append("success_action_status", "200")
    form.append("signature", data.signature)
    form.append("file", file)
    const ossResult = await upload(uploadUrls, form, onUploadProgress, executor)
    if (!ossResult) {
      const params = {
        filename: data.key,
        mimeType: file.type,
        size: file.size,
        oldFilename: file.name,
      }
      try {
        const { data: fileInfo } = await attrCallBack(params)
        result = {
          fileUrl: fileInfo.fileCallVo.filepath,
          pdfUrl: fileInfo.pdfUrl,
          smallPath: fileInfo.smallPath,
          name: file.name,
          screenUrl: fileInfo.screenUrl,
        }
      } catch (e) {}
    }
  } else {
    form.append("file", file)
    const { data: fileInfo } = await localUpload(
      form,
      onUploadProgress,
      executor
    )
    result = {
      fileUrl: fileInfo.fileUrl,
      pdfUrl: fileInfo.pdfUrl,
      smallPath: fileInfo.smallPath,
      name: file.name,
      screenUrl: fileInfo.screenUrl,
    }
  }
  return result
}
