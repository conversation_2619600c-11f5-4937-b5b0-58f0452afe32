<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ControlBarTip 测试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .test-container {
            width: 300px;
            height: 400px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 16px rgba(0,0,0,0.1);
            padding: 20px;
            margin: 50px auto;
        }
        
        .control-panel {
            width: 230px;
            border-radius: 6px;
            padding: 32px 0 24px 0;
            background: #fff;
            box-shadow: 0px 2px 16px 1px rgba(0, 0, 0, 0.1);
            margin: 0 auto;
        }
        
        .top {
            margin: 0 auto;
            width: 180px;
            height: 180px;
            border-radius: 50%;
            background: #e4e7ed;
            overflow: hidden;
            position: relative;
            clip-path: circle(50% at 50% 50%);
        }
        
        .item {
            position: absolute;
            cursor: pointer;
        }
        
        .item:hover {
            background: #007aff;
        }
        
        .item:hover .arrow {
            border-color: #fff;
        }
        
        .item:hover .arrow::before {
            border-color: #fff;
            background: #fff;
        }
        
        .item:hover .arrow::after {
            border-color: #fff;
        }
        
        .arrow {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            pointer-events: none;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0;
            border-color: #909399;
        }
        
        /* 8个方向按钮的样式 */
        .direction-up {
            width: 90px;
            height: 45px;
            top: 0;
            left: 45px;
            border-bottom: 2px solid #fff;
        }
        
        .direction-up-right {
            width: 63.64px;
            height: 63.64px;
            top: 0;
            right: 0;
            border-left: 2px solid #fff;
            border-bottom: 2px solid #fff;
            transform: rotate(45deg);
            transform-origin: 0 100%;
        }
        
        .direction-right {
            width: 45px;
            height: 90px;
            top: 45px;
            right: 0;
            border-left: 2px solid #fff;
        }
        
        .direction-down-right {
            width: 63.64px;
            height: 63.64px;
            bottom: 0;
            right: 0;
            border-left: 2px solid #fff;
            border-top: 2px solid #fff;
            transform: rotate(-45deg);
            transform-origin: 0 0;
        }
        
        .direction-down {
            width: 90px;
            height: 45px;
            bottom: 0;
            left: 45px;
            border-top: 2px solid #fff;
        }
        
        .direction-down-left {
            width: 63.64px;
            height: 63.64px;
            bottom: 0;
            left: 0;
            border-right: 2px solid #fff;
            border-top: 2px solid #fff;
            transform: rotate(45deg);
            transform-origin: 100% 0;
        }
        
        .direction-left {
            width: 45px;
            height: 90px;
            top: 45px;
            left: 0;
            border-right: 2px solid #fff;
        }
        
        .direction-up-left {
            width: 63.64px;
            height: 63.64px;
            top: 0;
            left: 0;
            border-right: 2px solid #fff;
            border-bottom: 2px solid #fff;
            transform: rotate(-45deg);
            transform-origin: 100% 100%;
        }
        
        /* 箭头样式 */
        .arrow-up {
            border-width: 0 8px 12px 8px;
            border-color: transparent transparent #909399 transparent;
        }
        
        .arrow-up-right {
            width: 12px;
            height: 12px;
            border-width: 0;
            position: relative;
        }
        
        .arrow-up-right::before {
            content: '';
            position: absolute;
            width: 12px;
            height: 2px;
            background: #909399;
            transform: rotate(-45deg);
            top: 5px;
            left: 0;
        }
        
        .arrow-up-right::after {
            content: '';
            position: absolute;
            border-style: solid;
            border-width: 6px 6px 0 0;
            border-color: #909399 transparent transparent transparent;
            transform: rotate(0deg);
            top: -2px;
            right: -2px;
        }
        
        .arrow-right {
            border-width: 8px 0 8px 12px;
            border-color: transparent transparent transparent #909399;
        }
        
        .arrow-down-right {
            width: 12px;
            height: 12px;
            border-width: 0;
            position: relative;
        }
        
        .arrow-down-right::before {
            content: '';
            position: absolute;
            width: 12px;
            height: 2px;
            background: #909399;
            transform: rotate(45deg);
            top: 5px;
            left: 0;
        }
        
        .arrow-down-right::after {
            content: '';
            position: absolute;
            border-style: solid;
            border-width: 0 6px 6px 0;
            border-color: transparent #909399 transparent transparent;
            transform: rotate(0deg);
            bottom: -2px;
            right: -2px;
        }
        
        .arrow-down {
            border-width: 12px 8px 0 8px;
            border-color: #909399 transparent transparent transparent;
        }
        
        .arrow-down-left {
            width: 12px;
            height: 12px;
            border-width: 0;
            position: relative;
        }
        
        .arrow-down-left::before {
            content: '';
            position: absolute;
            width: 12px;
            height: 2px;
            background: #909399;
            transform: rotate(-45deg);
            top: 5px;
            left: 0;
        }
        
        .arrow-down-left::after {
            content: '';
            position: absolute;
            border-style: solid;
            border-width: 0 0 6px 6px;
            border-color: transparent transparent #909399 transparent;
            transform: rotate(0deg);
            bottom: -2px;
            left: -2px;
        }
        
        .arrow-left {
            border-width: 8px 12px 8px 0;
            border-color: transparent #909399 transparent transparent;
        }
        
        .arrow-up-left {
            width: 12px;
            height: 12px;
            border-width: 0;
            position: relative;
        }
        
        .arrow-up-left::before {
            content: '';
            position: absolute;
            width: 12px;
            height: 2px;
            background: #909399;
            transform: rotate(45deg);
            top: 5px;
            left: 0;
        }
        
        .arrow-up-left::after {
            content: '';
            position: absolute;
            border-style: solid;
            border-width: 6px 0 0 6px;
            border-color: #909399 transparent transparent transparent;
            transform: rotate(0deg);
            top: -2px;
            left: -2px;
        }
        
        .zz {
            position: absolute;
            left: 50%;
            top: 50%;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            width: 76px;
            height: 76px;
            border: 4px solid #fff;
            background: #e4e7ed;
            z-index: 9;
            overflow: hidden;
            clip-path: circle(50% at 50% 50%);
        }
        
        .mask {
            cursor: pointer;
            position: absolute;
            background: #e4e7ed;
            left: 0;
            top: 0;
            bottom: 0;
            right: 0;
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .mask:hover {
            background: rgba(76, 132, 255, 0.2);
        }
        
        .center-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #a9acb1;
        }
        
        .info {
            text-align: center;
            margin-top: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2 style="text-align: center; margin-bottom: 20px;">8方向控制面板测试</h2>
        
        <div class="control-panel">
            <div class="top">
                <!-- 上方向 -->
                <div data-role="up" class="direction-up item">
                    <div class="arrow arrow-up"></div>
                </div>
                <!-- 右上方向 -->
                <div data-role="up-right" class="direction-up-right item">
                    <div class="arrow arrow-up-right"></div>
                </div>
                <!-- 右方向 -->
                <div data-role="right" class="direction-right item">
                    <div class="arrow arrow-right"></div>
                </div>
                <!-- 右下方向 -->
                <div data-role="down-right" class="direction-down-right item">
                    <div class="arrow arrow-down-right"></div>
                </div>
                <!-- 下方向 -->
                <div data-role="down" class="direction-down item">
                    <div class="arrow arrow-down"></div>
                </div>
                <!-- 左下方向 -->
                <div data-role="down-left" class="direction-down-left item">
                    <div class="arrow arrow-down-left"></div>
                </div>
                <!-- 左方向 -->
                <div data-role="left" class="direction-left item">
                    <div class="arrow arrow-left"></div>
                </div>
                <!-- 左上方向 -->
                <div data-role="up-left" class="direction-up-left item">
                    <div class="arrow arrow-up-left"></div>
                </div>
                <!-- 中心停止按钮 -->
                <div data-role="zz" class="zz">
                    <div class="mask">
                        <div class="center-dot"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="info">
            <p>鼠标悬停在各个方向按钮上查看效果</p>
            <p>8个方向：上、右上、右、右下、下、左下、左、左上</p>
        </div>
    </div>
    
    <script>
        // 添加点击事件监听
        document.querySelectorAll('.item').forEach(item => {
            item.addEventListener('click', function() {
                const role = this.getAttribute('data-role');
                console.log('点击了方向:', role);
                alert('点击了方向: ' + role);
            });
        });
    </script>
</body>
</html>
